from django.urls import path
from . import views

urlpatterns = [
    path('logs/', views.audit_log_list, name='audit_log_list'),
    path('sql-logs/', views.sql_audit_log_list, name='sql_audit_log_list'),
    path('sql-logs/export/', views.export_sql_audit_logs, name='export_sql_audit_logs'),
    path('rules/', views.audit_rules, name='audit_rules'),
    path('rules/<int:pk>/', views.audit_rule_detail, name='audit_rule_detail'),
    path('results/<int:query_id>/', views.audit_result_list, name='audit_result_list'),
    path('check-sql/', views.check_sql_with_rules, name='check_sql_with_rules'),
] 