import pymysql

# 连接到MySQL数据库
conn = pymysql.connect(
    host='*************',
    port=4406,
    user='root',
    password='udream2000',
    database='baoleiji',
    charset='utf8mb4'
    
)

try:
    with conn.cursor() as cursor:
        # 禁用外键检查
        cursor.execute('SET FOREIGN_KEY_CHECKS = 0;')
        
        # 获取所有表名
        cursor.execute('SHOW TABLES;')
        tables = cursor.fetchall()
        
        # 删除所有表
        for table in tables:
            table_name = table[0]
            print(f"正在删除表: {table_name}")
            cursor.execute(f'DROP TABLE IF EXISTS `{table_name}`;')
        
        # 启用外键检查
        cursor.execute('SET FOREIGN_KEY_CHECKS = 1;')
        
        # 提交事务
        conn.commit()
        
        print("所有表已成功删除！")
finally:
    conn.close() 