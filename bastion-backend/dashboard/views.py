from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.db.models import Count, F, Q
from django.utils import timezone
from datetime import timedelta, datetime
from django.contrib.auth.models import User
from datasource.models import Datasource, DatabaseType
from query.models import Query
from audit.models import AuditLog, SQLAuditResult

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_statistics(request):
    """获取仪表盘统计数据"""
    # 获取请求参数
    period = request.query_params.get('period', 'week')
    start_date_str = request.query_params.get('start_date')
    end_date_str = request.query_params.get('end_date')
    
    # 确定日期范围
    today = timezone.now().date()
    
    if period == 'custom' and start_date_str and end_date_str:
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            # 日期格式错误，使用默认的周期
            start_date = today - timedelta(days=7)
            end_date = today
    elif period == 'month':
        # 本月
        start_date = today.replace(day=1)
        end_date = today
    else:
        # 默认为本周
        start_date = today - timedelta(days=today.weekday())
        end_date = today
    
    # 上一个周期的日期范围
    period_length = (end_date - start_date).days + 1
    previous_end_date = start_date - timedelta(days=1)
    previous_start_date = previous_end_date - timedelta(days=period_length - 1)
    
    # 获取数据库总数统计
    db_count = Datasource.objects.count()
    
    # 获取上一周期的数据库总数，计算趋势
    db_count_last_period = Datasource.objects.filter(created_at__lt=start_date).count()
    db_trend = 0
    if db_count_last_period > 0:
        db_trend = round((db_count - db_count_last_period) / db_count_last_period * 100, 1)
    
    # 获取查询总数统计
    query_count = Query.objects.filter(
        start_time__date__gte=start_date,
        start_time__date__lte=end_date
    ).count()
    
    # 获取上一周期的查询总数，计算趋势
    query_count_last_period = Query.objects.filter(
        start_time__date__gte=previous_start_date,
        start_time__date__lte=previous_end_date
    ).count()
    query_trend = 0
    if query_count_last_period > 0:
        query_trend = round((query_count - query_count_last_period) / query_count_last_period * 100, 1)
    
    # 获取活跃用户数
    active_users_count = User.objects.filter(
        queries__start_time__date__gte=start_date,
        queries__start_time__date__lte=end_date
    ).distinct().count()
    
    # 获取上一周期活跃用户数，计算趋势
    active_users_last_period = User.objects.filter(
        queries__start_time__date__gte=previous_start_date,
        queries__start_time__date__lte=previous_end_date
    ).distinct().count()
    user_trend = 0
    if active_users_last_period > 0:
        user_trend = round((active_users_count - active_users_last_period) / active_users_last_period * 100, 1)
    
    # 获取审计日志总数
    audit_count = AuditLog.objects.filter(
        action_time__date__gte=start_date,
        action_time__date__lte=end_date
    ).count()
    
    # 获取上一周期的审计日志总数，计算趋势
    audit_count_last_period = AuditLog.objects.filter(
        action_time__date__gte=previous_start_date,
        action_time__date__lte=previous_end_date
    ).count()
    audit_trend = 0
    if audit_count_last_period > 0:
        audit_trend = round((audit_count - audit_count_last_period) / audit_count_last_period * 100, 1)
    
    # 获取最近查询记录
    recent_queries = Query.objects.all().order_by('-start_time')[:4]
    recent_queries_data = []
    for query in recent_queries:
        recent_queries_data.append({
            'database': query.datasource.name,
            'query': query.sql_content[:100] + ('...' if len(query.sql_content) > 100 else ''),
            'time': query.start_time.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    # 获取最近审计日志
    recent_audits = AuditLog.objects.all().order_by('-action_time')[:4]
    recent_audits_data = []
    for audit in recent_audits:
        recent_audits_data.append({
            'user': audit.user.username,
            'operation': audit.action_type,
            'detail': audit.content[:100] + ('...' if len(audit.content) > 100 else ''),
            'time': audit.action_time.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    # 获取数据库类型分布
    db_types = DatabaseType.objects.annotate(
        count=Count('datasource')
    ).values('name', 'count')
    
    db_type_distribution = [{'name': item['name'], 'value': item['count']} for item in db_types]
    
    # 获取日期范围内的每日查询统计数据
    date_range = []
    current_date = start_date
    while current_date <= end_date:
        date_range.append(current_date)
        current_date += timedelta(days=1)
    
    # 初始化日期数据
    date_data = {date.strftime('%Y-%m-%d'): 0 for date in date_range}
    
    # 查询每天的查询数量
    daily_queries = Query.objects.filter(
        start_time__date__gte=start_date,
        start_time__date__lte=end_date
    ).extra({'date': "date(start_time)"}).values('date').annotate(count=Count('id'))
    
    # 填充查询数量
    for item in daily_queries:
        date_str = item['date'].strftime('%Y-%m-%d')
        if date_str in date_data:
            date_data[date_str] = item['count']
    
    # 构造图表数据
    query_chart_data = {
        'dates': list(date_data.keys()),
        'counts': list(date_data.values())
    }
    
    # 获取用户查询次数统计
    user_query_stats = Query.objects.filter(
        start_time__date__gte=start_date,
        start_time__date__lte=end_date
    ).values('user__username').annotate(
        query_count=Count('id')
    ).order_by('-query_count')[:10]  # 获取查询次数最多的前10名用户
    
    user_query_data = []
    for item in user_query_stats:
        user_query_data.append({
            'name': item['user__username'],
            'value': item['query_count']
        })
    
    # 构造响应数据
    response_data = {
        'statistics': {
            'databaseCount': db_count,
            'databaseTrend': db_trend,
            'queryCount': query_count,
            'queryTrend': query_trend,
            'activeUsers': active_users_count,
            'userTrend': user_trend,
            'auditCount': audit_count,
            'auditTrend': audit_trend
        },
        'recentQueries': recent_queries_data,
        'recentAudits': recent_audits_data,
        'dbTypeDistribution': db_type_distribution,
        'queryChartData': query_chart_data,
        'userQueryStats': user_query_data,
        'dateRange': {
            'start': start_date.strftime('%Y-%m-%d'),
            'end': end_date.strftime('%Y-%m-%d')
        }
    }
    
    return Response(response_data) 