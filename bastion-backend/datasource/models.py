from django.db import models
from django.contrib.auth.models import User
from authentication.models import Role, UserProfile

class DatabaseType(models.Model):
    """
    数据库类型模型
    """
    name = models.CharField(max_length=50, unique=True, verbose_name="数据库类型名称")
    code = models.CharField(max_length=50, unique=True, verbose_name="类型代码")
    icon = models.CharField(max_length=100, blank=True, null=True, verbose_name="图标")
    description = models.TextField(blank=True, null=True, verbose_name="描述")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "数据库类型"
        verbose_name_plural = "数据库类型"

    def __str__(self):
        return self.name

class Datasource(models.Model):
    """
    数据源模型
    """
    STATUS_CHOICES = (
        ('active', '活跃'),
        ('inactive', '不活跃'),
        ('testing', '测试中'),
    )
    
    name = models.CharField(max_length=100, verbose_name="数据源名称")
    db_type = models.ForeignKey(DatabaseType, on_delete=models.CASCADE, verbose_name="数据库类型")
    host = models.CharField(max_length=255, verbose_name="主机地址")
    port = models.IntegerField(verbose_name="端口")
    username = models.CharField(max_length=100, verbose_name="用户名")
    password = models.CharField(max_length=255, verbose_name="密码")
    database = models.CharField(max_length=100, blank=True, null=True, verbose_name="数据库名")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='inactive', verbose_name="状态")
    description = models.TextField(blank=True, null=True, verbose_name="描述")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name="created_datasources", verbose_name="创建人")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "数据源"
        verbose_name_plural = "数据源"
        unique_together = ('name', 'host', 'port')
        
    def __str__(self):
        db_str = f"/{self.database}" if self.database else ""
        return f"{self.name} ({self.host}:{self.port}{db_str})"

class DatasourcePermission(models.Model):
    """
    数据源权限模型
    """
    PERMISSION_TYPES = (
        ('read', '只读'),
        ('write', '读写'),
        ('admin', '管理员'),
    )
    
    datasource = models.ForeignKey(Datasource, on_delete=models.CASCADE, related_name="permissions", verbose_name="数据源")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="datasource_permissions", verbose_name="用户")
    permission_type = models.CharField(max_length=20, choices=PERMISSION_TYPES, default='read', verbose_name="权限类型")
    granted_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name="granted_permissions", verbose_name="授权人")
    granted_at = models.DateTimeField(auto_now_add=True, verbose_name="授权时间")
    
    class Meta:
        verbose_name = "数据源权限"
        verbose_name_plural = "数据源权限"
        unique_together = ('datasource', 'user')
        
    def __str__(self):
        return f"{self.user.username} - {self.datasource.name} - {self.permission_type}"

class TablePermission(models.Model):
    """
    表级权限模型，控制到表粒度的访问权限
    """
    PERMISSION_TYPES = (
        ('read', '只读'),
        ('write', '读写'),
        ('denied', '禁止访问'),
    )
    
    datasource = models.ForeignKey(Datasource, on_delete=models.CASCADE, related_name="table_permissions", verbose_name="数据源")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="table_permissions", verbose_name="用户")
    schema_name = models.CharField(max_length=100, blank=True, null=True, verbose_name="Schema名称")
    table_name = models.CharField(max_length=100, verbose_name="表名称")
    permission_type = models.CharField(max_length=20, choices=PERMISSION_TYPES, default='read', verbose_name="权限类型")
    granted_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name="granted_table_permissions", verbose_name="授权人")
    granted_at = models.DateTimeField(auto_now_add=True, verbose_name="授权时间")
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name="过期时间")
    description = models.TextField(blank=True, null=True, verbose_name="说明")
    
    class Meta:
        verbose_name = "表权限"
        verbose_name_plural = "表权限"
        unique_together = ('datasource', 'user', 'schema_name', 'table_name')
        
    def __str__(self):
        schema_prefix = f"{self.schema_name}." if self.schema_name else ""
        return f"{self.user.username} - {self.datasource.name} - {schema_prefix}{self.table_name} - {self.permission_type}"
