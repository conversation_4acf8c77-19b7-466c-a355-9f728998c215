from django.shortcuts import render, get_object_or_404
from django.contrib.auth.models import User
from django.db import transaction
from django.db.models import Q
from django.utils import timezone
from django.conf import settings
from django.core.cache import cache
from django.http import JsonResponse, Http404
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
import json
import pymysql
import time
from rest_framework import serializers
from .models import DatabaseType, Datasource, DatasourcePermission, TablePermission
from .connection_pool import connection_pool
import threading
import functools
import hashlib
from django.core.cache import cache
from django.http import JsonResponse
from django.db.models import Q
import logging
from rest_framework import viewsets, permissions
from rest_framework.decorators import action
from .serializers import DatabaseTypeSerializer, DatasourceSerializer, DatasourcePermissionSerializer, TablePermissionSerializer
from audit.models import AuditLog, SQLAuditRule, SQLAuditResult
from datetime import timedelta

# 获取logger
logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def database_types(request):
    """获取所有数据库类型"""
    db_types = DatabaseType.objects.all()
    result = []
    
    for db_type in db_types:
        result.append({
            'id': db_type.id,
            'name': db_type.name,
            'code': db_type.code,
            'icon': db_type.icon
        })
    
    return Response(result)

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def datasource_list(request):
    """获取数据源列表或创建新数据源"""
    if request.method == 'GET':
        # 获取用户可访问的数据源
        user = request.user
        
        if user.is_superuser:
            # 管理员可以访问所有数据源
            datasources = Datasource.objects.all()
        else:
            # 普通用户只能访问被授权的数据源
            permissions = DatasourcePermission.objects.filter(user=user)
            datasource_ids = [perm.datasource_id for perm in permissions]
            datasources = Datasource.objects.filter(id__in=datasource_ids)
        
        # 序列化数据
        serializer = DatasourceSerializer(datasources, many=True)
        return Response(serializer.data)
    
    elif request.method == 'POST':
        # 创建新数据源
        # 使用request.data而不是json.loads(request.body)
        is_temporary = request.data.get('is_temporary', False)
        
        # 打印请求数据用于调试
        print(f"创建数据源请求数据: {request.data}")
        
        # 序列化与验证
        serializer = DatasourceSerializer(data=request.data)
        if serializer.is_valid():
            # 设置创建者信息
            datasource = serializer.save(created_by=request.user, status='inactive')
            
            # 自动为创建者授予所有权限
            DatasourcePermission.objects.create(
                user=request.user,
                datasource=datasource,
                permission_type='admin',
                granted_by=request.user
            )
            
            # 记录审计日志
            AuditLog.objects.create(
                user=request.user,
                action_type='datasource_add',
                ip_address=request.META.get('REMOTE_ADDR'),
                datasource=datasource,
                content=f"用户 {request.user.username} 创建了数据源 {datasource.name}"
            )
            
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        # 打印验证错误用于调试
        print(f"数据验证错误: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def datasource_detail(request, pk):
    """获取、更新或删除数据源"""
    try:
        datasource = Datasource.objects.get(pk=pk)
        
        # 检查用户是否有权限操作该数据源
        user = request.user
        if not (user.is_superuser or user.is_staff):
            try:
                permission = DatasourcePermission.objects.get(datasource=datasource, user=user)
                # 如果是读写或管理员权限，则可以执行操作
                if request.method != 'GET' and permission.permission_type == 'read':
                    return Response({
                        'success': False,
                        'message': '您没有权限执行此操作'
                    }, status=status.HTTP_403_FORBIDDEN)
            except DatasourcePermission.DoesNotExist:
                return Response({
                    'success': False,
                    'message': '您没有权限访问此数据源'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # 获取数据源详情
        if request.method == 'GET':
            return Response({
                'id': datasource.id,
                'name': datasource.name,
                'db_type': {
                    'id': datasource.db_type.id,
                    'name': datasource.db_type.name,
                    'code': datasource.db_type.code,
                    'icon': datasource.db_type.icon,
                },
                'host': datasource.host,
                'port': datasource.port,
                'username': datasource.username,
                # 'database': datasource.database,
                'status': datasource.status,
                'description': datasource.description,
                'created_by': datasource.created_by.username if datasource.created_by else None,
                'created_at': datasource.created_at,
                'updated_at': datasource.updated_at,
            })
        
        # 更新数据源
        elif request.method == 'PUT':
            data = request.data
            
            with transaction.atomic():
                # 更新数据源信息
                if 'name' in data:
                    datasource.name = data['name']
                if 'db_type_id' in data:
                    try:
                        db_type = DatabaseType.objects.get(pk=data['db_type_id'])
                        datasource.db_type = db_type
                    except DatabaseType.DoesNotExist:
                        return Response({
                            'success': False,
                            'message': '数据库类型不存在'
                        }, status=status.HTTP_400_BAD_REQUEST)
                if 'host' in data:
                    datasource.host = data['host']
                if 'port' in data:
                    datasource.port = data['port']
                if 'username' in data:
                    datasource.username = data['username']
                if 'password' in data and data['password']:  # 只有密码不为空时才更新
                    datasource.password = data['password']
                # if 'database' in data:
                #     datasource.database = data['database']
                if 'status' in data:
                    datasource.status = data['status']
                if 'description' in data:
                    datasource.description = data['description']
                
                datasource.save()
                
                # 记录审计日志
                AuditLog.objects.create(
                    user=request.user,
                    action_type='datasource_edit',
                    ip_address=request.META.get('REMOTE_ADDR'),
                    datasource=datasource,
                    content=f"用户 {request.user.username} 修改了数据源 {datasource.name} 的信息"
                )
            
            return Response({
                'success': True,
                'message': '数据源更新成功'
            })
        
        # 删除数据源
        elif request.method == 'DELETE':
            datasource_name = datasource.name
            # 记录审计日志
            AuditLog.objects.create(
                user=request.user,
                action_type='datasource_delete',
                ip_address=request.META.get('REMOTE_ADDR'),
                content=f"用户 {request.user.username} 删除了数据源 {datasource_name}"
            )
            
            datasource.delete()
            return Response({
                'success': True,
                'message': '数据源删除成功'
            })
        
    except Datasource.DoesNotExist:
        return Response({
            'success': False,
            'message': '数据源不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'message': f'操作失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def test_connection(request, pk=None):
    """测试数据库连接"""
    try:
        if pk:
            # 测试已有数据源
            datasource = Datasource.objects.get(pk=pk)
            
            # 检查权限
            if not request.user.is_superuser and not DatasourcePermission.objects.filter(
                user=request.user, datasource=datasource, permission_type__in=['read', 'write', 'admin']
            ).exists():
                return Response({
                    'success': False,
                    'message': '没有权限连接此数据源'
                }, status=status.HTTP_403_FORBIDDEN)
        else:
            # 测试未保存的数据源
            try:
                # 使用request.data代替json.loads(request.body)
                # 兼容两种参数格式：db_type 和 db_type_id
                print(f"测试连接请求数据: {request.data}")
                
                db_type_id = request.data.get('db_type_id')
                if db_type_id is None:
                    # 尝试从db_type获取，可能是整数或对象
                    db_type = request.data.get('db_type')
                    if isinstance(db_type, dict):
                        db_type_id = db_type.get('id')
                    elif isinstance(db_type, int) or (isinstance(db_type, str) and db_type.isdigit()):
                        # 如果db_type是整数或数字字符串，直接使用
                        db_type_id = int(db_type) if isinstance(db_type, str) else db_type
                    else:
                        # 如果类型是字符串，尝试通过名称查找
                        try:
                            db_type_obj = DatabaseType.objects.get(name=db_type)
                            db_type_id = db_type_obj.id
                        except DatabaseType.DoesNotExist:
                            # 使用默认ID
                            db_type_id = 1
                
                # 获取数据库类型
                db_type = DatabaseType.objects.get(pk=db_type_id)
                print(f"找到数据库类型: id={db_type.id}, name={db_type.name}, code={db_type.code}")
                
                # 创建临时数据源对象
                datasource = Datasource(
                    name=request.data.get('name', '临时连接'),
                    db_type=db_type,
                    host=request.data.get('host'),
                    port=int(request.data.get('port')),
                    username=request.data.get('username'),
                    password=request.data.get('password'),
                    database=request.data.get('database', '')
                )
            except (TypeError, ValueError) as e:
                return Response({
                    'success': False,
                    'message': f'数据格式不正确: {str(e)}'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        # 测试连接
        try:
            # 根据数据库类型进行不同的连接测试
            if datasource.db_type.code.lower() == 'mysql':
                try:
                    conn_kwargs = {
                        'host': datasource.host,
                        'port': int(datasource.port),
                        'user': datasource.username,
                        'password': datasource.password,
                        'connect_timeout': 5
                    }
                    
                    # 如果有数据库名，添加到连接参数中
                    if datasource.database:
                        conn_kwargs['database'] = datasource.database
                    
                    conn = pymysql.connect(**conn_kwargs)
                    cursor = conn.cursor()
                    cursor.execute('SELECT VERSION()')
                    version = cursor.fetchone()[0]
                    cursor.close()
                    conn.close()
                    
                    return Response({
                        'success': True,
                        'message': f'连接成功，MySQL版本: {version}'
                    })
                except pymysql.err.OperationalError as e:
                    error_code, error_message = e.args
                    return Response({
                        'success': False,
                        'message': f'MySQL连接错误: {error_message}',
                        'error_code': error_code
                    }, status=status.HTTP_400_BAD_REQUEST)
                except Exception as e:
                    return Response({
                        'success': False,
                        'message': f'MySQL连接失败: {str(e)}'
                    }, status=status.HTTP_400_BAD_REQUEST)
            elif datasource.db_type.code.lower() == 'clickhouse':
                try:
                    import clickhouse_driver
                    
                    # 记录连接信息
                    logger.info(f"尝试连接ClickHouse: {datasource.host}:{datasource.port}, 用户: {datasource.username}, 数据库: {datasource.database}")
                    
                    # 基本连接参数
                    conn_kwargs = {
                        'host': datasource.host,
                        'port': int(datasource.port),
                        'user': datasource.username,
                        'password': datasource.password,
                        'connect_timeout': 5,
                        'secure': False  # 默认不使用SSL
                    }
                    
                    # 如果有数据库名，添加到连接参数中
                    if datasource.database:
                        conn_kwargs['database'] = datasource.database
                    
                    # 尝试不同的设置组合
                    connection_success = False
                    error_message = ""
                    
                    # 1. 尝试标准连接
                    try:
                        logger.info("尝试标准ClickHouse连接")
                        client = clickhouse_driver.Client(**conn_kwargs)
                        version = client.execute('SELECT version()')[0][0]
                        connection_success = True
                        success_message = f'连接成功，ClickHouse版本: {version}'
                    except Exception as e:
                        error_message = f'标准连接失败: {str(e)}'
                        logger.warning(error_message)
                    
                    # 2. 如果标准连接失败，尝试启用SSL
                    if not connection_success:
                        try:
                            logger.info("尝试启用SSL的ClickHouse连接")
                            conn_kwargs['secure'] = True
                            conn_kwargs['verify'] = False  # 不验证证书
                            client = clickhouse_driver.Client(**conn_kwargs)
                            version = client.execute('SELECT version()')[0][0]
                            connection_success = True
                            success_message = f'SSL连接成功，ClickHouse版本: {version}'
                        except Exception as e:
                            error_message += f'\nSSL连接失败: {str(e)}'
                            logger.warning(f"SSL连接失败: {str(e)}")
                    
                    # 3. 尝试使用HTTP协议
                    if not connection_success:
                        try:
                            logger.info("尝试HTTP协议连接")
                            import requests
                            
                            # 构建HTTP连接URL - 使用用户提供的端口号
                            http_url = f"http://{datasource.host}:{datasource.port}"  # 使用用户输入的端口
                            auth = (datasource.username, datasource.password)
                            
                            # 执行简单查询
                            query = "SELECT version()"
                            if datasource.database:
                                query = f"USE {datasource.database}; {query}"
                                
                            response = requests.post(http_url, data=query, auth=auth, timeout=5)
                            
                            if response.status_code == 200:
                                version = response.text.strip()
                                connection_success = True
                                success_message = f'HTTP连接成功，ClickHouse版本: {version}'
                            else:
                                error_message += f'\nHTTP连接失败: 状态码 {response.status_code}, 响应: {response.text}'
                        except Exception as e:
                            error_message += f'\nHTTP连接失败: {str(e)}'
                            logger.warning(f"HTTP连接失败: {str(e)}")
                    
                    # 返回结果
                    if connection_success:
                        logger.info(f"ClickHouse连接成功: {success_message}")
                        return Response({
                            'success': True,
                            'message': success_message
                        })
                    else:
                        logger.error(f"所有ClickHouse连接尝试均失败: {error_message}")
                        return Response({
                            'success': False,
                            'message': f'ClickHouse连接失败: {error_message}\n\n连接提示: 请检查以下几点:\n1. 确认主机地址和端口是否正确\n2. 确认ClickHouse服务是否运行\n3. 检查是否需要使用SSL连接\n4. 可能需要在ClickHouse服务器上允许远程连接\n5. 检查防火墙设置是否允许连接'
                        }, status=status.HTTP_400_BAD_REQUEST)
                except Exception as e:
                    logger.error(f"ClickHouse连接过程中出现意外错误: {str(e)}")
                    return Response({
                        'success': False,
                        'message': f'ClickHouse连接失败: {str(e)}'
                    }, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({
                    'success': False,
                    'message': f'不支持的数据库类型: {datasource.db_type.name} (代码: {datasource.db_type.code})'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 连接成功，记录日志
            # if pk:
                # AuditLog.objects.create(
                #     user=request.user,
                #     action_type='test_connection',
                #     ip_address=request.META.get('REMOTE_ADDR'),
                #     datasource=datasource,
                #     content=f"用户 {request.user.username} 测试连接数据源 {datasource.name} 成功"
                # )
            
            return Response({
                'success': True,
                'message': '连接成功'
            })
        except Exception as e:
            return Response({
                'success': False,
                'message': f'连接测试失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
    except DatabaseType.DoesNotExist:
        return Response({
            'success': False,
            'message': '数据库类型不存在'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Datasource.DoesNotExist:
        return Response({
            'success': False,
            'message': '数据源不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'message': f'连接测试失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def datasource_permission_list(request):
    """获取或添加数据源权限"""
    if request.method == 'GET':
        # 获取数据源权限列表
        user = request.user
        if user.is_superuser:
            # 管理员可以看到所有权限
            permissions = DatasourcePermission.objects.all()
        else:
            # 普通用户只能看到自己授予的权限
            permissions = DatasourcePermission.objects.filter(granted_by=user)
        
        # 分页
        paginator = PageNumberPagination()
        paginator.page_size = 10
        permissions = paginator.paginate_queryset(permissions, request)
        
        result = []
        for perm in permissions:
            result.append({
                'id': perm.id,
                'datasource': {
                    'id': perm.datasource.id,
                    'name': perm.datasource.name
                },
                'user': {
                    'id': perm.user.id,
                    'username': perm.user.username
                },
                'permission_type': perm.permission_type,
                'granted_by': perm.granted_by.username if perm.granted_by else None,
                'granted_at': perm.granted_at
            })
        
        return paginator.get_paginated_response(result)
    
    elif request.method == 'POST':
        # 添加数据源权限
        # 使用request.data代替json.loads(request.body)
        datasource_id = request.data.get('datasource_id')
        user_id = request.data.get('user_id')
        permission_type = request.data.get('permission_type')
        
        try:
            # 获取数据源和用户
            datasource = Datasource.objects.get(pk=datasource_id)
            user_to_grant = User.objects.get(pk=user_id)
            
            # 检查授权者权限
            current_user = request.user
            if not current_user.is_superuser and not DatasourcePermission.objects.filter(
                user=current_user, datasource=datasource, permission_type='admin'
            ).exists():
                return Response({
                    'success': False,
                    'message': '没有权限授予此数据源的访问权限'
                }, status=status.HTTP_403_FORBIDDEN)
            
            # 检查是否已有权限
            if DatasourcePermission.objects.filter(datasource=datasource, user=user_to_grant).exists():
                return Response({
                    'success': False,
                    'message': '用户已有此数据源的访问权限'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 创建权限
            permission = DatasourcePermission.objects.create(
                datasource=datasource,
                user=user_to_grant,
                permission_type=permission_type,
                granted_by=current_user
            )
            
            # 记录审计日志
            # AuditLog.objects.create(
            #     user=current_user,
            #     action_type='grant_permission',
            #     ip_address=request.META.get('REMOTE_ADDR'),
            #     datasource=datasource,
            #     content=f"用户 {current_user.username} 授予 {user_to_grant.username} 数据源 {datasource.name} 的 {permission_type} 权限"
            # )
            
            return Response({
                'success': True,
                'message': '权限授予成功',
                'id': permission.id
            })
            
        except Datasource.DoesNotExist:
            return Response({
                'success': False,
                'message': '数据源不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except User.DoesNotExist:
            return Response({
                'success': False,
                'message': '用户不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'权限授予失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def datasource_permission_detail(request, pk):
    """获取、更新或删除数据源权限"""
    try:
        permission = DatasourcePermission.objects.get(pk=pk)
        
        # 检查用户是否有权限操作
        current_user = request.user
        if not (current_user.is_superuser or current_user.is_staff):
            try:
                user_permission = DatasourcePermission.objects.get(
                    datasource=permission.datasource,
                    user=current_user
                )
                if user_permission.permission_type != 'admin':
                    return Response({
                        'success': False,
                        'message': '您没有权限执行此操作'
                    }, status=status.HTTP_403_FORBIDDEN)
            except DatasourcePermission.DoesNotExist:
                return Response({
                    'success': False,
                    'message': '您没有权限执行此操作'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # 获取权限详情
        if request.method == 'GET':
            return Response({
                'id': permission.id,
                'datasource': {
                    'id': permission.datasource.id,
                    'name': permission.datasource.name,
                },
                'user': {
                    'id': permission.user.id,
                    'username': permission.user.username,
                },
                'permission_type': permission.permission_type,
                'granted_by': permission.granted_by.username if permission.granted_by else None,
                'granted_at': permission.granted_at,
            })
        
        # 更新权限
        elif request.method == 'PUT':
            data = request.data
            permission_type = data.get('permission_type')
            
            permission.permission_type = permission_type
            permission.granted_by = current_user
            permission.save()
            
            # 记录审计日志
            # AuditLog.objects.create(
            #     user=current_user,
            #     action_type='permission_grant',
            #     ip_address=request.META.get('REMOTE_ADDR'),
            #     datasource=permission.datasource,
            #     content=f"用户 {current_user.username} 更新了用户 {permission.user.username} 对数据源 {permission.datasource.name} 的权限为 {permission_type}"
            # )
            
            return Response({
                'success': True,
                'message': '数据源权限更新成功'
            })
        
        # 删除权限
        elif request.method == 'DELETE':
            datasource = permission.datasource
            user = permission.user
            
            # 记录审计日志
            # AuditLog.objects.create(
            #     user=current_user,
            #     action_type='permission_revoke',
            #     ip_address=request.META.get('REMOTE_ADDR'),
            #     datasource=datasource,
            #     content=f"用户 {current_user.username} 撤销了用户 {user.username} 对数据源 {datasource.name} 的权限"
            # )
            
            with transaction.atomic():
                # 删除相关的表级权限
                TablePermission.objects.filter(
                    datasource=permission.datasource,
                    user=permission.user
                ).delete()
                
                # 删除数据源权限
                permission.delete()
                
                # 记录审计日志
                AuditLog.objects.create(
                    user=request.user,
                    action_type='permission_delete',
                    ip_address=request.META.get('REMOTE_ADDR'),
                    datasource=permission.datasource,
                    content=f"用户 {request.user.username} 删除了 {permission.user.username} 对数据源 {permission.datasource.name} 的权限"
                )
            
            return Response({
                'success': True,
                'message': '数据源权限删除成功'
            })
        
    except DatasourcePermission.DoesNotExist:
        return Response({
            'success': False,
            'message': '权限不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'message': f'操作失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



# 模式数据缓存
_schema_cache = {}
_schema_cache_lock = threading.Lock()

# 添加缓存装饰器
def with_schema_cache(timeout=300):
    """
    一个装饰器，用于缓存schema信息。
    它现在会检查请求中是否有 'no_cache' 参数来决定是否绕过缓存。
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(request, pk, schema_name=None):
            # 从请求中检查 'no_cache' 参数
            no_cache = request.GET.get('no_cache', 'false').lower() in ['true', '1']
            
            # 使用Django的缓存框架
            cache_key = f"schema_cache_{pk}_{schema_name if schema_name else 'default'}"
            
            if not no_cache:
                cached_data = cache.get(cache_key)
                if cached_data:
                    logger.info(f"从缓存中获取schema: {cache_key}")
                    # 在返回的数据中添加一个标志，表示数据来自缓存
                    response_data = cached_data
                    response_data['from_cache'] = True
                    return JsonResponse(response_data)

            # 如果需要刷新缓存或缓存中没有数据，则执行函数
            logger.info(f"缓存未命中或需要刷新，正在获取schema: {cache_key}")
            
            # 这里我们假设被装饰的函数现在返回一个字典或可以被JsonResponse序列化的数据
            # 并且它不再需要自己处理JsonResponse
            result_data = func(request, pk, schema_name)
            
            # 确保结果是可序列化的
            if isinstance(result_data, Response):
                 # 如果是DRF的Response对象，提取其数据
                 actual_data = result_data.data
            else:
                 actual_data = result_data

            if actual_data and actual_data.get('success', True):
                cache.set(cache_key, actual_data, timeout=timeout)
                logger.info(f"Schema已缓存: {cache_key}")
                actual_data['from_cache'] = False
            
            return JsonResponse(actual_data)
        return wrapper
    return decorator

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
@with_schema_cache(timeout=settings.SCHEMA_CACHE_TIMEOUT)
def get_schema(request, pk, schema_name=None):
    """
    获取指定数据源和schema下的所有表和视图。
    这个视图现在被 with_schema_cache 装饰器包裹，该装饰器处理缓存逻辑。
    schema_name 参数现在是可选的。
    """
    datasource = get_object_or_404(Datasource, pk=pk)
    
    # 权限检查
    user = request.user
    if not (user.is_superuser or user.is_staff):
        try:
            DatasourcePermission.objects.get(
                user=user,
                datasource=datasource
            )
        except DatasourcePermission.DoesNotExist:
            return Response({"success": False, "message": "您暂时没有访问此数据源的权限，如需使用，请联系系统管理员申请"}, status=status.HTTP_403_FORBIDDEN)

    def fetch_schema():
        """实际获取schema信息的逻辑"""
        adapter = get_datasource_adapter(datasource)
        if schema_name is None:
            # 如果没有提供 schema_name，返回一个空的结构
            return {
                "tables": [],
                "views": [],
                "message": "请选择一个数据库模式"
            }
        schema_info = adapter.get_schema(schema_name)
        
        # 过滤掉用户没有权限的表
        if not user.is_superuser and not user.is_staff:
            filtered_tables = []
            for table in schema_info.get("tables", []):
                if check_table_permission(user, pk, table["name"], schema_name, "read"):
                    filtered_tables.append(table)
            schema_info["tables"] = filtered_tables
            
            filtered_views = []
            for view in schema_info.get("views", []):
                if check_table_permission(user, pk, view["name"], schema_name, "read"):
                    filtered_views.append(view)
            schema_info["views"] = filtered_views
            
        return schema_info

    # 这里的try-except块主要用于捕获数据库连接或查询中的错误
    try:
        schema_info = fetch_schema()
        
        # 返回数据，让装饰器来处理JsonResponse和缓存
        return {
            "success": True,
            "schema": schema_info
        }
    except Exception as e:
        logger.error(f"获取数据源 {pk} 的schema '{schema_name}' 失败: {e}", exc_info=True)
        # 返回错误信息，也让装饰器来处理
        return {
            "success": False,
            "message": f"获取schema信息失败: {str(e)}"
        }

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_schemas(request, pk):
    """获取数据库所有模式列表"""
    try:
        # 记录请求信息
        logger.info(f"开始获取数据源ID:{pk}的模式列表")
        
        # 获取数据源
        datasource = Datasource.objects.get(pk=pk)
        logger.info(f"找到数据源: {datasource.name}, 类型: {datasource.db_type.name if datasource.db_type else 'Unknown'}")
        
        # 检查权限
        if not request.user.is_superuser and not DatasourcePermission.objects.filter(
            user=request.user, datasource=datasource
        ).filter(Q(permission_type='admin') | Q(permission_type='read') | Q(permission_type='write')).exists():
            logger.warning(f"用户 {request.user.username} 没有权限查看数据源 {datasource.name} 的模式")
            return Response({
                'success': False,
                'message': '很抱歉，您暂时没有查看此数据源模式的权限，请联系系统管理员申请相应权限'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # 获取适配器并获取模式列表
        logger.info(f"获取 {datasource.db_type.name if datasource.db_type else 'Unknown'} 类型适配器")
        from .adapters import get_adapter
        try:
            adapter = get_adapter(datasource)
            logger.info(f"成功创建适配器: {adapter.__class__.__name__}")
        except Exception as adapter_error:
            logger.error(f"创建适配器失败: {adapter_error}")
            raise
        
        try:
            logger.info(f"开始获取模式列表")
            schemas = adapter.get_schemas()
            logger.info(f"成功获取 {len(schemas)} 个模式")
        except Exception as schema_error:
            logger.error(f"获取模式列表失败: {schema_error}")
            raise
        
        # 记录审计日志
        # AuditLog.objects.create(
        #     user=request.user,
        #     action_type='get_schemas',
        #     ip_address=request.META.get('REMOTE_ADDR'),
        #     datasource=datasource,
        #     content=f"用户 {request.user.username} 获取数据源 {datasource.name} 的模式列表"
        # )
        
        return Response({
            'success': True,
            'schemas': schemas
        })
        
    except Datasource.DoesNotExist:
        logger.error(f"数据源不存在: {pk}")
        return Response({
            'success': False,
            'message': '数据源不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"获取数据库模式列表失败: {e}")
        if hasattr(e, '__dict__'):
            logger.error(f"错误详情: {e.__dict__}")
        return Response({
            'success': False,
            'message': f'获取数据库模式列表失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_pool_status(request, datasource_id=None):
    """获取指定数据源或所有数据源的连接池状态"""
    try:
        if datasource_id:
            # 检查数据源是否存在
            try:
                datasource = Datasource.objects.get(id=datasource_id)
            except Datasource.DoesNotExist:
                return Response(
                    {"error": f"数据源ID {datasource_id} 不存在"},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # 获取指定数据源的连接池状态
            pool_status = connection_pool.get_pool_status(datasource_id)
            return Response({
                "datasource_id": datasource_id,
                "datasource_name": datasource.name,
                "pool_status": pool_status
            })
        else:
            # 获取所有数据源的连接池状态
            all_status = connection_pool.get_pool_status()
            
            # 增加数据源名称信息
            result = []
            for ds_id, stats in all_status.items():
                try:
                    ds = Datasource.objects.get(id=ds_id)
                    result.append({
                        "datasource_id": ds_id,
                        "datasource_name": ds.name,
                        "pool_status": stats
                    })
                except Datasource.DoesNotExist:
                    # 可能是已删除的数据源
                    continue
            
            return Response(result)
    
    except Exception as e:
        return Response(
            {"error": f"获取连接池状态失败: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_pool_connections(request, datasource_id):
    """为指定数据源预创建连接池连接"""
    try:
        # 检查数据源是否存在
        try:
            datasource = Datasource.objects.get(id=datasource_id)
        except Datasource.DoesNotExist:
            return Response(
                {"error": f"数据源ID {datasource_id} 不存在"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # 获取当前连接池状态
        before_status = connection_pool.get_pool_status(datasource_id)
        
        # 获取请求中的连接数量参数，默认创建2个
        count = request.data.get('count', 2)
        if not isinstance(count, int) or count <= 0 or count > 10:
            return Response(
                {"error": "连接数量必须是1-10之间的整数"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 预创建连接
        created = 0
        for _ in range(count):
            try:
                with connection_pool.get_connection(datasource) as conn:
                    # 简单测试连接
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.close()
                created += 1
            except Exception as e:
                return Response(
                    {
                        "error": f"创建连接失败: {str(e)}",
                        "created": created
                    },
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        
        # 获取更新后的连接池状态
        after_status = connection_pool.get_pool_status(datasource_id)
        
        return Response({
            "datasource_id": datasource_id,
            "datasource_name": datasource.name,
            "created": created,
            "before_status": before_status,
            "after_status": after_status
        })
    
    except Exception as e:
        return Response(
            {"error": f"预创建连接池失败: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def datasource_status(request):
    """获取所有数据源的连接状态（从缓存）"""
    user = request.user
    
    # 获取用户可访问的数据源
    if user.is_superuser:
        # 管理员可以访问所有数据源
        datasources = Datasource.objects.all()
    else:
        # 普通用户只能访问被授权的数据源
        permissions = DatasourcePermission.objects.filter(user=user)
        datasource_ids = [perm.datasource_id for perm in permissions]
        datasources = Datasource.objects.filter(id__in=datasource_ids)
    
    # 获取所有数据源的状态
    all_status = connection_pool.get_all_datasources_status()
    
    # 只返回用户有权限访问的数据源状态
    result = []
    for datasource in datasources:
        status_info = all_status.get(datasource.id, {
            'status': 'unknown',
            'last_check': 0
        })
        
        result.append({
            'id': datasource.id,
            'name': datasource.name,
            'status': status_info.get('status', 'unknown'),
            'last_check': status_info.get('last_check', 0),
            'db_type': {
                'id': datasource.db_type.id,
                'name': datasource.db_type.name,
                'code': datasource.db_type.code,
                'icon': datasource.db_type.icon
            } if datasource.db_type else None,
            'host': datasource.host,
            'port': datasource.port
        })
    
    return Response(result)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def single_datasource_status(request, pk):
    """获取单个数据源的连接状态（从缓存）"""
    try:
        # 获取数据源
        datasource = Datasource.objects.get(pk=pk)
        
        # 检查权限
        user = request.user
        if not user.is_superuser and not DatasourcePermission.objects.filter(
            user=user, datasource=datasource
        ).exists():
            return Response({
                'success': False,
                'message': '没有权限查看此数据源的状态'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # 获取状态信息
        status_info = connection_pool.get_datasource_status(pk)
        
        result = {
            'id': datasource.id,
            'name': datasource.name,
            'status': status_info.get('status', 'unknown'),
            'last_check': status_info.get('last_check', 0),
            'db_type': {
                'id': datasource.db_type.id,
                'name': datasource.db_type.name,
                'code': datasource.db_type.code,
                'icon': datasource.db_type.icon
            } if datasource.db_type else None,
            'host': datasource.host,
            'port': datasource.port
        }
        
        return Response(result)
    
    except Datasource.DoesNotExist:
        return Response({
            'success': False,
            'message': '数据源不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'message': f'获取数据源状态失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def debug_schema(request, pk):
    """调试获取数据库模式结构"""
    try:
        # 获取数据源
        datasource = Datasource.objects.get(pk=pk)
        
        # 检查权限
        if not request.user.is_superuser and not DatasourcePermission.objects.filter(
            user=request.user, datasource=datasource
        ).filter(Q(permission_type='admin') | Q(permission_type='read') | Q(permission_type='write')).exists():
            return Response({
                'success': False,
                'message': '没有权限查看此数据源的结构'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # 获取schema名称
        schema_name = request.data.get('schema', '')
        
        # 获取适配器
        from .adapters import get_adapter
        adapter = get_adapter(datasource)
        
        # 记录数据源信息
        db_info = {
            'id': datasource.id,
            'name': datasource.name,
            'host': datasource.host,
            'port': datasource.port,
            'database': datasource.database,
            'db_type': {
                'id': datasource.db_type.id,
                'name': datasource.db_type.name,
                'code': datasource.db_type.code,
            },
            'adapter_class': adapter.__class__.__name__
        }
        
        logger.info(f"调试数据库结构 - 数据源信息: {db_info}")
        
        # 执行调试方法
        result = adapter.debug_table_structure(schema_name)
        
        # 记录审计日志
        # AuditLog.objects.create(
        #     user=request.user,
        #     action_type='debug_schema',
        #     ip_address=request.META.get('REMOTE_ADDR'),
        #     datasource=datasource,
        #     content=f"用户 {request.user.username} 调试数据源 {datasource.name} 的模式信息"
        # )
        
        return Response({
            'success': True,
            'db_info': db_info,
            'debug_result': result
        })
        
    except Datasource.DoesNotExist:
        return Response({
            'success': False,
            'message': '数据源不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"调试获取数据库模式失败: {str(e)}")
        return Response({
            'success': False,
            'message': f'调试获取数据库模式失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def test_mysql_connection(request, pk):
    """测试MySQL连接和游标行为"""
    try:
        # 获取数据源
        datasource = Datasource.objects.get(pk=pk)
        
        # 检查是否是MySQL数据源
        if datasource.db_type.code.lower() != 'mysql':
            return Response({
                'success': False,
                'message': '非MySQL数据源'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查权限
        if not request.user.is_superuser and not DatasourcePermission.objects.filter(
            user=request.user, datasource=datasource
        ).filter(Q(permission_type='admin')).exists():
            return Response({
                'success': False,
                'message': '没有权限执行此操作'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # 创建日志记录
        logger.info(f"开始测试MySQL连接: {datasource.host}:{datasource.port}")
        
        # 构建连接参数，不指定游标类
        conn_params = {
            'host': datasource.host,
            'port': int(datasource.port),
            'user': datasource.username,
            'password': datasource.password,
            'charset': 'utf8mb4',
            'connect_timeout': 10
        }
        
        # 如果有数据库名，添加到连接参数
        if datasource.database:
            conn_params['database'] = datasource.database
        
        # 测试结果
        results = {
            'normal_cursor': None,
            'dict_cursor': None,
            'databases': None,
            'tables': None
        }
        
        # 测试1: 普通游标
        try:
            conn = pymysql.connect(**conn_params)
            cursor = conn.cursor()
            cursor.execute("SELECT 1 as test")
            test_result = cursor.fetchall()
            logger.info(f"普通游标结果: {test_result}, 类型: {type(test_result)}")
            if test_result and len(test_result) > 0:
                logger.info(f"第一行: {test_result[0]}, 类型: {type(test_result[0])}")
            
            results['normal_cursor'] = {
                'success': True,
                'result': str(test_result),
                'result_type': str(type(test_result)),
                'first_row': str(test_result[0]) if test_result else None,
                'first_row_type': str(type(test_result[0])) if test_result else None
            }
            cursor.close()
            conn.close()
        except Exception as e:
            logger.error(f"普通游标测试失败: {e}")
            results['normal_cursor'] = {
                'success': False,
                'error': str(e)
            }
        
        # 测试2: Dict游标
        try:
            conn_params['cursorclass'] = pymysql.cursors.DictCursor
            conn = pymysql.connect(**conn_params)
            cursor = conn.cursor()
            cursor.execute("SELECT 1 as test")
            test_result = cursor.fetchall()
            logger.info(f"Dict游标结果: {test_result}, 类型: {type(test_result)}")
            if test_result and len(test_result) > 0:
                logger.info(f"第一行: {test_result[0]}, 类型: {type(test_result[0])}")
            
            results['dict_cursor'] = {
                'success': True,
                'result': str(test_result),
                'result_type': str(type(test_result)),
                'first_row': str(test_result[0]) if test_result else None,
                'first_row_type': str(type(test_result[0])) if test_result else None
            }
            cursor.close()
            conn.close()
        except Exception as e:
            logger.error(f"Dict游标测试失败: {e}")
            results['dict_cursor'] = {
                'success': False,
                'error': str(e)
            }
        
        # 测试3: 获取数据库列表
        try:
            # 移除游标类参数
            if 'cursorclass' in conn_params:
                del conn_params['cursorclass']
            
            conn = pymysql.connect(**conn_params)
            cursor = conn.cursor()
            cursor.execute("SHOW DATABASES")
            test_result = cursor.fetchall()
            logger.info(f"数据库列表测试: {test_result}, 类型: {type(test_result)}")
            if test_result and len(test_result) > 0:
                logger.info(f"第一行: {test_result[0]}, 类型: {type(test_result[0])}")
            
            # 提取数据库名
            databases = []
            for row in test_result:
                if isinstance(row, dict):
                    for key, value in row.items():
                        databases.append(value)
                        break
                elif isinstance(row, (list, tuple)) and len(row) > 0:
                    databases.append(row[0])
                else:
                    databases.append(str(row))
            
            results['databases'] = {
                'success': True,
                'count': len(databases),
                'databases': databases[:10],  # 只返回前10个
                'first_row': str(test_result[0]) if test_result else None,
                'first_row_type': str(type(test_result[0])) if test_result else None
            }
            cursor.close()
            conn.close()
        except Exception as e:
            logger.error(f"数据库列表测试失败: {e}")
            results['databases'] = {
                'success': False,
                'error': str(e)
            }
        
        # 测试4: 获取表列表
        if datasource.database:
            try:
                conn = pymysql.connect(**conn_params)
                cursor = conn.cursor()
                cursor.execute(f"SHOW TABLES")
                test_result = cursor.fetchall()
                logger.info(f"表列表测试: {test_result}, 类型: {type(test_result)}")
                if test_result and len(test_result) > 0:
                    logger.info(f"第一行: {test_result[0]}, 类型: {type(test_result[0])}")
                
                # 提取表名
                tables = []
                for row in test_result:
                    if isinstance(row, dict):
                        for key, value in row.items():
                            tables.append(value)
                            break
                    elif isinstance(row, (list, tuple)) and len(row) > 0:
                        tables.append(row[0])
                    else:
                        tables.append(str(row))
                
                results['tables'] = {
                    'success': True,
                    'count': len(tables),
                    'tables': tables[:10],  # 只返回前10个
                    'first_row': str(test_result[0]) if test_result else None,
                    'first_row_type': str(type(test_result[0])) if test_result else None
                }
                cursor.close()
                conn.close()
            except Exception as e:
                logger.error(f"表列表测试失败: {e}")
                results['tables'] = {
                    'success': False,
                    'error': str(e)
                }
        
        # 记录审计日志
        # AuditLog.objects.create(
        #     user=request.user,
        #     action_type='test_mysql_connection',
        #     ip_address=request.META.get('REMOTE_ADDR'),
        #     datasource=datasource,
        #     content=f"用户 {request.user.username} 测试 MySQL 数据源 {datasource.name} 的连接"
        # )
        
        return Response({
            'success': True,
            'datasource': {
                'id': datasource.id,
                'name': datasource.name,
                'host': datasource.host,
                'port': datasource.port,
                'database': datasource.database
            },
            'results': results
        })
        
    except Datasource.DoesNotExist:
        return Response({
            'success': False,
            'message': '数据源不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"测试MySQL连接失败: {e}")
        return Response({
            'success': False,
            'message': f'测试MySQL连接失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class TablePermissionViewSet(viewsets.ModelViewSet):
    """
    表级权限管理视图集
    """
    queryset = TablePermission.objects.all()
    serializer_class = TablePermissionSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None #禁用分页
    
    def get_queryset(self):
        """根据用户权限过滤表级权限数据"""
        user = self.request.user
        
        # 超级管理员可以查看所有表级权限
        if user.is_superuser or user.is_staff:
            return TablePermission.objects.all()
        
        # 普通用户只能查看自己授予的或被授予的表级权限
        return TablePermission.objects.filter(
            Q(granted_by=user) | Q(user=user)
        )
    
    def list(self, request):
        """获取表级权限列表，支持按需加载和分组功能"""
        user = request.user
        # 获取查询参数
        group_only = request.query_params.get('group_only') == 'true'
        details = request.query_params.get('details') == 'true'
        user_id = request.query_params.get('user_id')
        datasource_id = request.query_params.get('datasource_id')
        
        # 获取基础查询集
        queryset = self.get_queryset()
        
        # 如果指定了用户ID和数据源ID，进行筛选
        if user_id and datasource_id:
            queryset = queryset.filter(user_id=user_id, datasource_id=datasource_id)
        elif user_id:
            queryset = queryset.filter(user_id=user_id)
        elif datasource_id:
            queryset = queryset.filter(datasource_id=datasource_id)
        
        # 如果仅需要分组信息，则进行聚合查询
        if group_only and not details:
            # 使用Django ORM分组聚合，确保数据库兼容性
            from django.db.models import Count, Max, F
            from django.db.models.functions import Concat
            
            # 使用ORM进行分组聚合
            aggregated_data = queryset.values(
                'user_id', 'user__username', 'datasource_id', 'datasource__name'
            ).annotate(
                permission_count=Count('id'),  # 添加表权限计数
                latest_granted_at=Max('granted_at'),
                user_username=F('user__username'),
                datasource_name=F('datasource__name')
            ).order_by('user__username', 'datasource__name')
            
            # 添加groupKey并确保有空的permissions数组
            results = []
            for item in aggregated_data:
                # 提取不同的权限类型
                permission_types = set(queryset.filter(
                    user_id=item['user_id'], 
                    datasource_id=item['datasource_id']
                ).values_list('permission_type', flat=True))
                
                # 构建结果项
                result_item = {
                    'user_id': item['user_id'],
                    'user_username': item['user_username'],
                    'datasource_id': item['datasource_id'],
                    'datasource_name': item['datasource_name'],
                    'permission_count': item['permission_count'],  # 实际表权限数量
                    'latest_granted_at': item['latest_granted_at'],
                    'permission_types': ', '.join(permission_types),
                    'groupKey': f"{item['user_id']}-{item['datasource_id']}",
                    'permissions': []  # 空数组，前端会按需加载详情
                }
                results.append(result_item)
            
            return Response(results)
        
        # 否则，返回完整的权限数据
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    def perform_create(self, serializer):
        """创建表级权限时自动设置授权人为当前用户和过期时间"""
        serializer.save(
            granted_by=self.request.user,
            expires_at=timezone.now() + timedelta(seconds=1) # Set expiration for 24 hours
        )
    
    def destroy(self, request, *args, **kwargs):
        """
        删除表级权限，覆盖默认行为，返回200状态码而不是204
        """
        instance = self.get_object()
        # 记录审计日志
        # AuditLog.objects.create(
        #     user=request.user,
        #     action_type='revoke_table_permission',
        #     ip_address=request.META.get('REMOTE_ADDR'),
        #     datasource=instance.datasource,
        #     content=f"用户 {request.user.username} 撤销了 {instance.user.username} 对表 {instance.schema_name}.{instance.table_name} 的权限"
        # )
        return super().destroy(request, *args, **kwargs)
    
    @action(detail=False, methods=['get'])
    def by_datasource(self, request):
        """根据数据源ID获取所有表权限"""
        datasource_id = request.query_params.get('datasource_id')
        if not datasource_id:
            return Response({"error": "缺少数据源ID参数"}, status=status.HTTP_400_BAD_REQUEST)
        
        # 验证用户是否有权限访问该数据源
        user = request.user
        if not user.is_superuser:
            datasource_exists = Datasource.objects.filter(
                Q(id=datasource_id) & 
                (Q(created_by=user) | Q(permissions__user=user))
            ).exists()
            
            if not datasource_exists:
                return Response({"error": "您没有权限访问该数据源"}, status=status.HTTP_403_FORBIDDEN)
        
        # 获取该数据源的所有表权限
        permissions = self.get_queryset().filter(datasource_id=datasource_id)
        serializer = self.get_serializer(permissions, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def user_tables(self, request):
        """获取当前用户有权限的所有表"""
        user = request.user
        datasource_id = request.query_params.get('datasource_id')
        
        # 构建基础查询
        query = Q(user=user)
        
        if datasource_id:
            query &= Q(datasource_id=datasource_id)
        
        # 获取用户表权限
        permissions = TablePermission.objects.filter(query)
        serializer = self.get_serializer(permissions, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def batch_delete(self, request):
        """批量删除表级权限"""
        permission_ids = request.data.get('permission_ids', [])
        if not permission_ids:
            return Response({
                'message': '未提供要删除的权限ID'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            with transaction.atomic():
                # 获取要删除的权限记录
                permissions = TablePermission.objects.filter(id__in=permission_ids)
                
                # 检查权限
                for perm in permissions:
                    if not (request.user.is_superuser or request.user.is_staff or perm.granted_by == request.user):
                        return Response({
                            'message': f'您没有权限删除ID为 {perm.id} 的权限记录'
                        }, status=status.HTTP_403_FORBIDDEN)
                
                # 记录审计日志
                for perm in permissions:
                    AuditLog.objects.create(
                        user=request.user,
                        action_type='table_permission_delete',
                        ip_address=request.META.get('REMOTE_ADDR'),
                        datasource=perm.datasource,
                        content=f"用户 {request.user.username} 删除了用户 {perm.user.username} 对表 {perm.schema_name + '.' if perm.schema_name else ''}{perm.table_name} 的 {perm.permission_type} 权限"
                    )
                
                # 执行删除
                deleted_count = permissions.delete()[0]
                
                return Response({
                    'message': f'成功删除 {deleted_count} 条权限记录'
                })
                
        except Exception as e:
            return Response({
                'message': f'批量删除失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def batch_create(self, request):
        """批量创建表权限"""
        data = request.data
        datasource_id = data.get('datasource_id')
        user_id = data.get('user_id')
        tables = data.get('tables', [])
        permission_type = data.get('permission_type', 'read')
        # description = data.get('description', '') # Assuming description might be part of batch, though not in current frontend form

        if not datasource_id or not user_id or not tables:
            return Response(
                {"error": "缺少必要参数"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 验证授权人权限
        if not request.user.is_superuser:
            datasource_admin = DatasourcePermission.objects.filter(
                datasource_id=datasource_id,
                user=request.user,
                permission_type='admin'
            ).exists()
            
            if not datasource_admin:
                return Response(
                    {"error": "您没有该数据源的管理权限"}, 
                    status=status.HTTP_403_FORBIDDEN
                )
        
        created_permissions = []
        expiration_time = timezone.now() + timedelta(hours=24) # Calculate expiration time once for the batch

        for table_data in tables: # Renamed 'table' to 'table_data' to avoid conflict
            schema_name = table_data.get('schema_name')
            table_name = table_data.get('table_name')
            
            if not table_name:
                continue
            
            # 创建或更新权限
            permission, created = TablePermission.objects.update_or_create(
                datasource_id=datasource_id,
                user_id=user_id,
                schema_name=schema_name,
                table_name=table_name,
                defaults={
                    'permission_type': permission_type,
                    'granted_by': request.user,
                    'expires_at': expiration_time, # Set expiration for 24 hours
                    # 'description': description # Add if description is part of batch data
                }
            )
            
            created_permissions.append(permission)
        
        serializer = self.get_serializer(created_permissions, many=True)
        return Response(serializer.data)

class DatabaseTypeViewSet(viewsets.ModelViewSet):
    """数据库类型视图集"""
    queryset = DatabaseType.objects.all()
    serializer_class = DatabaseTypeSerializer
    permission_classes = [permissions.IsAuthenticated]

class DatasourceViewSet(viewsets.ModelViewSet):
    """数据源视图集"""
    queryset = Datasource.objects.all()
    serializer_class = DatasourceSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """根据用户权限过滤数据源"""
        user = self.request.user
        
        if user.is_superuser:
            # 管理员可以访问所有数据源
            return Datasource.objects.all()
        else:
            # 普通用户只能访问被授权的数据源
            permissions = DatasourcePermission.objects.filter(user=user)
            datasource_ids = [perm.datasource_id for perm in permissions]
            return Datasource.objects.filter(id__in=datasource_ids)
    
    def perform_create(self, serializer):
        """创建数据源时自动设置创建者"""
        datasource = serializer.save(created_by=self.request.user, status='inactive')
        
        # 自动为创建者授予管理员权限
        DatasourcePermission.objects.create(
            user=self.request.user,
            datasource=datasource,
            permission_type='admin',
            granted_by=self.request.user
        )
        
        # 记录审计日志
        # AuditLog.objects.create(
        #     user=self.request.user,
        #     action_type='create_datasource',
        #     ip_address=self.request.META.get('REMOTE_ADDR'),
        #     content=f"创建数据源: {datasource.name} ({datasource.db_type.name})"
        # )

class DatasourcePermissionViewSet(viewsets.ModelViewSet):
    """数据源权限视图集"""
    queryset = DatasourcePermission.objects.all()
    serializer_class = DatasourcePermissionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """根据用户权限过滤数据源权限"""
        user = self.request.user
        
        if user.is_superuser or user.is_staff:
            # 管理员可以看到所有权限
            return DatasourcePermission.objects.all()
        else:
            # 普通用户只能看到自己的权限或自己授予的权限
            return DatasourcePermission.objects.filter(
                Q(user=user) | Q(granted_by=user)
            )
    
    def perform_create(self, serializer):
        """创建权限时自动设置授权人"""
        permission = serializer.save(granted_by=self.request.user)
        # 记录审计日志
        # AuditLog.objects.create(
        #     user=self.request.user,
        #     action_type='grant_datasource_permission',
        #     ip_address=self.request.META.get('REMOTE_ADDR'),
        #     datasource=permission.datasource,
        #     content=f"用户 {self.request.user.username} 授予 {permission.user.username} 对数据源 {permission.datasource.name} 的 {permission.permission_type} 权限"
        # )
        
    def destroy(self, request, *args, **kwargs):
        """删除数据源权限时，同时删除相关的表级权限"""
        permission = self.get_object()
        
        # 检查用户是否有权限删除
        current_user = request.user
        if not (current_user.is_superuser or current_user.is_staff):
            try:
                user_permission = DatasourcePermission.objects.get(
                    datasource=permission.datasource,
                    user=current_user
                )
                if user_permission.permission_type != 'admin':
                    return Response({
                        'success': False,
                        'message': '您没有权限执行此操作'
                    }, status=status.HTTP_403_FORBIDDEN)
            except DatasourcePermission.DoesNotExist:
                return Response({
                    'success': False,
                    'message': '您没有权限执行此操作'
                }, status=status.HTTP_403_FORBIDDEN)

        try:
            with transaction.atomic():
                # 删除相关的表级权限
                TablePermission.objects.filter(
                    datasource=permission.datasource,
                    user=permission.user
                ).delete()
                
                # 删除数据源权限
                permission.delete()
                
                # 记录审计日志
                AuditLog.objects.create(
                    user=request.user,
                    action_type='permission_delete',
                    ip_address=request.META.get('REMOTE_ADDR'),
                    datasource=permission.datasource,
                    content=f"用户 {request.user.username} 删除了 {permission.user.username} 对数据源 {permission.datasource.name} 的权限"
                )

            return Response({
                'success': True,
                'message': '数据源权限删除成功'
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'操作失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# 辅助函数：从SQL提取表名
def extract_table_names_from_sql(sql):
    """
    从SQL语句中提取表名和schema
    返回格式：[{'schema': schema_name, 'table': table_name}, ...]
    """
    import re
    
    # 尝试匹配常见的SQL模式（支持schema.table格式）
    from_pattern = r'FROM\s+([`"\[]{0,1}[\w\d_]+[`"\]]{0,1}(?:\.[`"\[]{0,1}[\w\d_]+[`"\]]{0,1})?)'
    into_pattern = r'INTO\s+([`"\[]{0,1}[\w\d_]+[`"\]]{0,1}(?:\.[`"\[]{0,1}[\w\d_]+[`"\]]{0,1})?)'
    update_pattern = r'UPDATE\s+([`"\[]{0,1}[\w\d_]+[`"\]]{0,1}(?:\.[`"\[]{0,1}[\w\d_]+[`"\]]{0,1})?)'
    join_pattern = r'JOIN\s+([`"\[]{0,1}[\w\d_]+[`"\]]{0,1}(?:\.[`"\[]{0,1}[\w\d_]+[`"\]]{0,1})?)'
    
    tables = []
    
    # 查找所有匹配项
    for pattern in [from_pattern, into_pattern, update_pattern, join_pattern]:
        matches = re.finditer(pattern, sql, re.IGNORECASE)
        for match in matches:
            full_name = match.group(1)
            
            # 移除可能的引号和方括号
            full_name = full_name.replace('`', '').replace('"', '').replace('[', '').replace(']', '')
            
            # 处理schema.table格式
            parts = full_name.split('.')
            if len(parts) == 2:
                schema_name, table_name = parts
            else:
                schema_name = None
                table_name = parts[0]
            
            tables.append({
                'schema': schema_name,
                'table': table_name
            })
    
    # 去重（基于schema和table的组合）
    unique_tables = []
    seen = set()
    for table in tables:
        key = f"{table['schema']}.{table['table']}" if table['schema'] else table['table']
        if key not in seen:
            seen.add(key)
            unique_tables.append(table)
    
    return unique_tables

# 辅助函数：判断是否是写操作SQL
def is_write_sql(sql):
    """
    判断SQL是否是写操作
    """
    # 简单检查SQL类型
    sql_lower = sql.lower().strip()
    
    # 只读操作列表
    read_operations = [
        'select', 'show', 'desc', 'describe', 'explain'
    ]
    
    # 检查是否是只读操作
    for op in read_operations:
        if sql_lower.startswith(op):
            return False
    
    # 默认认为是写操作
    return True

# 辅助函数：检查表级权限
def check_table_permission(user, datasource_id, table_name, schema_name=None, operation='read'):
    """
    检查用户是否有表的权限
    
    Args:
        user (User): Django用户对象
        datasource_id (int): 数据源ID
        table_name (str): 表名
        schema_name (str, optional): Schema名称
        operation (str, optional): 操作类型，'read'或'write'
        
    Returns:
        bool: 是否有权限
    """
    # 超级管理员有所有权限
    if user.is_superuser:
        return True
    
    # 检查数据源权限
    datasource_permission = DatasourcePermission.objects.filter(
        user=user,
        datasource_id=datasource_id
    ).first()
    
    if not datasource_permission:
        return False
        
    # 检查是否有明确的表级权限设置
    permission_query = Q(
        user=user,
        datasource_id=datasource_id,
        table_name=table_name
    )
    
    if schema_name:
        permission_query &= Q(schema_name=schema_name)
    else:
        permission_query &= (Q(schema_name__isnull=True) | Q(schema_name=''))

    table_permission = TablePermission.objects.filter(permission_query).first()
    
    # 如果没有明确的表级权限设置，则根据数据源权限判断
    if not table_permission:
        # 如果是管理员权限，允许所有操作
        if datasource_permission.permission_type == 'admin':
            return True
        # 如果是读操作，任何数据源权限都可以
        elif operation == 'read':
            return True
        # 如果是写操作，需要write权限
        else:
            return datasource_permission.permission_type == 'write'
    
    # 如果有明确的表级权限设置
    # Check if the permission has expired
    if table_permission.expires_at and table_permission.expires_at < timezone.now():
        logger.info(f"Table permission ID {table_permission.id} for user {user.username} on table {table_name} has expired at {table_permission.expires_at}.")
        return False # Permission has expired
    
    # 检查权限类型
    if table_permission.permission_type == 'denied':
        return False
    elif operation == 'read':
        # 读操作: 只读/读写权限都可以
        return table_permission.permission_type in ['read', 'write']
    elif operation == 'write':
        # 写操作: 只有读写权限可以
        return table_permission.permission_type == 'write'
    
    return False

# 获取数据源适配器
def get_datasource_adapter(datasource):
    """
    根据数据源获取适配的数据库适配器
    
    Args:
        datasource (Datasource): 数据源对象
        
    Returns:
        BaseAdapter: 数据库适配器实例
    """
    from .adapters import get_adapter
    
    try:
        return get_adapter(datasource)
    except Exception as e:
        logger.error(f"获取数据库适配器失败: {str(e)}")
        return None
