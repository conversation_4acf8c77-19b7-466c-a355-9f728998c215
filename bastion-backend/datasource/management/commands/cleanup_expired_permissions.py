# datasource/management/commands/cleanup_expired_permissions.py
from django.core.management.base import BaseCommand
from django.utils import timezone
from datasource.models import TablePermission # 确保从正确的 app 和 models 文件导入
import logging

# 获取一个 logger 实例 (与您 views.py 中使用的 logger 保持一致或新建一个)
logger = logging.getLogger(__name__) 
# 或者，如果想用Django settings里配置的logger:
# logger = logging.getLogger('django') # 或其他您配置的logger名

class Command(BaseCommand):
    help = 'Deletes expired table permissions from the database.'

    def handle(self, *args, **options):
        now = timezone.now()
        
        # 1. 查询所有 expires_at 早于当前时间的权限
        # 使用 __lt 表示 "less than"
        expired_permissions_query = TablePermission.objects.filter(expires_at__lt=now)
        
        # 获取将要被删除的权限数量 (在实际删除前)
        count_to_delete = expired_permissions_query.count()
        
        if count_to_delete > 0:
            try:
                # 2. 批量删除这些权限
                # .delete() 返回一个元组 (deleted_count, type_map)，我们关心第一个值
                deleted_objects_count, _ = expired_permissions_query.delete()
                
                # 使用 Django BaseCommand 提供的 stdout 来输出成功信息
                # self.style.SUCCESS() 会用绿色高亮显示 (如果终端支持)
                success_message = f'Successfully deleted {deleted_objects_count} expired table permissions.'
                self.stdout.write(self.style.SUCCESS(success_message))
                logger.info(success_message) # 也可以记录到日志文件
            except Exception as e:
                # 处理删除过程中可能发生的错误
                error_message = f'Error during deletion of expired permissions: {e}'
                self.stderr.write(self.style.ERROR(error_message)) # stderr 用于错误输出
                logger.error(error_message)
        else:
            # 如果没有过期的权限
            info_message = 'No expired table permissions found to delete.'
            self.stdout.write(self.style.INFO(info_message)) # INFO 样式
            logger.info(info_message)
