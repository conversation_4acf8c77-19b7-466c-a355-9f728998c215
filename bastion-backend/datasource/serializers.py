from rest_framework import serializers
from django.contrib.auth.models import User
from .models import DatabaseType, Datasource, DatasourcePermission, TablePermission

# 其他现有序列化器...

class DatabaseTypeSerializer(serializers.ModelSerializer):
    """数据库类型序列化器"""
    class Meta:
        model = DatabaseType
        fields = ['id', 'name', 'code', 'icon']

class DatasourceSerializer(serializers.ModelSerializer):
    """数据源序列化器"""
    db_type_id = serializers.IntegerField(write_only=True, required=False)
    db_type_name = serializers.ReadOnlyField(source='db_type.name')
    db_type_code = serializers.ReadOnlyField(source='db_type.code')
    db_type_icon = serializers.ReadOnlyField(source='db_type.icon')
    created_by_username = serializers.ReadOnlyField(source='created_by.username')
    
    class Meta:
        model = Datasource
        fields = [
            'id', 'name', 'db_type', 'db_type_id', 'db_type_name', 'db_type_code', 
            'db_type_icon', 'host', 'port', 'username', 'password', 'database', 
            'status', 'description', 'created_by', 'created_by_username', 
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'status', 'created_by', 'created_at', 'updated_at']
        extra_kwargs = {
            'password': {'write_only': True},
            'database': {'required': False},
            'db_type': {'required': False}
        }
    
    def create(self, validated_data):
        # 从验证数据中提取db_type_id
        db_type_id = validated_data.pop('db_type_id', None)
        
        # 如果db_type_id为空，尝试从db_type字段获取
        if not db_type_id and 'db_type' in validated_data:
            db_type_id = validated_data.pop('db_type')
        
        if not db_type_id:
            raise serializers.ValidationError({'db_type_id': '数据库类型ID不能为空'})
            
        # 获取db_type对象
        try:
            db_type = DatabaseType.objects.get(pk=db_type_id)
        except DatabaseType.DoesNotExist:
            raise serializers.ValidationError({'db_type_id': '数据库类型不存在'})
        
        # 创建数据源
        datasource = Datasource(
            db_type=db_type,
            **validated_data
        )
        datasource.save()
        return datasource

class DatasourcePermissionSerializer(serializers.ModelSerializer):
    """数据源权限序列化器"""
    user_username = serializers.ReadOnlyField(source='user.username')
    datasource_name = serializers.ReadOnlyField(source='datasource.name')
    granted_by_username = serializers.ReadOnlyField(source='granted_by.username')
    
    # 默认设置permission_type为'read'
    permission_type = serializers.CharField(default='read', write_only=True)
    
    class Meta:
        model = DatasourcePermission
        fields = [
            'id', 'datasource', 'datasource_name', 'user', 'user_username',
            'permission_type', 'granted_by', 'granted_by_username', 'granted_at'
        ]
        read_only_fields = ['granted_at', 'granted_by']

class TablePermissionSerializer(serializers.ModelSerializer):
    """表级权限序列化器"""
    user_username = serializers.ReadOnlyField(source='user.username')
    user_email = serializers.ReadOnlyField(source='user.email')
    datasource_name = serializers.ReadOnlyField(source='datasource.name')
    granted_by_username = serializers.ReadOnlyField(source='granted_by.username')
    
    class Meta:
        model = TablePermission
        fields = [
            'id', 'datasource', 'datasource_name', 'user', 'user_username', 
            'user_email', 'schema_name', 'table_name', 'permission_type', 
            'granted_by', 'granted_by_username', 'granted_at', 'description'
        ]
        read_only_fields = ['granted_at', 'granted_by'] 