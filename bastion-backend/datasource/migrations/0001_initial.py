# Generated by Django 4.2.7 on 2025-05-23 03:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="DatabaseType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="数据库类型名称"
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="类型代码"
                    ),
                ),
                (
                    "icon",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="图标"
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="描述"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "数据库类型",
                "verbose_name_plural": "数据库类型",
            },
        ),
        migrations.CreateModel(
            name="Datasource",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="数据源名称")),
                ("host", models.CharField(max_length=255, verbose_name="主机地址")),
                ("port", models.IntegerField(verbose_name="端口")),
                ("username", models.CharField(max_length=100, verbose_name="用户名")),
                ("password", models.CharField(max_length=255, verbose_name="密码")),
                (
                    "database",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="数据库名"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "活跃"),
                            ("inactive", "不活跃"),
                            ("testing", "测试中"),
                        ],
                        default="inactive",
                        max_length=20,
                        verbose_name="状态",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="描述"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_datasources",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建人",
                    ),
                ),
                (
                    "db_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="datasource.databasetype",
                        verbose_name="数据库类型",
                    ),
                ),
            ],
            options={
                "verbose_name": "数据源",
                "verbose_name_plural": "数据源",
                "unique_together": {("name", "host", "port")},
            },
        ),
        migrations.CreateModel(
            name="DatasourcePermission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "permission_type",
                    models.CharField(
                        choices=[
                            ("read", "只读"),
                            ("write", "读写"),
                            ("admin", "管理员"),
                        ],
                        default="read",
                        max_length=20,
                        verbose_name="权限类型",
                    ),
                ),
                (
                    "granted_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="授权时间"),
                ),
                (
                    "datasource",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="permissions",
                        to="datasource.datasource",
                        verbose_name="数据源",
                    ),
                ),
                (
                    "granted_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="granted_permissions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="授权人",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="datasource_permissions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "数据源权限",
                "verbose_name_plural": "数据源权限",
                "unique_together": {("datasource", "user")},
            },
        ),
    ]
