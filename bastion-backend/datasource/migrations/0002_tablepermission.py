# Generated by Django 4.2.7 on 2025-05-23 07:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("datasource", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="TablePermission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "schema_name",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="Schema名称"
                    ),
                ),
                ("table_name", models.CharField(max_length=100, verbose_name="表名称")),
                (
                    "permission_type",
                    models.CharField(
                        choices=[
                            ("read", "只读"),
                            ("write", "读写"),
                            ("denied", "禁止访问"),
                        ],
                        default="read",
                        max_length=20,
                        verbose_name="权限类型",
                    ),
                ),
                (
                    "granted_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="授权时间"),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="说明"),
                ),
                (
                    "datasource",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="table_permissions",
                        to="datasource.datasource",
                        verbose_name="数据源",
                    ),
                ),
                (
                    "granted_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="granted_table_permissions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="授权人",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="table_permissions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "表权限",
                "verbose_name_plural": "表权限",
                "unique_together": {
                    ("datasource", "user", "schema_name", "table_name")
                },
            },
        ),
    ]
