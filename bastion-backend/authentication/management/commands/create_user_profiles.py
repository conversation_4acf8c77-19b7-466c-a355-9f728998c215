from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from authentication.models import UserProfile

class Command(BaseCommand):
    help = '为所有用户创建用户档案'

    def handle(self, *args, **options):
        for user in User.objects.all():
            try:
                # 检查用户是否已有档案
                profile = UserProfile.objects.get(user=user)
                self.stdout.write(self.style.SUCCESS(f"用户 {user.username} 已有档案"))
            except UserProfile.DoesNotExist:
                # 创建新档案
                UserProfile.objects.create(user=user)
                self.stdout.write(self.style.SUCCESS(f"为用户 {user.username} 创建了档案")) 