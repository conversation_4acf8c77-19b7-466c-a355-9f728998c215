from django.db import connection
from django.utils.deprecation import MiddlewareMixin
import logging

logger = logging.getLogger('django')

class DatabaseConnectionMiddleware(MiddlewareMixin):
    """
    中间件用于确保数据库连接的可靠性
    在请求处理前检查连接状态，如果连接已断开则重新连接
    """
    
    def process_request(self, request):
        """在处理请求前检查数据库连接"""
        if connection.connection and not connection.is_usable():
            logger.warning("数据库连接已断开，尝试重新连接")
            connection.close()
        return None
        
    def process_response(self, request, response):
        """请求处理完成后，如果连接不再使用则关闭它"""
        if connection.connection and not connection.in_atomic_block:
            connection.close()
        return response 