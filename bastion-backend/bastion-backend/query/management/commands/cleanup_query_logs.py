from django.core.management.base import BaseCommand
from query.models import Query
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '清理旧的SQL查询日志记录'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='保留最近多少天的记录，默认30天'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅显示将要删除的记录数量，不实际删除'
        )

    def handle(self, *args, **options):
        days = options['days']
        dry_run = options['dry_run']
        
        self.stdout.write(f"开始清理 {days} 天前的查询日志记录...")
        
        if dry_run:
            from django.utils import timezone
            cutoff_date = timezone.now() - timezone.timedelta(days=days)
            count = Query.objects.filter(start_time__lt=cutoff_date).count()
            self.stdout.write(self.style.SUCCESS(f"将删除 {count} 条记录 (dry run 模式，未实际删除)"))
        else:
            count = Query.cleanup_old_records(days)
            self.stdout.write(self.style.SUCCESS(f"成功删除 {count} 条旧查询记录")) 