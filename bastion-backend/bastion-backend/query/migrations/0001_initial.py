# Generated by Django 4.2.7 on 2025-05-26 06:35

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("datasource", "0002_tablepermission"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="SavedQuery",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="查询名称")),
                ("sql_content", models.TextField(verbose_name="SQL内容")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="描述"),
                ),
                (
                    "is_public",
                    models.BooleanField(default=False, verbose_name="是否公开"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "datasource",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="saved_queries",
                        to="datasource.datasource",
                        verbose_name="数据源",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="saved_queries",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "保存的查询",
                "verbose_name_plural": "保存的查询",
                "ordering": ["-updated_at"],
            },
        ),
        migrations.CreateModel(
            name="Query",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("sql_content", models.TextField(verbose_name="SQL内容")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("running", "运行中"),
                            ("completed", "已完成"),
                            ("failed", "失败"),
                            ("cancelled", "已取消"),
                        ],
                        default="running",
                        max_length=20,
                        verbose_name="状态",
                    ),
                ),
                (
                    "start_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="开始时间"),
                ),
                (
                    "end_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="结束时间"
                    ),
                ),
                (
                    "result",
                    models.TextField(blank=True, null=True, verbose_name="执行结果"),
                ),
                (
                    "error_message",
                    models.TextField(blank=True, null=True, verbose_name="错误信息"),
                ),
                (
                    "affected_rows",
                    models.IntegerField(default=0, verbose_name="影响行数"),
                ),
                (
                    "execution_time",
                    models.FloatField(default=0, verbose_name="执行时间(秒)"),
                ),
                (
                    "datasource",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="queries",
                        to="datasource.datasource",
                        verbose_name="数据源",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="queries",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="执行用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "SQL审计日志",
                "verbose_name_plural": "SQL审计日志",
                "db_table": "audit_sqllog",
                "ordering": ["-start_time"],
            },
        ),
    ]
