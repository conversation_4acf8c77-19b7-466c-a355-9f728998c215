from django.shortcuts import render, get_object_or_404
from django.db import transaction
from django.utils import timezone
from django.db import models
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
import json
import pymysql
import clickhouse_driver
import re
import time
import datetime
import pandas as pd
from io import StringIO
from django.contrib.auth.models import User
from datasource.models import Datasource, DatasourcePermission, TablePermission
from audit.models import AuditLog, SQLAuditRule, SQLAuditResult
from django.db.models import Q
from .models import Query, SavedQuery  # 添加SavedQuery模型导入
import logging
import sqlite3
import traceback
from dateutil import parser
from datasource.adapters import get_adapter # 导入适配器工厂函数

logger = logging.getLogger(__name__)

def audit_sql(sql, user):
    """SQL审计功能"""
    audit_results = []
    risk_level = 'low'
    
    # 获取启用的审计规则
    rules = SQLAuditRule.objects.filter(is_enabled=True)
    
    for rule in rules:
        if rule.rule_type == 'keyword':
            # 关键字匹配
            if rule.pattern.lower() in sql.lower():
                audit_results.append({
                    'rule_name': rule.name,
                    'rule_type': rule.rule_type,
                    'matched_content': rule.pattern,
                    'risk_level': rule.risk_level,
                    'description': rule.description
                })
                
                # 更新风险等级
                if RISK_LEVEL_MAP.get(rule.risk_level, 0) > RISK_LEVEL_MAP.get(risk_level, 0):
                    risk_level = rule.risk_level
        
        elif rule.rule_type == 'regex':
            # 正则表达式匹配
            try:
                pattern = re.compile(rule.pattern, re.IGNORECASE)
                matches = pattern.findall(sql)
                if matches:
                    audit_results.append({
                        'rule_name': rule.name,
                        'rule_type': rule.rule_type,
                        'matched_content': ', '.join(matches),
                        'risk_level': rule.risk_level,
                        'description': rule.description
                    })
                    
                    # 更新风险等级
                    if RISK_LEVEL_MAP.get(rule.risk_level, 0) > RISK_LEVEL_MAP.get(risk_level, 0):
                        risk_level = rule.risk_level
            except:
                pass
    
    return audit_results, risk_level

def execute_clickhouse_query(datasource, sql, user, schema=None, page=1, page_size=100):
    """执行ClickHouse查询"""
    try:
        # 连接数据库
        logger.info(f"尝试连接ClickHouse数据库: {datasource.host}:{datasource.port}/{datasource.database}")
        
        import requests
        import json
        from urllib.parse import quote
        
        # 构建HTTP URL，ClickHouse HTTP端口默认为8123
        # 如果数据源配置的是9000/9001端口(ClickHouse-client端口)，则自动转为8123
        port = int(datasource.port)
        if port == 9001:
            port = 8124
            logger.info(f"检测到ClickHouse原生端口{datasource.port}，自动转换为HTTP端口8123")
        
        http_url = f"http://{datasource.host}:{port}"
        logger.info(f"ClickHouse HTTP URL: {http_url}")
        
        # 设置认证
        auth = None
        if datasource.username:
            from requests.auth import HTTPBasicAuth
            auth = HTTPBasicAuth(datasource.username, datasource.password or '')
        
        # 测试连接
        logger.info(f"尝试HTTP连接: {http_url}")
        try:
            test_response = requests.get(f"{http_url}/ping", auth=auth, timeout=5)
            if test_response.status_code == 200:
                logger.info(f"成功连接到ClickHouse HTTP接口: {http_url}")
            else:
                logger.warning(f"ClickHouse HTTP连接测试失败，状态码: {test_response.status_code}")
        except Exception as e:
            logger.warning(f"ClickHouse HTTP连接测试失败: {str(e)}")
        
        start_time = time.time()
        
        # 设置数据库参数
        database = datasource.database or ''
        if schema:
            database = schema
        
        # 处理SQL查询
        is_select_query = sql.strip().lower().startswith('select')
        has_limit = re.search(r'\bLIMIT\b', sql, re.IGNORECASE)
        
        # 添加分页
        effective_sql = sql
        if is_select_query and not has_limit:
            offset = (page - 1) * page_size
            effective_sql = f"{sql.rstrip(';')} LIMIT {page_size} OFFSET {offset}"
            logger.info(f"添加分页: LIMIT {page_size} OFFSET {offset}")
        
        # 构建请求参数
        params = {}
        if database:
            params['database'] = database
        
        # 执行查询
        logger.info(f"开始执行ClickHouse查询: {effective_sql[:100]}")
        
        # 发送HTTP请求
        response = requests.post(
            http_url, 
            params=params,
            data=effective_sql,
            auth=auth,
            headers={'Content-Type': 'text/plain'},
            timeout=60
        )
        logger.info(f"ClickHouse查询结果response: {response.text}")
        if response.status_code != 200:
            logger.error(f"ClickHouse查询失败，状态码: {response.status_code}, 响应: {response.text}")
            return {
                'success': False,
                'error_message': f"查询失败: HTTP {response.status_code} - {response.text}",
                'error_code': f"HTTP_{response.status_code}"
            }
        
        # 解析结果
        result_text = response.text
        
        # 尝试解析TSV格式的结果
        rows = []
        columns = []
        lines = result_text.strip().split('\n')
        
        if lines:
            # 第一行是列名
            columns = lines[0].split('\t')
            
            # 剩余行是数据
            for i in range(1, len(lines)):
                values = lines[i].split('\t')
                row = {}
                for j, col in enumerate(columns):
                    if j < len(values):
                        row[col] = values[j]
                    else:
                        row[col] = None
                rows.append(row)
        
        end_time = time.time()
        db_execution_time = end_time - start_time
        
        # 如果是SELECT查询，尝试获取总记录数
        total_count = len(rows)
        if is_select_query and not sql.strip().lower().startswith(('desc ', 'describe ', 'show ')):
            try:
                count_sql = f"SELECT count() as total_count FROM ({sql.rstrip(';')})"
                logger.info(f"执行COUNT查询: {count_sql[:100]}")
                
                count_response = requests.post(
                    http_url,
                    params=params,
                    data=count_sql,
                    auth=auth,
                    headers={'Content-Type': 'text/plain'},
                    timeout=30
                )
                
                if count_response.status_code == 200:
                    count_lines = count_response.text.strip().split('\n')
                    if len(count_lines) > 1:
                        total_count = int(count_lines[1])
                        logger.info(f"总记录数: {total_count}")
            except Exception as ce:
                logger.warning(f"获取总记录数失败: {str(ce)}")
        
        return {
            'success': True,
            'affected_rows': total_count,
            'execution_time': db_execution_time,
            'total_count': total_count,
            'rows': rows,
            'data': rows,
            'columns': columns
        }
        
    except Exception as e:
        logger.error(f"ClickHouse查询发生异常: {str(e)}")
        return {
            'success': False,
            'error_message': f"查询失败: {str(e)}",
            'error_code': type(e).__name__
        }

def execute_mysql_query(datasource, sql, user, schema=None, page=1, page_size=100):
    """执行MySQL查询"""
    conn = None
    try:
        # 连接数据库
        logger.info(f"尝试连接数据库: {datasource.host}:{datasource.port}/{datasource.database}")
        conn = pymysql.connect(
            host=datasource.host,
            port=int(datasource.port),
            user=datasource.username,
            password=datasource.password,
            database=datasource.database,
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor,
            connect_timeout=10,  # 增加连接超时时间
            read_timeout=300,    # 读取超时时间
            write_timeout=300,   # 写入超时时间
            client_flag=pymysql.constants.CLIENT.MULTI_STATEMENTS  # 支持多语句执行
        )
        logger.info(f"数据库连接成功: {datasource.host}:{datasource.port}/{datasource.database}")
        
        start_time = time.time()
        
        with conn.cursor() as cursor:
            # 尝试先执行一个简单的查询来测试连接
            try:
                logger.debug("执行连接测试查询: SELECT 1")
                cursor.execute("SELECT 1")
                cursor.fetchone()
                logger.debug("连接测试成功")
                
                # 如果指定了schema，先切换到指定的schema
                if schema:
                    logger.info(f"切换到指定的schema: {schema}")
                    cursor.execute(f"USE `{schema}`")
                    logger.info(f"成功切换到schema: {schema}")
            except pymysql.err.OperationalError as e:
                # 如果连接已断开，尝试重新连接
                if e.args[0] in (2006, 2013):  # MySQL server has gone away 或 Lost connection
                    logger.warning(f"MySQL连接已断开 (错误码: {e.args[0]}), 尝试重新连接...")
                    conn.ping(reconnect=True)
                    logger.info("重新连接成功")
                    
                    # 重新连接后如果有schema，再次尝试切换
                    if schema:
                        logger.info(f"重新连接后切换到指定的schema: {schema}")
                        cursor.execute(f"USE `{schema}`")
                        logger.info(f"重新连接后成功切换到schema: {schema}")
                else:
                    # 其他操作错误，重新抛出
                    logger.error(f"连接测试失败: {str(e)}")
                    raise
            
            original_sql = sql
            sql_lower = sql.strip().lower()
        
            if sql_lower.startswith(('desc ', 'describe ')):
                match = re.match(r'(?:desc|describe)\s+([`"\[]{0,1}[\w\d_\.]+[`"\]]{0,1})', sql.strip(), re.IGNORECASE)
                if match:
                    table_name_with_schema = match.group(1).replace('`', '').replace('"', '').replace('[', '').replace(']', '')
                    if '.' in table_name_with_schema:
                        effective_schema, table_name = table_name_with_schema.split('.', 1)
                    else:
                        effective_schema = schema
                        table_name = table_name_with_schema
                    
                    sql = f"""
                        SELECT
                            COLUMN_NAME        AS `Field`,
                            COLUMN_TYPE        AS `Type`,
                            IS_NULLABLE        AS `Null`,
                            COLUMN_KEY         AS `Key`,
                            COLUMN_DEFAULT     AS `Default`,
                            EXTRA              AS `Extra`,
                            COLUMN_COMMENT     AS `Comment`
                        FROM
                            information_schema.COLUMNS
                        WHERE
                            TABLE_SCHEMA = '{effective_schema or datasource.database}' AND TABLE_NAME = '{table_name}'
                        ORDER BY
                            ORDINAL_POSITION;
                    """
            
            # 检查SQL是否是SELECT查询
            is_select_query = sql.strip().lower().startswith('select')
            
            # 如果是SELECT查询，并且没有包含LIMIT，添加LIMIT和OFFSET
            if is_select_query and not original_sql.strip().lower().startswith(('desc ', 'describe ')):
                # 先执行COUNT查询获取总记录数
                # 移除SQL末尾可能存在的分号，避免语法错误
                sql_for_count = sql.strip()
                if sql_for_count.endswith(';'):
                    sql_for_count = sql_for_count[:-1]
                
                count_sql = f"SELECT COUNT(*) as total_count FROM ({sql_for_count}) as count_query"
                logger.info(f"执行COUNT查询: {count_sql[:100]}")
                cursor.execute(count_sql)
                count_result = cursor.fetchone()
                total_count = count_result.get('total_count', 0) if count_result else 0
                logger.info(f"总记录数: {total_count}")
                
                # 计算OFFSET
                offset = (page - 1) * page_size
                
                # 处理SQL语句，确保不会因为末尾分号导致语法错误
                sql_for_pagination = sql.strip()
                if sql_for_pagination.endswith(';'):
                    sql_for_pagination = sql_for_pagination[:-1]
                
                # 检查SQL中是否已经有LIMIT
                has_limit = re.search(r'\bLIMIT\b', sql_for_pagination, re.IGNORECASE)
                
                # 如果没有LIMIT，添加LIMIT和OFFSET
                if not has_limit:
                    paginated_sql = f"{sql_for_pagination} LIMIT {page_size} OFFSET {offset}"
                    logger.info(f"添加分页: {paginated_sql[-50:]}")
                else:
                    paginated_sql = sql_for_pagination
                    logger.info("SQL已包含LIMIT，不添加分页")
                
                # 执行带分页的SQL
                logger.info(f"开始执行带分页的SQL: {paginated_sql[:100]}")
                affected_rows = cursor.execute(paginated_sql)
            else:
                # 非SELECT查询，直接执行
                # 处理SQL语句，确保不会因为末尾分号导致语法错误
                sql_to_execute = sql.strip()
                logger.info(f"开始执行非SELECT SQL: {sql_to_execute[:1000]}")
                affected_rows = cursor.execute(sql_to_execute)
                total_count = affected_rows
            
            # 对于SELECT查询，获取结果
            if sql.strip().lower().startswith(('select', 'show', 'describe', 'explain', 'desc')):
                try:
                    logger.debug("获取查询结果...")
                    result = cursor.fetchall()
                    logger.info(f"成功获取查询结果，行数: {len(result)}")
                    
                    # 处理日期时间类型
                    for row in result:
                        for key, value in row.items():
                            if isinstance(value, datetime.date) or isinstance(value, datetime.datetime):
                                row[key] = value.isoformat()
                    
                    # 处理列信息
                    columns = []
                    if result:
                        columns = list(result[0].keys())
                        logger.debug(f"结果列: {columns}")
                except pymysql.err.OperationalError as e:
                    if e.args[0] in (2006, 2013):  # MySQL server has gone away 或 Lost connection
                        # 重连并重新尝试
                        logger.warning(f"获取结果时连接断开 (错误码: {e.args[0]}), 尝试重新连接...")
                        conn.ping(reconnect=True)
                        logger.info("重新连接成功，重新执行查询")
                        cursor.execute(paginated_sql if is_select_query else sql)
                        result = cursor.fetchall()
                        
                        # 处理日期时间类型
                        for row in result:
                            for key, value in row.items():
                                if isinstance(value, datetime.date) or isinstance(value, datetime.datetime):
                                    row[key] = value.isoformat()
                        
                        # 处理列信息
                        columns = []
                        if result:
                            columns = list(result[0].keys())
                            logger.debug(f"重试后结果列: {columns}")
                    else:
                        logger.error(f"获取查询结果时出错: {str(e)}")
                        raise
            else:
                # 非查询类SQL，返回影响的行数
                result = []
                columns = []
                
                # 如果是修改操作，提交事务
                if not sql_to_execute.lower().startswith(('select', 'show', 'describe', 'explain')):
                    logger.info("执行非查询SQL，提交事务")
                    conn.commit()
        
        end_time = time.time()
        db_execution_time = end_time - start_time
        
        if conn:
            try:
                logger.debug("关闭数据库连接")
                conn.close()
            except Exception as close_error:
                logger.warning(f"关闭连接时出错 (忽略): {str(close_error)}")
        
        return {
            'success': True,
            'affected_rows': affected_rows,
            'execution_time': db_execution_time,
            'columns': columns,
            'data': result,
            'total_count': total_count,
            'page': page,
            'page_size': page_size,
            'total_pages': (total_count + page_size - 1) // page_size if page_size > 0 else 0
        }
    
    except pymysql.err.OperationalError as e:
        if conn:
            try:
                conn.rollback()
                conn.close()
                logger.debug("已回滚事务并关闭连接")
            except Exception as close_error:
                logger.warning(f"回滚和关闭连接时出错 (忽略): {str(close_error)}")
        
        error_code = e.args[0] if len(e.args) > 0 else 'unknown'
        error_msg = e.args[1] if len(e.args) > 1 else str(e)
        
        # 添加详细的错误诊断
        error_message = f"MySQL操作错误(代码: {error_code}): {error_msg}"
        logger.error(f"MySQL操作错误: 代码={error_code}, 消息={error_msg}")
        
        if error_code == 2006:
            error_message += " - 数据库连接已断开，请重试或检查数据库连接设置"
        elif error_code == 2013:
            error_message += " - 与数据库的连接丢失，可能是查询超时或网络问题"
        elif "Unknown database" in str(e):
            error_message += " - 请检查数据库名是否正确"
        elif "Access denied" in str(e):
            error_message += " - 请检查数据库访问权限"
        elif "Table" in str(e) and "doesn't exist" in str(e):
            error_message += " - 请检查表名是否正确"
        elif "Unknown column" in str(e):
            error_message += " - 请检查列名是否正确"
        elif "Syntax error" in str(e):
            error_message += " - 请检查SQL语法是否正确"
        
        logger.error(f"SQL执行错误: {error_message}")
        return {
            'success': False,
            'error_message': error_message,
            'error_code': error_code,
            'error_details': [str(e), f"MySQL错误代码: {error_code}"]
        }
    
    except Exception as e:
        if conn:
            try:
                conn.rollback()
                conn.close()
                logger.debug("已回滚事务并关闭连接")
            except Exception as close_error:
                logger.warning(f"回滚和关闭连接时出错 (忽略): {str(close_error)}")
                
        # 添加异常类型信息
        error_message = f"SQL执行错误({type(e).__name__}): {str(e)}"
        logger.error(f"SQL执行异常: {error_message}")
        logger.exception("异常详情:")
        
        # 获取异常堆栈
        error_traceback = traceback.format_exc()
        error_details = error_traceback.split('\n')
        
        return {
            'success': False,
            'error_message': error_message,
            'error_code': type(e).__name__,
            'error_details': error_details[-5:] if error_details else [str(e)]
        }

# 风险等级映射
RISK_LEVEL_MAP = {
    'low': 1,
    'medium': 2,
    'high': 3,
    'critical': 4
}

# 检查表级权限的辅助函数
def check_table_permission(user, datasource_id, table_name, schema_name=None, operation='read'):
    """
    检查用户是否有表的权限
    
    Args:
        user (User): Django用户对象
        datasource_id (int): 数据源ID
        table_name (str): 表名
        schema_name (str, optional): Schema名称
        operation (str, optional): 操作类型，'read'或'write'
        
    Returns:
        bool: 是否有权限
    """
    # 超级管理员有所有权限
    if user.is_superuser:
        return True
    
    # 检查数据源创建者
    datasource = Datasource.objects.filter(id=datasource_id).first()
    if datasource and datasource.created_by == user:
        return True
    
    # 检查表级权限
    permission_query = Q(
        user=user,
        datasource_id=datasource_id,
        table_name=table_name
    )
    
    if schema_name:
        permission_query &= Q(schema_name=schema_name)
    
    table_permission = TablePermission.objects.filter(permission_query).first()
    
    if not table_permission:
        return False
    
    # 检查权限类型
    if table_permission.permission_type == 'denied':
        return False
    elif operation == 'read':
        # 读操作: 只读/读写权限都可以
        return table_permission.permission_type in ['read', 'write']
    elif operation == 'write':
        # 写操作: 只有读写权限可以
        return table_permission.permission_type == 'write'
    
    return False

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def execute_query(request):
    """执行SQL查询 - 重构后统一使用Adapter"""
    try:
        logger.info(f"收到查询请求: {request.data}")
        
        datasource_id = request.data.get('datasource_id')
        sql_content = request.data.get('sql_content')
        schema = request.data.get('schema') # 获取前端选择的schema
        page = int(request.data.get('page', 1))
        page_size = int(request.data.get('page_size', 100))
        kill_query = request.data.get('kill_query', False)  # 获取是否为取消查询的标志
        tab_id = request.data.get('tab_id')  # 获取前端的标签ID，用于标识要取消的查询

        if not datasource_id or not sql_content:
            return Response({'success': False, 'message': '参数不完整'}, status=status.HTTP_400_BAD_REQUEST)

        datasource = Datasource.objects.get(pk=datasource_id)
        
        # 如果是取消查询请求
        if kill_query:
            logger.info(f"收到取消查询请求，Tab ID: {tab_id}")
            try:
                # 获取查询哈希值，用于标识特定查询
                query_hash = request.data.get('query_hash')
                logger.info(f"查询哈希值: {query_hash}")
                
                # 找到正在运行的查询记录并标记为取消
                running_queries = Query.objects.filter(
                    user=request.user,
                    datasource=datasource,
                    status='running'
                ).order_by('-start_time')
                
                if running_queries.exists():
                    query_to_cancel = running_queries.first()
                    query_to_cancel.status = 'cancelled'
                    query_to_cancel.end_time = timezone.now()
                    query_to_cancel.save()
                    
                    # 如果需要在数据库中终止查询
                    if '/* KILL_QUERY_FLAG */' in sql_content:
                        logger.info(f"尝试在数据库中终止查询进程")
                        try:
                            adapter = get_adapter(datasource)
                            # 传递查询哈希值，用于标识特定查询
                            kill_result = adapter.cancel_query(query_hash=query_hash)
                            logger.info(f"终止查询结果: {kill_result}")
                        except Exception as ke:
                            logger.error(f"终止数据库查询进程失败: {ke}")
                    
                    return Response({
                        'success': True,
                        'message': '查询已取消',
                        'query_id': query_to_cancel.id
                    })
                else:
                    return Response({
                        'success': False,
                        'message': '没有找到正在运行的查询'
                    }, status=status.HTTP_404_NOT_FOUND)
            except Exception as e:
                logger.exception(f"取消查询失败: {e}")
                return Response({
                    'success': False,
                    'message': f'取消查询失败: {e}'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # 权限检查逻辑
        if not request.user.is_superuser:
            # 检查数据源权限
            has_permission = DatasourcePermission.objects.filter(datasource=datasource, user=request.user).exists()
            if not has_permission:
                 return Response({'success': False, 'message': '没有权限在此数据源上执行查询'}, status=status.HTTP_403_FORBIDDEN)
            
            # 检查是否是写操作
            if not request.user.is_staff and is_write_sql(sql_content):
                 return Response({'success': False, 'message': '您只能执行只读查询'}, status=status.HTTP_403_FORBIDDEN)
            
            # 从SQL中提取表名，进行表级权限检查
            tables_info = extract_table_names_from_sql(sql_content)
            for table_name in tables_info:
                is_write_operation = is_write_sql(sql_content)
                operation_type = 'write' if is_write_operation else 'read'
                
                # 检查表级权限
                has_table_permission = check_table_permission(
                    user=request.user,
                    datasource_id=datasource_id,
                    table_name=table_name,
                    schema_name=schema,
                    operation=operation_type
                )
                
                if not has_table_permission:
                    return Response({
                        'success': False,
                        'message': f'您没有表 {schema+"."+table_name if schema else table_name} 的{operation_type}权限'
                    }, status=status.HTTP_403_FORBIDDEN)

        # SQL审计逻辑 (保持不变)
        audit_results, risk_level = audit_sql(sql_content, request.user)

        query_record = Query.objects.create(
            user=request.user,
            datasource=datasource,
            sql_content=sql_content,
            status='running'
        )

        start_time = time.time()
        
        try:
            adapter = get_adapter(datasource)
            result = adapter.execute_query(sql_content, schema=schema)

            if not result.get('success', False):
                raise Exception(result.get('error', '查询执行失败'))

            end_time = time.time()
            total_time = int((end_time - start_time) * 1000)
            
            # 从适配器返回的结果中正确提取数据
            result_data = result.get('data', []) # 使用 'data' 键
            columns = result.get('columns', [])
            row_count = len(result_data)

            query_record.status = 'completed'
            query_record.end_time = timezone.now()
            query_record.affected_rows = row_count
            query_record.execution_time = total_time
            query_record.save()
            
            # 将结果包装在前端期望的'data'对象中
            return Response({
                'success': True,
                'query_id': query_record.id,
                'total_time': total_time,
                'db_execution_time': result.get('db_execution_time', total_time),
                'data': {
                    'row_count': row_count,
                    'columns': columns,
                    'rows': result_data,
                },
                'audit_results': audit_results,
            })

        except Exception as e:
            logger.exception(f"查询执行失败: {e}")
            query_record.status = 'failed'
            query_record.end_time = timezone.now()
            query_record.error_message = str(e)
            query_record.save()
            # SQL执行失败时返回200状态码，让前端正常处理错误信息
            return Response({
                'success': False,
                'error': str(e),
                'message': f"查询执行失败: {e}",
                'query_id': query_record.id
            }, status=status.HTTP_200_OK)

    except Datasource.DoesNotExist:
        return Response({'success': False, 'message': '数据源不存在'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.exception(f"处理查询请求时发生未知异常: {e}")
        return Response({'success': False, 'message': f'服务器内部错误: {e}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def query_history(request):
    """获取查询历史"""
    user = request.user
    
    # 条件筛选参数
    datasource_id = request.query_params.get('datasource_id')
    limit = int(request.query_params.get('limit', 20))
    offset = int(request.query_params.get('offset', 0))
    
    # 构建查询条件
    query = {}
    
    # 非管理员只能查看自己的历史
    if not (user.is_superuser or user.is_staff):
        query['user'] = user
    else:
        # 管理员可按用户筛选
        user_id = request.query_params.get('user_id')
        if user_id:
            query['user_id'] = user_id
    
    # 按数据源筛选
    if datasource_id:
        query['datasource_id'] = datasource_id
    
    # 获取查询历史
    queries = Query.objects.filter(**query).order_by('-start_time')
    
    # 分页
    total = queries.count()
    queries = queries[offset:offset+limit]
    
    # 组装结果
    result = []
    for query in queries:
        result.append({
            'id': query.id,
            'datasource': {
                'id': query.datasource.id,
                'name': query.datasource.name,
            },
            'user': {
                'id': query.user.id,
                'username': query.user.username,
            },
            'sql_content': query.sql_content,
            'status': query.status,
            'start_time': query.start_time,
            'end_time': query.end_time,
            'affected_rows': query.affected_rows,
            'execution_time': query.execution_time,
        })
    
    return Response({
        'total': total,
        'items': result,
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def query_detail(request, pk):
    """获取查询详情"""
    user = request.user
    
    try:
        query = Query.objects.get(pk=pk)
        
        # 权限检查: 非管理员只能查看自己的查询
        if not (user.is_superuser or user.is_staff) and query.user != user:
            return Response({
                'success': False,
                'message': '您没有权限查看此查询'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # 获取审计结果
        audit_results = []
        for audit in query.audit_results.all():
            audit_results.append({
                'rule_name': audit.rule.name,
                'rule_type': audit.rule.rule_type,
                'matched_content': audit.matched_content,
                'risk_level': audit.risk_level,
                'description': audit.rule.description,
            })
        
        # 组装结果数据
        result = {
            'id': query.id,
            'datasource': {
                'id': query.datasource.id,
                'name': query.datasource.name,
                'db_type': query.datasource.db_type.name,
            },
            'user': {
                'id': query.user.id,
                'username': query.user.username,
            },
            'sql_content': query.sql_content,
            'status': query.status,
            'start_time': query.start_time,
            'end_time': query.end_time,
            'affected_rows': query.affected_rows,
            'execution_time': query.execution_time,
            'audit_results': audit_results,
        }
        
        # 添加结果数据
        if query.result:
            try:
                result_data = json.loads(query.result)
                # 不再尝试获取 columns 和 rows，因为我们不再保存这些数据
                result['row_count'] = result_data.get('row_count', 0)
                result['message'] = result_data.get('message', '查询结果未保存到数据库，仅记录元数据')
            except:
                result['row_count'] = 0
                result['message'] = '无法解析查询结果'
        
        # 添加空的列和行，保持前端兼容性
        result['columns'] = []
        result['rows'] = []
        
        # 添加错误信息
        if query.error_message:
            result['error'] = query.error_message
        
        return Response(result)
    
    except Query.DoesNotExist:
        return Response({
            'success': False,
            'message': '查询记录不存在'
        }, status=status.HTTP_404_NOT_FOUND)

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def saved_query_list(request):
    """获取或保存查询"""
    user = request.user
    
    # 获取保存的查询列表
    if request.method == 'GET':
        # 条件筛选参数
        datasource_id = request.query_params.get('datasource_id')
        # is_public = request.query_params.get('is_public') # We will rely on 'visibility' or filter by it if provided
        visibility_filter = request.query_params.get('visibility') # Expect 'private', 'public', 'shared', or empty for all

        # 构建查询条件
        # query = {} # Replaced with Q objects for more complex OR logic
        q_objects = models.Q() # Start with an empty Q object


        # 数据源筛选
        if datasource_id:
            # query['datasource_id'] = datasource_id
            q_objects &= models.Q(datasource_id=datasource_id)
        
        # 公开性筛选 based on 'visibility'
        if visibility_filter and visibility_filter != 'all': # 'all' or empty means no visibility filter
            # query['visibility'] = visibility_filter
            q_objects &= models.Q(visibility=visibility_filter)
        
        # 获取自己创建的和公开的查询 (adjusting logic for 'visibility')
        if not (user.is_superuser or user.is_staff):
            # User can see their own private/shared queries, or any public queries.
            # If visibility_filter is 'public', it's already handled by q_objects.
            # If visibility_filter is 'private' or 'shared', they must be the owner.
            # If no visibility_filter, they see their own (private/shared) + all public.
            
            own_queries_q = models.Q(user=user)
            public_queries_q = models.Q(visibility='public')

            if visibility_filter:
                if visibility_filter in ['private', 'shared']:
                    # If filtering for private/shared, must be owner
                    q_objects &= own_queries_q
                # If filtering for 'public', q_objects already has Q(visibility='public')
            else:
                # No specific visibility filter from user, so show own (any visibility) OR public
                q_objects &= (own_queries_q | public_queries_q)
            
            saved_queries = SavedQuery.objects.filter(q_objects).order_by('-updated_at').distinct()

        else: # Admins/staff can see all queries based on the filter
            saved_queries = SavedQuery.objects.filter(q_objects).order_by('-updated_at').distinct()
        
        # 组装结果
        result = []
        for query_item in saved_queries: # Renamed to query_item to avoid conflict
            result.append({
                'id': query_item.id,
                'name': query_item.name,
                'datasource': {
                    'id': query_item.datasource.id,
                    'name': query_item.datasource.name,
                },
                'datasource_id': query_item.datasource.id,  # 添加直接的datasource_id字段
                'datasource_name': query_item.datasource.name,  # 添加直接的datasource_name字段
                'user': {
                    'id': query_item.user.id,
                    'username': query_item.user.username,
                },
                'sql_content': query_item.sql_content,
                'description': query_item.description,
                'schema': query_item.schema,  # 添加schema字段
                'is_public': query_item.is_public, # Keep for backward compatibility if frontend uses it
                'visibility': getattr(query_item, 'visibility', 'private' if not query_item.is_public else 'public'), # Ensure visibility is returned
                'created_at': query_item.created_at,
                'updated_at': query_item.updated_at,
            })
        
        return Response(result)
    
    # 保存查询
    elif request.method == 'POST':
        data = request.data
        name = data.get('name')
        datasource_id = data.get('datasource_id')
        sql_content = data.get('sql_content')
        description = data.get('description', '')
        is_public = data.get('is_public', False)
        schema = data.get('schema')  # 获取schema字段
        
        # 参数验证
        if not name or not datasource_id or not sql_content:
            return Response({
                'success': False,
                'message': '缺少必填参数'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            datasource = Datasource.objects.get(pk=datasource_id)
            
            # 验证用户是否有权限使用该数据源
            if not (user.is_superuser or user.is_staff):
                try:
                    permission = DatasourcePermission.objects.get(datasource=datasource, user=user)
                except DatasourcePermission.DoesNotExist:
                    return Response({
                        'success': False,
                        'message': '您没有权限使用此数据源'
                    }, status=status.HTTP_403_FORBIDDEN)
            
            # 创建保存的查询
            saved_query = SavedQuery.objects.create(
                name=name,
                datasource=datasource,
                user=user,
                sql_content=sql_content,
                description=description,
                is_public=is_public,
                schema=schema  # 添加schema字段
            )
            
            # 记录审计日志
            # AuditLog.objects.create(
            #     user=user,
            #     action_type='saved_query_add',
            #     ip_address=request.META.get('REMOTE_ADDR'),
            #     content=f"用户 {request.user.username} 创建了已存查询: {saved_query.name}"
            # )
            
            return Response({
                'success': True,
                'message': '查询保存成功',
                'id': saved_query.id
            })
        
        except Datasource.DoesNotExist:
            return Response({
                'success': False,
                'message': '数据源不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'查询保存失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def saved_query_detail(request, pk):
    """获取、更新或删除保存的查询"""
    user = request.user
    
    try:
        saved_query = SavedQuery.objects.get(pk=pk)
        
        # 权限检查: 非管理员只能查看自己的或公开的查询，只能修改或删除自己的查询
        if not (user.is_superuser or user.is_staff):
            if request.method == 'GET' and not saved_query.is_public and saved_query.user != user:
                return Response({
                    'success': False,
                    'message': '您没有权限查看此查询'
                }, status=status.HTTP_403_FORBIDDEN)
            
            if request.method in ['PUT', 'DELETE'] and saved_query.user != user:
                return Response({
                    'success': False,
                    'message': '您没有权限修改或删除此查询'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # 获取查询详情
        if request.method == 'GET':
            return Response({
                'id': saved_query.id,
                'name': saved_query.name,
                'datasource': {
                    'id': saved_query.datasource.id,
                    'name': saved_query.datasource.name,
                },
                'datasource_id': saved_query.datasource.id,
                'datasource_name': saved_query.datasource.name,
                'user': {
                    'id': saved_query.user.id,
                    'username': saved_query.user.username,
                },
                'sql_content': saved_query.sql_content,
                'description': saved_query.description,
                'schema': saved_query.schema,
                'is_public': saved_query.is_public, # Keep for backward compatibility
                'visibility': getattr(saved_query, 'visibility', 'private' if not saved_query.is_public else 'public'), # Ensure visibility is returned
                'created_at': saved_query.created_at,
                'updated_at': saved_query.updated_at,
            })
        
        # 更新查询
        elif request.method == 'PUT':
            data = request.data
            
            # 更新字段
            if 'name' in data:
                saved_query.name = data['name']
            if 'sql_content' in data:
                saved_query.sql_content = data['sql_content']
            if 'description' in data:
                saved_query.description = data['description']
            if 'schema' in data:
                saved_query.schema = data['schema']
            
            # Handle 'visibility' first as it's the source of truth
            if 'visibility' in data:
                new_visibility = data['visibility']
                if new_visibility in ['private', 'shared', 'public']:
                    saved_query.visibility = new_visibility
                    # Update is_public based on the new visibility
                    saved_query.is_public = (new_visibility == 'public')
                else:
                    # Handle invalid visibility value if necessary, or ignore
                    pass 
            elif 'is_public' in data and 'visibility' not in data: 
                # Fallback: if only is_public is sent (e.g., older client)
                # and visibility is not, update visibility based on is_public
                saved_query.is_public = data['is_public']
                saved_query.visibility = 'public' if data['is_public'] else 'private'

            saved_query.save()
            
            # 记录审计日志
            # AuditLog.objects.create(
            #     user=user,
            #     action_type='saved_query_edit',
            #     ip_address=request.META.get('REMOTE_ADDR'),
            #     content=f"用户 {request.user.username} 更新了已存查询: {saved_query.name}"
            # )
            
            return Response({
                'success': True,
                'message': '查询更新成功'
            })
        
        # 删除查询
        elif request.method == 'DELETE':
            # 记录审计日志
            # AuditLog.objects.create(
            #     user=user,
            #     action_type='saved_query_delete',
            #     ip_address=request.META.get('REMOTE_ADDR'),
            #     content=f"用户 {request.user.username} 删除了已存查询: {saved_query.name}"
            # )
            
            saved_query.delete()
            return Response({
                'success': True,
                'message': '查询删除成功'
            })
    
    except SavedQuery.DoesNotExist:
        return Response({
            'success': False,
            'message': '保存的查询不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'message': f'操作失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_table_structure(request, datasource_id, table_name):
    """
    获取指定数据源中表的结构信息
    """
    schema = request.query_params.get('schema', None)
    datasource_type = request.query_params.get('datasourceType', None)
    logger.info(f"获取表结构: datasource_id={datasource_id}, table_name={table_name}, schema={schema}, datasource_type={datasource_type}")

    # 权限检查
    if not check_table_permission(request.user, datasource_id, table_name, schema, 'read'):
        return Response({'success': False, 'error': '没有权限查看该表'}, status=status.HTTP_403_FORBIDDEN)

    try:
        datasource = Datasource.objects.get(pk=datasource_id)
    except Datasource.DoesNotExist:
        return Response({'success': False, 'error': '数据源未找到'}, status=status.HTTP_404_NOT_FOUND)

    # 判断数据源类型
    db_type = datasource.db_type.code.lower()
    
    if db_type == 'mysql':
        return get_mysql_table_structure(datasource, table_name, schema)
    elif db_type == 'clickhouse':
        return get_clickhouse_table_structure(datasource, table_name, schema)
    else:
        return Response({'success': False, 'error': f'不支持的数据源类型: {datasource.db_type.name}'}, status=status.HTTP_400_BAD_REQUEST)

def get_mysql_table_structure(datasource, table_name, schema=None):
    """获取MySQL表结构"""
    conn = None
    try:
        conn = pymysql.connect(
            host=datasource.host,
            port=int(datasource.port),
            user=datasource.username,
            password=datasource.password,
            database=schema or datasource.database, # 如果提供了schema，直接连接到该库
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        with conn.cursor() as cursor:
            cursor.execute(f"DESCRIBE `{table_name}`")
            structure = cursor.fetchall()
            return Response(structure)
            
    except pymysql.Error as e:
        logger.error(f"获取MySQL表结构失败: {str(e)}")
        return Response({'success': False, 'error': f'数据库错误: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        logger.error(f"获取MySQL表结构时发生未知错误: {str(e)}")
        return Response({'success': False, 'error': f'获取表结构失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    finally:
        if conn:
            conn.close()

def get_clickhouse_table_structure(datasource, table_name, schema=None):
    """获取ClickHouse表结构 - 返回与MySQL格式兼容的结构"""
    try:
        import requests
        from requests.auth import HTTPBasicAuth
        
        # 构建HTTP URL，ClickHouse HTTP端口默认为8123
        # 如果数据源配置的是9000/9001端口(ClickHouse-client端口)，则自动转为8123
        port = int(datasource.port)
        if port == 9001:
            port = 8124
            logger.info(f"检测到ClickHouse原生端口{datasource.port}，自动转换为HTTP端口8124")
        elif port == 9000:
            port = 8123
            logger.info(f"检测到ClickHouse原生端口{datasource.port}，自动转换为HTTP端口8123")
        
        http_url = f"http://{datasource.host}:{port}"
        logger.info(f"ClickHouse HTTP URL: {http_url}")
        
        # 设置认证
        auth = None
        if datasource.username:
            auth = HTTPBasicAuth(datasource.username, datasource.password or '')
        
        # 设置数据库参数
        database = schema or datasource.database or ''
        
        # 只查询必要字段，让SQL尽可能简单
        query = f"SELECT name, type, comment FROM system.columns WHERE database = '{database}' AND table = '{table_name}'"
        
        # 执行查询前记录日志
        logger.info(f"执行ClickHouse表结构查询: {query}")
        
        # 发送HTTP请求
        response = requests.post(
            http_url,
            params={'database': database},
            data=query,
            auth=auth,
            headers={'Content-Type': 'text/plain'},
            timeout=30
        )
        
        if response.status_code != 200:
            logger.error(f"ClickHouse查询失败，状态码: {response.status_code}, 响应: {response.text}")
            return Response({'success': False, 'error': f'查询失败: HTTP {response.status_code} - {response.text}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # 解析结果
        result_text = response.text
        rows = []
        columns = []
        lines = result_text.strip().split('\n')
        
        if lines and len(lines) > 1:
            # 将ClickHouse的返回结果转换为MySQL格式，前端组件期望的格式
            for i in range(1, len(lines)):
                values = lines[i].split('\t')
                if len(values) >= 2:
                    # 创建与MySQL DESCRIBE结果兼容的结构
                    row = {
                        'Field': values[0],                   # 字段名
                        'Type': values[1],                    # 数据类型
                        'Null': 'YES',                        # 可为空
                        'Key': '',                            # 键类型
                        'Default': '',                        # 默认值
                        'Extra': '',                          # 额外信息
                    }
                    # 如果有注释，添加注释字段
                    if len(values) >= 3 and values[2]:
                        row['Comment'] = values[2]           # 注释
                    
                    rows.append(row)
            
            logger.info(f"成功获取表结构，共{len(rows)}个字段")
        else:
            logger.warning(f"表结构查询结果为空: {result_text}")
        
        # 处理空结果的情况
        if not rows:
            # 返回特殊格式，表示出错，但仍然能被前端识别为表字段
            return Response([{
                'Field': '获取表结构失败',
                'Type': 'String',
                'Null': 'YES',
                'Key': '',
                'Default': '',
                'Extra': '',
                'Comment': '请检查服务器日志或ClickHouse连接'
            }])
        
        # 返回与MySQL DESCRIBE命令格式兼容的结果
        return Response(rows)
        
    except Exception as e:
        logger.error(f"获取ClickHouse表结构失败: {str(e)}")
        return Response({'success': False, 'error': f'获取表结构失败: {str(e)}'}, 
                       status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_table_indexes(request, datasource_id, table_name):
    """
    获取指定数据源中表的索引信息
    """
    schema = request.query_params.get('schema', None)
    datasource_type = request.query_params.get('datasourceType', None)
    logger.info(f"获取表索引: datasource_id={datasource_id}, table_name={table_name}, schema={schema}, datasource_type={datasource_type}")

    # 权限检查
    if not check_table_permission(request.user, datasource_id, table_name, schema, 'read'):
        return Response({'success': False, 'error': '没有权限查看该表'}, status=status.HTTP_403_FORBIDDEN)

    try:
        datasource = Datasource.objects.get(pk=datasource_id)
    except Datasource.DoesNotExist:
        return Response({'success': False, 'error': '数据源未找到'}, status=status.HTTP_404_NOT_FOUND)

    # 判断数据源类型
    db_type = datasource.db_type.code.lower()
    
    if db_type == 'mysql':
        return get_mysql_table_indexes(datasource, table_name, schema)
    elif db_type == 'clickhouse':
        return get_clickhouse_table_indexes(datasource, table_name, schema)
    else:
        return Response({'success': False, 'error': f'不支持的数据源类型: {datasource.db_type.name}'}, status=status.HTTP_400_BAD_REQUEST)

def get_mysql_table_indexes(datasource, table_name, schema=None):
    """获取MySQL表索引"""
    try:
        conn = pymysql.connect(
            host=datasource.host,
            port=int(datasource.port),
            user=datasource.username,
            password=datasource.password,
            database=schema or datasource.database, # 如果提供了schema，直接连接到该库
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        with conn.cursor() as cursor:
            # SHOW INDEX FROM table_name在MySQL中不需要特别的schema上下文，因为它作用于当前数据库
            # 但为了严谨，我们已在连接时指定了database
            cursor.execute(f"SHOW INDEX FROM `{table_name}`")
            indexes = cursor.fetchall()
            return Response(indexes)
    except pymysql.Error as e:
        logger.error(f"获取MySQL表索引失败: {str(e)}")
        return Response({'success': False, 'error': f'数据库错误: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def get_clickhouse_table_indexes(datasource, table_name, schema=None):
    """获取ClickHouse表索引信息 - 返回与MySQL格式兼容的结构"""
    try:
        import requests
        from requests.auth import HTTPBasicAuth
        
        # 构建HTTP URL，ClickHouse HTTP端口默认为8123
        # 如果数据源配置的是9000/9001端口(ClickHouse-client端口)，则自动转为8123
        port = int(datasource.port)
        if port == 9001:
            port = 8124
            logger.info(f"检测到ClickHouse原生端口{datasource.port}，自动转换为HTTP端口8124")
        elif port == 9000:
            port = 8123
            logger.info(f"检测到ClickHouse原生端口{datasource.port}，自动转换为HTTP端口8123")
        
        http_url = f"http://{datasource.host}:{port}"
        logger.info(f"ClickHouse HTTP URL: {http_url}")
        
        # 设置认证
        auth = None
        if datasource.username:
            auth = HTTPBasicAuth(datasource.username, datasource.password or '')
        
        # 设置数据库参数
        database = schema or datasource.database or ''
        
        # 查询表的排序键和主键
        query = f"SELECT engine_full, sorting_key, primary_key FROM system.tables WHERE database = '{database}' AND name = '{table_name}'"
        
        # 执行查询前记录日志
        logger.info(f"执行ClickHouse表引擎和索引查询: {query}")
        
        # 发送HTTP请求
        response = requests.post(
            http_url,
            params={'database': database},
            data=query,
            auth=auth,
            headers={'Content-Type': 'text/plain'},
            timeout=30
        )
        
        if response.status_code != 200:
            logger.error(f"ClickHouse查询失败，状态码: {response.status_code}, 响应: {response.text}")
            return Response({'success': False, 'error': f'查询失败: HTTP {response.status_code} - {response.text}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # 解析结果
        result_text = response.text
        lines = result_text.strip().split('\n')
        
        # 准备返回MySQL格式兼容的索引信息
        result = []
        
        if len(lines) > 1:  # 如果有数据行
            values = lines[1].split('\t')
            engine_full = values[0] if len(values) > 0 else ""
            sorting_key = values[1] if len(values) > 1 else ""
            primary_key = values[2] if len(values) > 2 else ""
            
            # 添加排序键信息
            if sorting_key:
                # 解析可能的多列排序键
                sorting_columns = [col.strip() for col in sorting_key.split(',')]
                for i, col in enumerate(sorting_columns):
                    result.append({
                        'Table': table_name,
                        'Non_unique': 1,
                        'Key_name': 'SORTING_KEY',
                        'Seq_in_index': i + 1,
                        'Column_name': col,
                        'Collation': 'A',  # Ascending
                        'Cardinality': None,
                        'Sub_part': None,
                        'Packed': None,
                        'Null': 'YES',
                        'Index_type': 'SORTING',
                        'Comment': '',
                        'Index_comment': '排序键'
                    })
            
            # 添加主键信息
            if primary_key:
                # 解析可能的多列主键
                primary_columns = [col.strip() for col in primary_key.split(',')]
                for i, col in enumerate(primary_columns):
                    result.append({
                        'Table': table_name,
                        'Non_unique': 0,  # 主键是唯一的
                        'Key_name': 'PRIMARY',
                        'Seq_in_index': i + 1,
                        'Column_name': col,
                        'Collation': 'A',  # Ascending
                        'Cardinality': None,
                        'Sub_part': None,
                        'Packed': None,
                        'Null': '',  # 主键不能为NULL
                        'Index_type': 'PRIMARY',
                        'Comment': '',
                        'Index_comment': '主键'
                    })
            
            # 如果有引擎信息但没有主键和排序键
            if engine_full and not (primary_key or sorting_key):
                # 添加引擎信息作为索引注释
                result.append({
                    'Table': table_name,
                    'Non_unique': 1,
                    'Key_name': 'TABLE_ENGINE',
                    'Seq_in_index': 1,
                    'Column_name': '',
                    'Collation': None,
                    'Cardinality': None,
                    'Sub_part': None,
                    'Packed': None,
                    'Null': 'YES',
                    'Index_type': 'ENGINE',
                    'Comment': engine_full,
                    'Index_comment': 'ClickHouse表引擎'
                })
        
        # 如果没有索引信息，添加默认说明
        if not result:
            result.append({
                'Table': table_name,
                'Non_unique': 1,
                'Key_name': 'INFO',
                'Seq_in_index': 1,
                'Column_name': '',
                'Collation': None,
                'Cardinality': None,
                'Sub_part': None,
                'Packed': None,
                'Null': 'YES',
                'Index_type': 'NONE',
                'Comment': '此表没有索引信息',
                'Index_comment': 'ClickHouse表可能使用了不包含显式索引的引擎'
            })
        
        logger.info(f"成功获取ClickHouse表索引信息，共{len(result)}条记录")
        return Response(result)
        
    except Exception as e:
        logger.error(f"获取ClickHouse表索引失败: {str(e)}")
        return Response({'success': False, 'error': f'获取表索引失败: {str(e)}'}, 
                       status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_table_ddl(request, datasource_id, table_name):
    """
    获取指定数据源中表的建表语句(DDL)
    """
    schema = request.query_params.get('schema', None)
    datasource_type = request.query_params.get('datasourceType', None)
    logger.info(f"获取表DDL: datasource_id={datasource_id}, table_name={table_name}, schema={schema}, datasource_type={datasource_type}")

    # 权限检查
    if not check_table_permission(request.user, datasource_id, table_name, schema, 'read'):
        return Response({'success': False, 'error': '没有权限查看该表'}, status=status.HTTP_403_FORBIDDEN)

    try:
        datasource = Datasource.objects.get(pk=datasource_id)
    except Datasource.DoesNotExist:
        return Response({'success': False, 'error': '数据源未找到'}, status=status.HTTP_404_NOT_FOUND)

    # 判断数据源类型
    db_type = datasource.db_type.code.lower()
    
    if db_type == 'mysql':
        return get_mysql_table_ddl(datasource, table_name, schema)
    elif db_type == 'clickhouse':
        return get_clickhouse_table_ddl(datasource, table_name, schema)
    else:
        return Response({'success': False, 'error': f'不支持的数据源类型: {datasource.db_type.name}'}, status=status.HTTP_400_BAD_REQUEST)

def get_mysql_table_ddl(datasource, table_name, schema=None):
    """获取MySQL表DDL"""
    try:
        conn = pymysql.connect(
            host=datasource.host,
            port=int(datasource.port),
            user=datasource.username,
            password=datasource.password,
            database=schema or datasource.database,
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        with conn.cursor() as cursor:
            cursor.execute(f"SHOW CREATE TABLE `{table_name}`")
            result = cursor.fetchone()
            if result and 'Create Table' in result:
                ddl = result['Create Table']
                return Response({'ddl': ddl})
            else:
                return Response({'success': False, 'error': '未能获取建表语句'}, status=status.HTTP_404_NOT_FOUND)
    except pymysql.Error as e:
        logger.error(f"获取MySQL表DDL失败: {str(e)}")
        return Response({'success': False, 'error': f'数据库错误: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def get_clickhouse_table_ddl(datasource, table_name, schema=None):
    """获取ClickHouse表DDL - 确保返回格式与前端兼容"""
    try:
        import requests
        from requests.auth import HTTPBasicAuth
        
        # 构建HTTP URL，ClickHouse HTTP端口默认为8123
        # 如果数据源配置的是9000/9001端口(ClickHouse-client端口)，则自动转为8123
        port = int(datasource.port)
        if port == 9001:
            port = 8124
            logger.info(f"检测到ClickHouse原生端口{datasource.port}，自动转换为HTTP端口8124")
        elif port == 9000:
            port = 8123
            logger.info(f"检测到ClickHouse原生端口{datasource.port}，自动转换为HTTP端口8123")
        
        http_url = f"http://{datasource.host}:{port}"
        logger.info(f"ClickHouse HTTP URL: {http_url}")
        
        # 设置认证
        auth = None
        if datasource.username:
            auth = HTTPBasicAuth(datasource.username, datasource.password or '')
        
        # 设置数据库参数
        database = schema or datasource.database or ''
        
        # 使用标准查询获取表DDL
        query = f"SHOW CREATE TABLE {database}.{table_name}"
        
        # 执行查询前记录日志
        logger.info(f"执行ClickHouse表DDL查询: {query}")
        
        # 发送HTTP请求
        response = requests.post(
            http_url,
            params={'database': database},
            data=query,
            auth=auth,
            headers={'Content-Type': 'text/plain'},
            timeout=30
        )
        
        if response.status_code != 200:
            logger.error(f"ClickHouse查询失败，状态码: {response.status_code}, 响应: {response.text}")
            return Response({'success': False, 'error': f'查询失败: HTTP {response.status_code} - {response.text}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # 解析结果
        result_text = response.text
        
        # 检查是否返回了有效的DDL语句（应包含CREATE TABLE关键字）
        if 'CREATE TABLE' in result_text:
            # 提取完整的DDL语句
            ddl = result_text.strip()
            # 如果有标题行（列名），移除它
            if ddl.startswith('statement') or ddl.startswith('STATEMENT'):
                lines = ddl.split('\n', 1)
                if len(lines) > 1:
                    ddl = lines[1].strip()
            
            # 处理ClickHouse返回的转义字符
            # 1. 将\n转换为真正的换行符
            ddl = ddl.replace('\\n', '\n')
            # 2. 处理可能存在的其他转义字符
            ddl = ddl.replace('\\t', '\t').replace('\\"', '"').replace('\\\\', '\\')
            
            # 美化DDL，确保可读性
            try:
                # 0. 先移除多余的空格
                ddl = ' '.join(ddl.split())
                
                # 1. 处理数据类型定义中的括号和数字，避免拆分
                # 特别处理DateTime64(3)这样的格式，确保不被拆分
                import re
                # 匹配类型定义如 DateTime64(3) 并替换为无空格的 DateTime64(3)
                ddl = re.sub(r'([A-Za-z0-9]+)\s*\(\s*([0-9]+)\s*\)', r'\1(\2)', ddl)
                
                # 2. 替换逗号后添加换行和缩进
                formatted_ddl = ddl.replace(',', ',\n  ')
                
                # 3. 处理CREATE TABLE语句
                # 在表名后添加换行
                match = re.search(r'CREATE TABLE ([^\(]+)', formatted_ddl)
                if match:
                    table_def = match.group(0)
                    formatted_ddl = formatted_ddl.replace(table_def, f"{table_def}\n")
                
                # 4. 确保在左括号后有换行和缩进
                formatted_ddl = formatted_ddl.replace('(', '(\n  ')
                
                # 5. 特殊处理数据类型中的括号，避免被错误格式化
                # 例如DateTime64(3)不应该被分隔
                for data_type in ['DateTime64', 'Decimal', 'FixedString', 'Nullable']:
                    # 这里我们修复可能被错误分隔的数据类型定义
                    formatted_ddl = re.sub(f'({data_type})\n  \\(', f'{data_type}(', formatted_ddl)
                
                # 6. 修复ENGINE, PARTITION BY, ORDER BY等关键字的格式
                for keyword in ['ENGINE', 'PARTITION BY', 'ORDER BY', 'SETTINGS']:
                    formatted_ddl = formatted_ddl.replace(f'\n{keyword}', f'\n\n{keyword}')
                
                # 7. 确保右括号前有换行
                formatted_ddl = formatted_ddl.replace(')', '\n)')
                
                # 8. 修复数据类型定义中的右括号格式
                for data_type in ['DateTime64', 'Decimal', 'FixedString', 'Nullable']:
                    # 确保右括号不会单独一行
                    formatted_ddl = re.sub(f'({data_type}\\([^\\)]+)\n\\)', f'\\1)', formatted_ddl)
                
                # 9. 移除多余的连续空行
                while '\n\n\n' in formatted_ddl:
                    formatted_ddl = formatted_ddl.replace('\n\n\n', '\n\n')
                
                # 10. 给字段名和类型之间添加足够的空格对齐
                lines = formatted_ddl.split('\n')
                formatted_lines = []
                field_def_pattern = re.compile(r'^\s*`([^`]+)`\s+(.+)$')
                
                for line in lines:
                    match = field_def_pattern.match(line)
                    if match:
                        field_name = match.group(1)
                        field_type = match.group(2)
                        # 对齐字段名，保证类型从第30个字符位置开始
                        padding = max(2, 30 - len(field_name) - 2)  # -2 for backticks
                        formatted_lines.append(f"  `{field_name}`{' ' * padding}{field_type}")
                    else:
                        formatted_lines.append(line)
                
                formatted_ddl = '\n'.join(formatted_lines)
                
            except Exception as e:
                # 如果格式化失败，使用原始DDL
                logger.warning(f"DDL格式化失败: {str(e)}")
                formatted_ddl = ddl
            
            logger.info(f"成功获取表DDL: {table_name}")
            
            # 使用与MySQL兼容的格式返回
            return Response({
                'ddl': formatted_ddl,
                'success': True,
                'table_name': table_name,
                'schema': database,
                'create_table_sql': formatted_ddl
            })
        else:
            logger.warning(f"ClickHouse未返回有效的DDL（没有CREATE TABLE关键字），响应内容: {result_text[:100]}...")
            return Response({
                'success': False, 
                'error': '未能获取建表语句',
                'ddl': f'-- 无法获取表 {database}.{table_name} 的DDL\n-- 请检查表名是否正确或查看服务器日志'
            })
        
    except Exception as e:
        logger.error(f"获取ClickHouse表DDL失败: {str(e)}")
        return Response({'success': False, 'error': f'获取表DDL失败: {str(e)}'}, 
                       status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def edit_table_data(request, datasource_id, table_name):
    """编辑表数据"""
    try:
        # 获取数据源
        datasource = Datasource.objects.get(pk=datasource_id)
        
        # 获取请求数据
        operation = request.data.get('operation')  # insert, update, delete
        schema_name = request.data.get('schema_name')  # 获取数据库名
        row_data = request.data.get('row_data', {})  # 获取行数据
        conditions = request.data.get('conditions', {})  # 获取条件
        
        logger.info(f"编辑表数据: operation={operation}, schema_name={schema_name}, table_name={table_name}")
        
        # 检查schema_name是否存在
        if not schema_name:
            logger.error("缺少必要参数schema_name，无法确定操作的数据库")
            return Response({
                'success': False,
                'message': '缺少必要参数schema_name，请指定要操作的数据库'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 表全名
        qualified_table_name = f"`{schema_name}`.`{table_name}`"
        
        # 根据数据库类型执行不同的查询
        if datasource.db_type.code.lower() == 'mysql':
            if operation == 'insert':
                columns = list(row_data.keys())
                values = list(row_data.values())
                placeholders = []
                for value in values:
                    if value is None:
                        placeholders.append('NULL')
                    elif isinstance(value, bool):
                        placeholders.append('TRUE' if value else 'FALSE')
                    elif isinstance(value, (int, float)):
                        placeholders.append(str(value))
                    else:
                        placeholders.append(f"'{pymysql.converters.escape_string(str(value))}'")
                
                columns_str = ', '.join(f'`{col}`' for col in columns)
                values_str = ', '.join(placeholders)
                sql = f"INSERT INTO {qualified_table_name} ({columns_str}) VALUES ({values_str})"
                
            elif operation == 'update':
                set_clauses = []
                for column, value in row_data.items():
                    if value is None:
                        set_clauses.append(f"`{column}` = NULL")
                    elif isinstance(value, bool):
                        set_clauses.append(f"`{column}` = {'TRUE' if value else 'FALSE'}")
                    elif isinstance(value, (int, float)):
                        set_clauses.append(f"`{column}` = {value}")
                    else:
                        set_clauses.append(f"`{column}` = '{pymysql.converters.escape_string(str(value))}'")

                # 创建要更新的字段集合，这些字段不应该出现在WHERE条件中
                set_columns = set(row_data.keys())
                
                where_clauses = []
                if 'id' in conditions:
                    where_clauses.append(f"`id` = '{pymysql.converters.escape_string(str(conditions['id']))}'")
                else:
                    for column, value in conditions.items():
                        # 跳过要更新的字段，不要将它们包含在WHERE条件中
                        if column in set_columns:
                            logger.info(f"跳过在WHERE条件中使用要更新的字段: {column}")
                            continue
                            
                        if value is None:
                            where_clauses.append(f"`{column}` IS NULL")
                        elif isinstance(value, (int, float)):
                             where_clauses.append(f"`{column}` = {value}")
                        else:
                            where_clauses.append(f"`{column}` = '{pymysql.converters.escape_string(str(value))}'")
                
                set_str = ', '.join(set_clauses)
                where_str = ' AND '.join(where_clauses)
                
                if not where_str:
                    return Response({'success': False, 'message': '更新操作必须指定条件'}, status=status.HTTP_400_BAD_REQUEST)
                
                sql = f"UPDATE {qualified_table_name} SET {set_str} WHERE {where_str}"
                
            elif operation == 'delete':
                where_clauses = []
                delete_conditions = conditions or row_data
                if 'id' in delete_conditions:
                    where_clauses.append(f"`id` = '{pymysql.converters.escape_string(str(delete_conditions['id']))}'")
                else:
                    for column, value in delete_conditions.items():
                        if value is None:
                            where_clauses.append(f"`{column}` IS NULL")
                        elif isinstance(value, (int, float)):
                             where_clauses.append(f"`{column}` = {value}")
                        else:
                            where_clauses.append(f"`{column}` = '{pymysql.converters.escape_string(str(value))}'")

                where_str = ' AND '.join(where_clauses)
                
                if not where_str:
                    return Response({'success': False, 'message': '删除操作必须指定条件'}, status=status.HTTP_400_BAD_REQUEST)
                
                sql = f"DELETE FROM {qualified_table_name} WHERE {where_str}"
                
            else:
                return Response({'success': False, 'message': f'不支持的操作: {operation}'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 执行SQL (INSERT, UPDATE, DELETE)
            logger.info(f"开始执行非SELECT SQL: {sql[:1000]}")
            result = execute_mysql_query(datasource, sql, request.user, schema=schema_name)
            
            if result.get('success', False):
                return Response({
                    'success': True,
                    'message': f'{operation.capitalize()} 操作成功',
                    'affected_rows': result.get('affected_rows', 0)
                })
            else:
                return Response({
                    'success': False,
                    'message': result.get('error_message', '操作失败')
                }, status=status.HTTP_400_BAD_REQUEST)

        else:
            return Response({'success': False, 'message': f'不支持的数据库类型: {datasource.db_type.name}'}, status=status.HTTP_400_BAD_REQUEST)
            
    except Datasource.DoesNotExist:
        return Response({'success': False, 'message': '数据源不存在'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.exception("编辑表数据时发生未知错误")
        return Response({'success': False, 'message': f'编辑表数据时发生未知错误: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def export_table_structure(request, datasource_id, table_name):
    """导出表结构"""
    try:
        # 获取数据源
        datasource = Datasource.objects.get(pk=datasource_id)
        
        # 获取schema参数
        schema = request.query_params.get('schema', None)
        
        # 检查用户是否有查询权限
        if not request.user.is_superuser:
            try:
                permission = DatasourcePermission.objects.get(datasource=datasource, user=request.user)
                if not permission or not permission.permission_type in ['read', 'write', 'admin']:
                    return Response({
                        'success': False,
                        'message': '没有权限导出表结构'
                    }, status=status.HTTP_403_FORBIDDEN)
            except DatasourcePermission.DoesNotExist:
                return Response({
                    'success': False,
                    'message': '没有权限导出表结构'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # 根据数据库类型执行不同的查询
        if datasource.db_type.code.lower() == 'mysql':
            # 查询创建表的SQL
            if schema:
                # 如果提供了schema，使用指定的schema
                sql = f"SHOW CREATE TABLE `{schema}`.`{table_name}`"
            else:
                # 没有提供schema，使用当前数据库
                sql = f"SHOW CREATE TABLE `{table_name}`"
            
            # 执行SQL查询
            result = execute_mysql_query(datasource, sql, request.user)
            
            if not result['success']:
                return Response({
                    'success': False,
                    'message': result['error_message']
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 记录审计日志
            # AuditLog.objects.create(
            #     user=request.user,
            #     action_type='export_structure',
            #     ip_address=request.META.get('REMOTE_ADDR'),
            #     datasource=datasource,
            #     content=f"用户 {request.user.username} 导出了表 {table_name} 的结构"
            # )
            
            # 获取CREATE TABLE语句
            create_table_sql = result['data'][0].get('Create Table', '')
            
            return Response({
                'success': True,
                'table_name': table_name,
                'schema': schema,
                'create_table_sql': create_table_sql
            })
        else:
            return Response({
                'success': False,
                'message': f'不支持的数据库类型: {datasource.db_type.name}'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Datasource.DoesNotExist:
        return Response({
            'success': False,
            'message': '数据源不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'message': f'导出表结构失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def export_table_data(request, datasource_id, table_name):
    """导出表数据"""
    try:
        # 获取数据源
        datasource = Datasource.objects.get(pk=datasource_id)
        
        # 获取schema参数
        schema = request.query_params.get('schema', None)
        
        # 检查用户是否有查询权限
        if not request.user.is_superuser:
            try:
                permission = DatasourcePermission.objects.get(datasource=datasource, user=request.user)
                if not permission or not permission.permission_type in ['read', 'write', 'admin']:
                    return Response({
                        'success': False,
                        'message': '没有权限导出表数据'
                    }, status=status.HTTP_403_FORBIDDEN)
            except DatasourcePermission.DoesNotExist:
                return Response({
                    'success': False,
                    'message': '没有权限导出表数据'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # 获取请求参数
        format_type = request.query_params.get('format', 'json')
        limit = int(request.query_params.get('limit', 1000))  # 默认限制1000行
        
        # 根据数据库类型执行不同的查询
        if datasource.db_type.code.lower() == 'mysql':
            # 查询表数据的SQL
            if schema:
                # 如果提供了schema，使用指定的schema
                sql = f"SELECT * FROM `{schema}`.`{table_name}` LIMIT {limit}"
            else:
                # 没有提供schema，使用当前数据库
                sql = f"SELECT * FROM `{table_name}` LIMIT {limit}"
            
            # 执行SQL查询
            result = execute_mysql_query(datasource, sql, request.user)
            
            if not result['success']:
                return Response({
                    'success': False,
                    'message': result['error_message']
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 记录审计日志
            # AuditLog.objects.create(
            #     user=request.user,
            #     action_type='export_data',
            #     ip_address=request.META.get('REMOTE_ADDR'),
            #     datasource=datasource,
            #     content=f"用户 {request.user.username} 导出了表 {table_name} 的数据"
            # )
            
            data = result['data']
            
            # 根据请求的格式返回数据
            if format_type.lower() == 'csv':
                if not data:
                    return Response({
                        'success': False,
                        'message': '没有数据可导出'
                    }, status=status.HTTP_404_NOT_FOUND)
                
                # 使用pandas导出为CSV
                df = pd.DataFrame(data)
                csv_data = df.to_csv(index=False)
                
                return Response({
                    'success': True,
                    'table_name': table_name,
                    'schema': schema,
                    'format': 'csv',
                    'data': csv_data
                })
            else:  # 默认为JSON格式
                return Response({
                    'success': True,
                    'table_name': table_name,
                    'schema': schema,
                    'format': 'json',
                    'columns': result['columns'],
                    'data': data
                })
        else:
            return Response({
                'success': False,
                'message': f'不支持的数据库类型: {datasource.db_type.name}'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Datasource.DoesNotExist:
        return Response({
            'success': False,
            'message': '数据源不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'message': f'导出表数据失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



# 辅助函数：从SQL提取表名
def extract_table_names_from_sql(sql):
    """
    简单地从SQL语句中提取表名
    """
    import re
    
    # 尝试匹配常见的SQL模式
    from_pattern = r'FROM\s+([`"\[]{0,1}[\w\d_\.]+[`"\]]{0,1})'
    into_pattern = r'INTO\s+([`"\[]{0,1}[\w\d_\.]+[`"\]]{0,1})'
    update_pattern = r'UPDATE\s+([`"\[]{0,1}[\w\d_\.]+[`"\]]{0,1})'
    join_pattern = r'JOIN\s+([`"\[]{0,1}[\w\d_\.]+[`"\]]{0,1})'
    alter_pattern = r'ALTER\s+TABLE\s+([`"\[]{0,1}[\w\d_\.]+[`"\]]{0,1})'
    desc_pattern = r'(?:DESC|DESCRIBE)\s+([`"\[]{0,1}[\w\d_\.]+[`"\]]{0,1})'
    
    tables = []
    
    # 查找所有匹配项
    for pattern in [from_pattern, into_pattern, update_pattern, join_pattern, alter_pattern, desc_pattern]:
        matches = re.finditer(pattern, sql, re.IGNORECASE)
        for match in matches:
            table_name = match.group(1)
            
            # 移除可能的引号和方括号
            table_name = table_name.replace('`', '').replace('"', '').replace('[', '').replace(']', '')
            
            # 处理可能包含schema的情况
            if '.' in table_name:
                table_name = table_name.split('.')[-1]
            
            tables.append(table_name)
    
    return list(set(tables))  # 去重

# 辅助函数：判断是否是写操作SQL
def is_write_sql(sql):
    """
    判断SQL是否是写操作
    """
    # 简单检查SQL类型
    sql_lower = sql.lower().strip()
    
    write_operations = [
        'insert', 'update', 'delete', 'truncate', 
        'create', 'alter', 'drop', 'grant'
    ]
    
    for op in write_operations:
        if sql_lower.startswith(op):
            return True
    
    return False

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_query_record(request, pk):
    """删除查询记录"""
    user = request.user
    
    try:
        query_record = Query.objects.get(pk=pk)
        
        # 权限检查: 只有超级管理员或记录创建者可以删除
        if not user.is_superuser and query_record.user != user:
            logger.warning(f"用户 {user.username} 尝试删除不属于他的查询记录 ID:{pk}")
            return Response({
                'success': False,
                'message': '您没有权限删除此查询记录'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # 记录审计日志
        # AuditLog.objects.create(
        #     user=user,
        #     action_type='delete_query_history',
        #     ip_address=request.META.get('REMOTE_ADDR'),
        #     content=f"用户 {request.user.username} 删除了查询历史: {query_record.sql_content[:60]}"
        # )
        
        # 删除记录
        query_record.delete()
        logger.info(f"用户 {user.username} 成功删除查询记录 ID:{pk}")
        
        return Response({
            'success': True,
            'message': '查询记录已成功删除'
        })
        
    except Query.DoesNotExist:
        logger.error(f"尝试删除不存在的查询记录 ID:{pk}")
        return Response({
            'success': False,
            'message': '查询记录不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.exception(f"删除查询记录时发生异常: {str(e)}")
        return Response({
            'success': False,
            'message': f'删除查询记录失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_database_tables(request):
    """获取数据库的表列表"""
    user = request.user
    datasource_id = request.query_params.get('datasource_id')
    schema = request.query_params.get('schema')
    
    if not datasource_id:
        return Response({
            'success': False,
            'message': '缺少datasource_id参数'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        datasource = Datasource.objects.get(pk=datasource_id)
        
        # 检查用户权限
        if not (user.is_superuser or user.is_staff):
            try:
                permission = DatasourcePermission.objects.get(datasource=datasource, user=user)
            except DatasourcePermission.DoesNotExist:
                return Response({
                    'success': False,
                    'message': '您没有权限访问该数据源'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # 通过执行查询获取表列表
        conn = None
        try:
            # 连接数据库
            conn = pymysql.connect(
                host=datasource.host,
                port=int(datasource.port),
                user=datasource.username,
                password=datasource.password,
                database=datasource.database,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                connect_timeout=10
            )
            
            with conn.cursor() as cursor:
                if schema:
                    cursor.execute(f"USE `{schema}`")
                
                # 获取表列表的SQL（不同数据库可能需要不同的查询）
                if datasource.db_type.name.lower() == 'mysql':
                    query = "SHOW TABLES"
                else:
                    # 对于其他未知数据库类型，返回空列表
                    return Response([])
                
                cursor.execute(query)
                tables = cursor.fetchall()
                
                # 格式化结果
                result = []
                for table in tables:
                    # 表名可能在不同的键中，取决于数据库
                    table_name = list(table.values())[0] if table else None
                    if table_name:
                        result.append({
                            'name': table_name,
                            'type': 'table'
                        })
                
                return Response(result)
        
        except Exception as e:
            return Response({
                'success': False,
                'message': f'获取表列表失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        finally:
            if conn:
                conn.close()
    
    except Datasource.DoesNotExist:
        return Response({
            'success': False,
            'message': '数据源不存在'
        }, status=status.HTTP_404_NOT_FOUND)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_table_columns(request):
    """获取表的字段列表"""
    user = request.user
    datasource_id = request.query_params.get('datasource_id')
    table_name = request.query_params.get('table_name')
    schema = request.query_params.get('schema')
    
    if not datasource_id or not table_name:
        return Response({
            'success': False,
            'message': '缺少必要参数'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        datasource = Datasource.objects.get(pk=datasource_id)
        
        # 检查用户权限
        if not (user.is_superuser or user.is_staff):
            try:
                permission = DatasourcePermission.objects.get(datasource=datasource, user=user)
            except DatasourcePermission.DoesNotExist:
                return Response({
                    'success': False,
                    'message': '您没有权限访问该数据源'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # 通过执行查询获取字段列表
        conn = None
        try:
            # 连接数据库
            conn = pymysql.connect(
                host=datasource.host,
                port=int(datasource.port),
                user=datasource.username,
                password=datasource.password,
                database=datasource.database,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                connect_timeout=10
            )
            
            with conn.cursor() as cursor:
                if schema:
                    cursor.execute(f"USE `{schema}`")
                
                # 获取字段列表的SQL（不同数据库可能需要不同的查询）
                if datasource.db_type.name.lower() == 'mysql':
                    query = f"DESCRIBE `{table_name}`"
                else:
                    # 对于其他未知数据库类型，返回空列表
                    return Response([])
                
                cursor.execute(query)
                columns = cursor.fetchall()
                
                # 格式化结果
                result = []
                for column in columns:
                    if datasource.db_type.name.lower() == 'mysql':
                        result.append({
                            'name': column.get('Field'),
                            'type': column.get('Type'),
                            'nullable': column.get('Null') == 'YES',
                            'key': column.get('Key'),
                            'default': column.get('Default'),
                            'extra': column.get('Extra')
                        })
                
                return Response(result)
        
        except Exception as e:
            return Response({
                'success': False,
                'message': f'获取字段列表失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        finally:
            if conn:
                conn.close()
    
    except Datasource.DoesNotExist:
        return Response({
            'success': False,
            'message': '数据源不存在'
        }, status=status.HTTP_404_NOT_FOUND)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_database_full_metadata(request):
    """
    一次性获取数据源的完整元数据（所有schemas、tables和columns）。
    这是一个优化接口，旨在减少前端的请求次数。
    """
    user = request.user
    datasource_id = request.query_params.get('datasource_id')

    if not datasource_id:
        return Response({'success': False, 'message': '缺少datasource_id参数'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        datasource = Datasource.objects.get(pk=datasource_id)
        
        # 权限检查
        if not (user.is_superuser or user.is_staff or DatasourcePermission.objects.filter(datasource=datasource, user=user).exists()):
            return Response({'success': False, 'message': '您没有权限访问该数据源'}, status=status.HTTP_403_FORBIDDEN)
        
        conn = None
        try:
            conn = pymysql.connect(
                host=datasource.host,
                port=int(datasource.port),
                user=datasource.username,
                password=datasource.password,
                database=datasource.database,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                connect_timeout=20 # 增加超时时间
            )
            
            with conn.cursor() as cursor:
                # 根据数据库类型选择不同的高效查询
                if datasource.db_type.name.lower() == 'mysql':
                    # 排除系统库
                    excluded_schemas = "('mysql', 'information_schema', 'performance_schema', 'sys')"
                    query = f"""
                        SELECT
                            c.TABLE_SCHEMA as `schema`,
                            c.TABLE_NAME as `table`,
                            c.COLUMN_NAME as `column`,
                            c.COLUMN_TYPE as `type`
                        FROM information_schema.COLUMNS c
                        JOIN information_schema.TABLES t ON c.TABLE_SCHEMA = t.TABLE_SCHEMA AND c.TABLE_NAME = t.TABLE_NAME
                        WHERE t.TABLE_TYPE = 'BASE TABLE' AND c.TABLE_SCHEMA NOT IN {excluded_schemas}
                        ORDER BY `schema`, `table`, c.ORDINAL_POSITION;
                    """
                else:
                    return Response({'success': False, 'message': '暂不支持该数据库类型的高效元数据获取'}, status=status.HTTP_400_BAD_REQUEST)

                cursor.execute(query)
                rows = cursor.fetchall()
                
                # 将扁平数据转换为嵌套结构
                # 使用 defaultdict 简化嵌套字典的创建
                # 结构: { schema: { table: [ {name: col, type: type}, ... ] } }
                from collections import defaultdict
                result = defaultdict(lambda: defaultdict(list))
                for row in rows:
                    result[row['schema']][row['table']].append({
                        'name': row['column'],
                        'type': row['type']
                    })
                
                return Response(result)

        except Exception as e:
            return Response({'success': False, 'message': f'获取元数据失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        finally:
            if conn:
                conn.close()

    except Datasource.DoesNotExist:
        return Response({'success': False, 'message': '数据源不存在'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def execute_ddl(request):
    """
    直接执行DDL语句 (如 ALTER TABLE).
    这是一个高风险操作，需要严格的权限检查.
    """
    datasource_id = request.data.get('datasource_id')
    schema = request.data.get('schema')
    sql = request.data.get('sql_content')

    logger.info(f"执行DDL: datasource_id={datasource_id}, schema={schema}, sql='{sql[:100]}...'")

    if not all([datasource_id, schema, sql]):
        return Response({'success': False, 'error': '缺少必要参数 (datasource_id, schema, sql_content)'}, status=status.HTTP_400_BAD_REQUEST)

    table_names = extract_table_names_from_sql(sql)
    if not table_names:
        return Response({'success': False, 'error': '无法从SQL中解析出表名，操作被拒绝'}, status=status.HTTP_400_BAD_REQUEST)

    for table_name in table_names:
        if not check_table_permission(request.user, datasource_id, table_name, schema, 'write'):
            return Response({'success': False, 'error': f'用户没有对表 {schema}.{table_name} 的写入权限'}, status=status.HTTP_403_FORBIDDEN)

    try:
        datasource = Datasource.objects.get(pk=datasource_id)
    except Datasource.DoesNotExist:
        return Response({'success': False, 'error': '数据源未找到'}, status=status.HTTP_404_NOT_FOUND)

    if datasource.db_type.code.lower() != 'mysql':
        return Response({'success': False, 'error': f'该功能目前仅支持MySQL: {datasource.db_type.name}'}, status=status.HTTP_400_BAD_REQUEST)

    # 处理SQL语句，确保不会因为末尾分号导致语法错误
    sql_to_execute = sql.strip()

    conn = None
    try:
        conn = pymysql.connect(
            host=datasource.host,
            port=int(datasource.port),
            user=datasource.username,
            password=datasource.password,
            database=schema,
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        with conn.cursor() as cursor:
            affected_rows = cursor.execute(sql_to_execute)
            conn.commit()

            AuditLog.objects.create(
                user=request.user,
                action_type='execute_ddl',
                ip_address=request.META.get('REMOTE_ADDR'),
                datasource=datasource,
                content=f"执行DDL ({schema}): {sql}",
                risk_level='high'
            )

            return Response({'success': True, 'message': '表结构更新成功', 'affected_rows': affected_rows})

    except pymysql.Error as e:
        logger.error(f"执行DDL失败: {str(e)}")
        if conn:
            conn.rollback()
        return Response({'success': False, 'error': f'数据库错误: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        logger.error(f"执行DDL时发生未知错误: {str(e)}")
        if conn:
            conn.rollback()
        return Response({'success': False, 'error': f'执行DDL失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    finally:
        if conn:
            conn.close()
    