"""
数据库连接池管理模块
提供数据库连接的创建、复用、管理功能
"""
import time
import logging
import threading
import pymysql
import clickhouse_driver
from contextlib import contextmanager
from collections import defaultdict
from .models import Datasource
from .adapters import get_adapter # 导入统一的适配器工厂函数

logger = logging.getLogger(__name__)

class ConnectionWrapper:
    """连接包装器，记录连接的使用信息"""
    def __init__(self, connection, db_type):
        self.connection = connection
        self.db_type = db_type
        self.last_used = time.time()
        self.in_use = False
        self.created_at = time.time()

    def close(self):
        """关闭实际的数据库连接"""
        try:
            self.connection.close()
        except Exception as e:
            logger.error(f"关闭连接出错: {e}")

    def is_expired(self, max_idle_time=300):
        """检查连接是否超时"""
        return (time.time() - self.last_used) > max_idle_time

    def ping(self):
        """检查连接是否有效"""
        try:
            if self.db_type.lower() == 'mysql':
                self.connection.ping(reconnect=False)
            elif self.db_type.lower() == 'clickhouse':
                try:
                    # 尝试执行简单查询
                    self.connection.execute('SELECT 1')
                except Exception as e:
                    logger.warning(f"ClickHouse ping失败: {str(e)}")
                    # 可能是连接已断开，但我们在这里不重试，让调用方处理
                    raise
            return True
        except Exception as e:
            logger.error(f"连接健康检查失败: {e}")
            return False


class ConnectionPool:
    """数据库连接池，管理多个数据源的连接池"""
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ConnectionPool, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        if self._initialized:
            return
        
        self._pools = defaultdict(list)  # datasource_id -> 连接列表
        self._lock = threading.Lock()
        self._initialized = True
        
        # 配置
        self.min_connections = 2      # 每个数据源的最小连接数
        self.max_connections = 10     # 每个数据源的最大连接数
        self.max_idle_time = 300      # 连接最大空闲时间（秒）
        
        # 状态缓存
        self._status_cache = {}       # 存储数据源连接状态的缓存
        self._status_cache_lock = threading.Lock()  # 缓存锁
        self._last_status_check = {}  # 上次状态检查时间
        
        # 启动连接池维护线程
        self._maintenance_thread = threading.Thread(
            target=self._maintenance_task, 
            daemon=True
        )
        self._maintenance_thread.start()
        
        # 启动数据库状态检测线程
        self._heartbeat_thread = threading.Thread(
            target=self._heartbeat_task,
            daemon=True
        )
        self._heartbeat_thread.start()
        
        logger.info("数据库连接池初始化完成")

    def _create_connection(self, datasource):
        """创建新的数据库连接（已重构为使用适配器）"""
        try:
            logger.info(f"通过适配器创建新的数据库连接: {datasource.name} ({datasource.db_type.code})")
            adapter = get_adapter(datasource)
            # 调用适配器的connect方法，它会处理所有连接细节
            connection_object = adapter.connect() 
            db_type = datasource.db_type.code.lower()
            
            # ConnectionWrapper 现在可能包装的是适配器实例或原始连接对象
            return ConnectionWrapper(connection_object, db_type)
        except Exception as e:
            logger.error(f"通过适配器创建数据库连接失败: {datasource.name} - {e}")
            raise

    @contextmanager
    def get_connection(self, datasource):
        """获取一个数据库连接，使用上下文管理器模式"""
        conn_wrapper = None
        datasource_id = datasource.id
        
        try:
            with self._lock:
                # 查找可用连接
                available_connections = [
                    c for c in self._pools[datasource_id] 
                    if not c.in_use and not c.is_expired(self.max_idle_time)
                ]
                
                if available_connections:
                    conn_wrapper = available_connections[0]
                    if not conn_wrapper.ping():
                        # 连接已失效，移除并创建新连接
                        self._pools[datasource_id].remove(conn_wrapper)
                        conn_wrapper.close()
                        conn_wrapper = self._create_connection(datasource)
                        self._pools[datasource_id].append(conn_wrapper)
                else:
                    # 无可用连接，检查是否可以创建新连接
                    if len(self._pools[datasource_id]) < self.max_connections:
                        conn_wrapper = self._create_connection(datasource)
                        self._pools[datasource_id].append(conn_wrapper)
                    else:
                        # 池已满，等待连接释放（简单实现，可优化）
                        raise RuntimeError("连接池已满，无法获取新连接")
                
                conn_wrapper.in_use = True
                conn_wrapper.last_used = time.time()
            
            yield conn_wrapper.connection
            
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            raise
        finally:
            if conn_wrapper:
                with self._lock:
                    conn_wrapper.in_use = False
                    conn_wrapper.last_used = time.time()

    def _maintenance_task(self):
        """维护连接池的后台任务，定期清理过期连接"""
        while True:
            try:
                time.sleep(60)  # 每分钟执行一次
                self._cleanup_expired_connections()
                self._ensure_min_connections()
            except Exception as e:
                logger.error(f"连接池维护任务出错: {e}")

    def _heartbeat_task(self):
        """数据库心跳检测后台任务，每分钟检测一次所有数据源的状态"""
        while True:
            try:
                time.sleep(60)  # 每分钟执行一次
                self._check_all_datasources_status()
            except Exception as e:
                logger.error(f"心跳检测任务出错: {e}")

    def _check_all_datasources_status(self):
        """检测所有数据源的状态并更新缓存"""
        try:
            # 获取所有数据源
            datasources = Datasource.objects.all()
            
            for datasource in datasources:
                datasource_id = datasource.id
                
                # 检测单个数据源状态
                status = self._check_datasource_status(datasource)
                
                # 更新状态缓存
                with self._status_cache_lock:
                    self._status_cache[datasource_id] = {
                        'status': status,
                        'last_check': time.time(),
                        'name': datasource.name,
                        'type': datasource.db_type.name if datasource.db_type else None,
                        'host': datasource.host,
                        'port': datasource.port
                    }
                
                # 记录上次检查时间
                self._last_status_check[datasource_id] = time.time()
                
            logger.debug(f"完成所有数据源状态检测，共 {len(datasources)} 个数据源")
            
        except Exception as e:
            logger.error(f"检测数据源状态时出错: {e}")

    def _check_datasource_status(self, datasource):
        """检测单个数据源的状态（已重构为使用适配器）"""
        try:
            logger.info(f"通过适配器检测数据源状态: {datasource.name} ({datasource.db_type.code})")
            # 尝试使用适配器获取连接
            adapter = get_adapter(datasource)
            connection = adapter.connect()
            
            # 如果连接成功，我们认为状态是 'ok'
            # 对于返回适配器实例的情况，我们可能需要一个通用的ping方法
            # 但connect()成功本身就是一个强有力的信号
            
            # 如果是ClickHouse，我们已经连接上了，可以关闭（如果它有close方法）
            if hasattr(connection, 'close'):
                try:
                    connection.close()
                except:
                    pass # 忽略关闭错误

            logger.info(f"数据源 {datasource.name} 连接成功，状态: ok")
            return 'ok'
        except Exception as e:
            logger.warning(f"数据源 {datasource.name} 连接失败: {str(e)}")
            return 'error'

    def _cleanup_expired_connections(self):
        """清理过期的连接"""
        with self._lock:
            for datasource_id, connections in list(self._pools.items()):
                # 找出过期且未使用的连接
                expired = [
                    c for c in connections 
                    if not c.in_use and c.is_expired(self.max_idle_time)
                ]
                
                # 保留最小连接数
                while len(expired) > 0 and len(connections) - len(expired) >= self.min_connections:
                    conn = expired.pop()
                    connections.remove(conn)
                    conn.close()
                    logger.debug(f"关闭过期连接: datasource_id={datasource_id}")

    def _ensure_min_connections(self):
        """确保每个数据源有最小数量的连接"""
        try:
            # 获取活跃的数据源
            datasources = Datasource.objects.filter(status='active')
            
            for datasource in datasources:
                with self._lock:
                    current_count = len(self._pools[datasource.id])
                    
                    # 如果连接数小于最小值，创建新连接
                    if current_count < self.min_connections:
                        try:
                            for _ in range(self.min_connections - current_count):
                                conn_wrapper = self._create_connection(datasource)
                                self._pools[datasource.id].append(conn_wrapper)
                                logger.debug(f"为数据源{datasource.id}创建新连接")
                        except Exception as e:
                            logger.error(f"为数据源{datasource.id}预创建连接失败: {e}")
        
        except Exception as e:
            logger.error(f"维护最小连接数时出错: {e}")

    def get_pool_status(self, datasource_id=None):
        """获取连接池状态信息"""
        with self._lock:
            if datasource_id is not None:
                connections = self._pools[datasource_id]
                return {
                    'total': len(connections),
                    'in_use': sum(1 for c in connections if c.in_use),
                    'idle': sum(1 for c in connections if not c.in_use)
                }
            else:
                status = {}
                for ds_id, connections in self._pools.items():
                    status[ds_id] = {
                        'total': len(connections),
                        'in_use': sum(1 for c in connections if c.in_use),
                        'idle': sum(1 for c in connections if not c.in_use)
                    }
                return status

    def get_all_datasources_status(self):
        """获取所有数据源的状态信息（从缓存）"""
        with self._status_cache_lock:
            return {k: v.copy() for k, v in self._status_cache.items()}

    def get_datasource_status(self, datasource_id):
        """获取指定数据源的状态信息（从缓存）"""
        with self._status_cache_lock:
            return self._status_cache.get(datasource_id, {'status': 'unknown', 'last_check': 0}).copy()

    def close_all(self):
        """关闭所有连接池中的连接"""
        with self._lock:
            for datasource_id, connections in self._pools.items():
                for conn in connections:
                    try:
                        conn.close()
                    except:
                        pass
            self._pools.clear()


# 全局单例
connection_pool = ConnectionPool() 