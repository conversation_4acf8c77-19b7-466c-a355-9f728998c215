"""
数据库适配器模块
为不同类型的数据库提供统一的接口
"""

import logging
import pymysql
import clickhouse_driver
import redis
import json
import datetime
import decimal
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

def get_adapter(datasource):
    """
    根据数据源获取对应的适配器
    """
    try:
        db_type = datasource.db_type.code.lower()
        
        if db_type == 'mysql':
            return MySQLAdapter(datasource)
        elif db_type == 'clickhouse':
            return ClickHouseAdapter(datasource)
        elif db_type == 'redis':
            return RedisAdapter(datasource)
        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")
    except Exception as e:
        logger.error(f"创建数据库适配器失败: {str(e)}")
        raise Exception(f"无法连接到数据库: {str(e)}")

class DatabaseAdapter(ABC):
    """数据库适配器基类"""
    
    def __init__(self, datasource):
        self.datasource = datasource
    
    @abstractmethod
    def connect(self):
        """连接数据库"""
        pass
    
    @abstractmethod
    def execute_query(self, sql, schema=None):
        """执行SQL查询"""
        pass
    
    @abstractmethod
    def get_schemas(self):
        """获取所有模式列表"""
        pass
    
    @abstractmethod
    def get_schema(self, schema_name):
        """获取指定模式的结构"""
        pass
        
    def cancel_query(self, process_id=None, query_hash=None):
        """取消正在执行的查询，子类可以覆盖此方法实现具体的取消逻辑
        
        参数:
            process_id: 进程ID，如果提供则只终止该进程
            query_hash: 查询哈希值，如果提供则只终止匹配该哈希的查询
        """
        logger.warning(f"取消查询功能未在 {self.__class__.__name__} 中实现")
        return {"success": False, "message": "不支持取消查询操作"}
    
    def _process_result_for_json(self, data):
        """处理结果使其可以被JSON序列化，特别是将长整型和Decimal转换为字符串以避免JS精度丢失"""
        # JS的最大安全整数
        MAX_SAFE_INTEGER = 9007199254740991
        
        try:
            if isinstance(data, dict):
                return {k: self._process_result_for_json(v) for k, v in data.items()}
            elif isinstance(data, list):
                return [self._process_result_for_json(item) for item in data]
            elif isinstance(data, tuple):
                return [self._process_result_for_json(item) for item in data]
            elif isinstance(data, datetime.datetime):
                return data.strftime('%Y-%m-%d %H:%M:%S')
            elif isinstance(data, datetime.date):
                return data.isoformat()
            # 增加对Decimal类型的处理
            elif isinstance(data, decimal.Decimal):
                return str(data)
            elif isinstance(data, int):
                # 检查整数是否超过JavaScript的安全范围
                if abs(data) > MAX_SAFE_INTEGER:
                    return str(data)
                return data
            elif isinstance(data, (float, str, bool, type(None))):
                return data
            else:
                # 对于其他类型，尝试转换为字符串
                try:
                    # 尝试使用json.dumps进行序列化
                    import json
                    return json.dumps(data, ensure_ascii=False)
                except:
                    # 如果序列化失败，使用字符串表示
                    return str(data)
        except Exception as e:
            logger.error(f"处理结果数据时出错: {e}")
            # 返回安全的字符串表示
            return f"[无法处理的数据类型: {type(data).__name__}]"

class MySQLAdapter(DatabaseAdapter):
    """MySQL数据库适配器"""
    
    def connect(self):
        """连接到MySQL数据库"""
        try:
            # 根据操作类型选择合适的游标类
            conn_params = {
                'host': self.datasource.host,
                'port': int(self.datasource.port),
                'user': self.datasource.username,
                'password': self.datasource.password,
                'charset': 'utf8mb4',
                'connect_timeout': 10
            }
            
            # 如果有数据库名，添加到连接参数
            if self.datasource.database:
                conn_params['database'] = self.datasource.database
            
            # 使用通用的连接方式，不指定游标类型
            conn = pymysql.connect(**conn_params)
            logger.info(f"成功连接到MySQL数据库: {self.datasource.host}:{self.datasource.port}")
            return conn
        except Exception as e:
            error_msg = str(e)
            logger.error(f"MySQL连接失败: {error_msg}")
            
            # 添加更友好的错误消息
            if "Access denied" in error_msg:
                raise Exception(f"访问被拒绝，请检查用户名和密码: {error_msg}")
            elif "Unknown database" in error_msg:
                raise Exception(f"数据库不存在: {error_msg}")
            elif "Can't connect" in error_msg or "Connection refused" in error_msg:
                raise Exception(f"无法连接到数据库服务器，请检查主机名和端口: {error_msg}")
            elif "timed out" in error_msg:
                raise Exception(f"连接超时，请检查网络或服务器状态: {error_msg}")
            else:
                raise
    
    def execute_query(self, sql, schema=None):
        """执行SQL查询"""
        conn = None
        try:
            conn = self.connect()
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            # 如果前端提供了schema，则使用它
            if schema:
                logger.info(f"MySQLAdapter: Switching to schema '{schema}'")
                cursor.execute(f"USE `{schema}`")
            
            # 检查是否为终止查询的特殊标记
            if '/* KILL_QUERY_FLAG */' in sql:
                logger.info("正在执行终止查询操作")
                # 获取所有正在运行的进程
                cursor.execute("SHOW PROCESSLIST")
                processes = cursor.fetchall()
                
                # 过滤出当前用户的查询进程（非系统进程）
                user_processes = [p for p in processes if p.get('User') == self.datasource.username and p.get('Command') != 'Sleep']
                
                if user_processes:
                    killed_count = 0
                    for process in user_processes:
                        try:
                            process_id = process.get('Id')
                            logger.info(f"正在终止进程 ID: {process_id}")
                            cursor.execute(f"KILL QUERY {process_id}")
                            killed_count += 1
                        except Exception as e:
                            logger.error(f"终止进程 {process.get('Id')} 时出错: {e}")
                    
                    return {
                        'success': True,
                        'message': f'已终止 {killed_count} 个查询进程',
                        'columns': ['message'],
                        'rows': [{'message': f'已终止 {killed_count} 个查询进程'}],
                        'data': [{'message': f'已终止 {killed_count} 个查询进程'}],
                        'affected_rows': killed_count
                    }
                else:
                    return {
                        'success': True,
                        'message': '没有找到可终止的查询进程',
                        'columns': ['message'],
                        'rows': [{'message': '没有找到可终止的查询进程'}],
                        'data': [{'message': '没有找到可终止的查询进程'}],
                        'affected_rows': 0
                    }
            
            # 正常执行SQL
            affected_rows = cursor.execute(sql)
            
            # 对于SELECT查询，获取结果
            if sql.strip().lower().startswith(('select', 'show', 'describe', 'explain')):
                rows = cursor.fetchall()
                columns = []
                
                if rows:
                    # 获取列名
                    columns = list(rows[0].keys())
                    
                    # 处理结果以便JSON序列化
                    rows = [self._process_result_for_json(row) for row in rows]
                
                return {
                    'success': True,
                    'columns': columns,
                    'rows': rows,
                    'data': rows,  # 同时返回两种格式保持兼容
                    'affected_rows': affected_rows
                }
            else:
                # 非查询类SQL，返回影响的行数
                conn.commit()
                return {
                    'success': True,
                    'columns': ['affected_rows'],
                    'rows': [{'affected_rows': affected_rows}],
                    'data': [{'affected_rows': affected_rows}],  # 同时返回两种格式保持兼容
                    'affected_rows': affected_rows
                }
        except Exception as e:
            if conn:
                try:
                    conn.rollback()
                except:
                    pass  # 忽略回滚错误
            logger.error(f"执行SQL查询失败: {str(e)}")
            # 错误信息添加诊断信息
            error_msg = f"SQL执行错误: {str(e)}"
            if "Unknown database" in str(e):
                error_msg += " - 请检查数据库名是否正确"
            elif "Access denied" in str(e):
                error_msg += " - 请检查数据库访问权限"
            elif "Table" in str(e) and "doesn't exist" in str(e):
                error_msg += " - 请检查表名是否正确"
            elif "Unknown column" in str(e):
                error_msg += " - 请检查列名是否正确"
            elif "Syntax error" in str(e):
                error_msg += " - 请检查SQL语法是否正确"
            
            raise Exception(error_msg)
        finally:
            if conn:
                try:
                    conn.close()
                except:
                    pass  # 忽略关闭错误
    
    def get_schemas(self):
        """获取MySQL所有数据库列表"""
        try:
            conn = self.connect()
            cursor = conn.cursor()
            
            # 查询MySQL所有数据库
            cursor.execute("SHOW DATABASES")
            result = cursor.fetchall()
            
            # 调试日志
            logger.info(f"获取MySQL数据库列表结果类型: {type(result)}")
            if result and len(result) > 0:
                logger.info(f"第一个结果类型: {type(result[0])}, 内容: {result[0]}")
            
            # 提取数据库名称 - 修复元组索引访问
            schemas = []
            for item in result:
                if isinstance(item, dict):
                    # DictCursor模式
                    if 'Database' in item:
                        schemas.append(item['Database'])
                    elif 'database' in item:
                        schemas.append(item['database'])
                elif isinstance(item, (list, tuple)):
                    # 标准游标模式 - 使用整数索引而不是字符串
                    schemas.append(item[0])
                else:
                    # 未知类型，使用字符串转换
                    schemas.append(str(item))
            
            cursor.close()
            conn.close()
            
            return schemas
        except Exception as e:
            logger.error(f"获取MySQL数据库列表失败: {e}")
            if hasattr(e, '__dict__'):
                logger.error(f"错误详情: {e.__dict__}")
            raise
    
    def get_schema(self, schema_name):
        """获取MySQL数据库结构"""
        try:
            conn = self.connect()
            cursor = conn.cursor()
            
            # 如果没有指定schema，使用当前数据库
            if not schema_name:
                schema_name = self.datasource.database
            
            # 添加详细的调试日志
            logger.info(f"开始获取MySQL数据库结构，模式: {schema_name}")
            
            try:
                # 获取表和视图结构，使用单次查询提高效率
                cursor.execute(f"""
                    SELECT 
                        table_name, 
                        table_type
                    FROM 
                        information_schema.tables 
                    WHERE 
                        table_schema = '{schema_name}'
                """)
                table_results = cursor.fetchall()
                
                # 调试日志
                logger.info(f"获取到 {len(table_results)} 个表/视图对象")
                if table_results and len(table_results) > 0:
                    logger.info(f"第一个表/视图结果示例: {table_results[0]}")
                
                tables = []
                views = []
                
                # 处理表和视图
                for item in table_results:
                    # 确保使用正确的方式获取表名和类型
                    if isinstance(item, dict):
                        # DictCursor模式
                        table_name = item.get('table_name', item.get('TABLE_NAME'))
                        table_type = item.get('table_type', item.get('TABLE_TYPE'))
                    elif isinstance(item, (list, tuple)):
                        # 标准游标模式
                        table_name = item[0]
                        table_type = item[1]
                    else:
                        # 未知类型，跳过
                        logger.warning(f"未知的表/视图结果类型: {type(item)}, 值: {item}")
                        continue
                    
                    # 确保我们有有效的表名
                    if not table_name:
                        logger.warning(f"无法从结果中获取表名: {item}")
                        continue
                    
                    # 基于类型处理表或视图
                    if table_type and 'BASE TABLE' in table_type:
                        # 避免为每个表运行COUNT查询，该操作对大表非常耗时
                        # 改为使用information_schema中的估算值
                        try:
                            cursor.execute(f"""
                                SELECT 
                                    table_rows as row_count
                                FROM 
                                    information_schema.tables
                                WHERE 
                                    table_schema = '{schema_name}'
                                    AND table_name = '{table_name}'
                            """)
                            row_count_result = cursor.fetchone()
                            
                            # 同样处理结果类型差异
                            if isinstance(row_count_result, dict):
                                row_count = row_count_result.get('row_count', 0)
                            elif isinstance(row_count_result, (list, tuple)) and len(row_count_result) > 0:
                                row_count = row_count_result[0]
                            else:
                                row_count = 0
                        except Exception as e:
                            logger.warning(f"获取表 {table_name} 行数时出错: {e}")
                            row_count = 0
                        
                        tables.append({
                            'name': table_name,
                            'row_count': row_count
                        })
                    elif table_type and 'VIEW' in table_type:
                        views.append({'name': table_name})
                
                # 批量获取存储过程和函数
                procedures = []
                functions = []
                
                try:
                    # 一次性获取所有存储过程和函数
                    cursor.execute(f"""
                        SELECT 
                            routine_name,
                            routine_type
                        FROM 
                            information_schema.routines
                        WHERE 
                            routine_schema = '{schema_name}'
                    """)
                    routine_results = cursor.fetchall()
                    
                    # 调试日志
                    logger.info(f"获取到 {len(routine_results)} 个例程(函数/存储过程)")
                    
                    # 处理存储过程和函数
                    for routine in routine_results:
                        if isinstance(routine, dict):
                            # DictCursor模式
                            routine_name = routine.get('routine_name', routine.get('ROUTINE_NAME'))
                            routine_type = routine.get('routine_type', routine.get('ROUTINE_TYPE'))
                        elif isinstance(routine, (list, tuple)):
                            # 标准游标模式
                            routine_name = routine[0]
                            routine_type = routine[1]
                        else:
                            # 未知类型，跳过
                            continue
                        
                        if not routine_name:
                            continue
                        
                        if routine_type and 'PROCEDURE' in routine_type:
                            procedures.append({'name': routine_name})
                        elif routine_type and 'FUNCTION' in routine_type:
                            functions.append({'name': routine_name})
                except Exception as e:
                    logger.warning(f"获取存储过程和函数时出错: {e}")
                    # 继续处理，不要因为这里的错误中断整个流程
                
                cursor.close()
                conn.close()
                
                # 构建最终的结果对象
                result = {
                    'name': schema_name,
                    'tables': tables,
                    'views': views,
                    'procedures': procedures,
                    'functions': functions
                }
                
                # 记录成功
                logger.info(f"成功获取MySQL结构: {len(tables)}表, {len(views)}视图, {len(procedures)}存储过程, {len(functions)}函数")
                
                return result
                
            except Exception as inner_e:
                logger.error(f"获取MySQL数据库结构内部错误: {inner_e}")
                # 尝试记录详细错误
                if hasattr(inner_e, '__dict__'):
                    logger.error(f"错误详情: {inner_e.__dict__}")
                raise inner_e
                
        except Exception as e:
            logger.error(f"获取MySQL数据库结构失败: {e}")
            if hasattr(e, '__dict__'):
                logger.error(f"错误详情: {e.__dict__}")
            raise

    def debug_table_structure(self, schema_name):
        """调试表结构方法，用于检查返回的数据格式"""
        if not schema_name:
            schema_name = self.datasource.database
            
        conn = self.connect()
        cursor = conn.cursor()
        
        # 查询表结构
        cursor.execute(f"""
            SELECT 
                table_name, 
                table_type
            FROM 
                information_schema.tables 
            WHERE 
                table_schema = '{schema_name}'
            LIMIT 5
        """)
        table_results = cursor.fetchall()
        
        # 输出结果类型和内容
        logger.info(f"表结构类型: {type(table_results)}")
        logger.info(f"表结构内容: {table_results}")
        
        if table_results and len(table_results) > 0:
            first_row = table_results[0]
            logger.info(f"第一行类型: {type(first_row)}")
            logger.info(f"第一行内容: {first_row}")
            
            if hasattr(first_row, '__getitem__'):
                logger.info(f"第一行第一个元素: {first_row[0]}")
                if len(first_row) > 1:
                    logger.info(f"第一行第二个元素: {first_row[1]}")
        
        cursor.close()
        conn.close()
        
        return {
            'success': True,
            'type': str(type(table_results)),
            'sample': table_results[:2] if table_results else []
        }

    def cancel_query(self, process_id=None, query_hash=None):
        """取消MySQL正在执行的查询进程
        
        参数:
            process_id: 进程ID，如果提供则只终止该进程
            query_hash: 查询哈希值，如果提供则只终止匹配该哈希的查询
        """
        conn = None
        try:
            conn = self.connect()
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            # 如果提供了具体的进程ID，则直接终止该进程
            if process_id:
                logger.info(f"正在终止MySQL进程 ID: {process_id}")
                try:
                    cursor.execute(f"KILL QUERY {process_id}")
                    return {
                        'success': True,
                        'message': f'已终止进程 ID: {process_id}',
                    }
                except Exception as e:
                    # 忽略 "Query execution was interrupted" 错误，这实际上表示成功
                    if "Query execution was interrupted" in str(e):
                        logger.info(f"进程 {process_id} 已成功终止 (错误信息表示成功: {e})")
                        return {
                            'success': True,
                            'message': f'已终止进程 ID: {process_id}',
                        }
                    else:
                        raise
            
            # 尝试直接从information_schema.PROCESSLIST获取进程
            try:
                # 首先尝试使用information_schema.PROCESSLIST获取运行中的查询
                # 这通常比SHOW PROCESSLIST提供更完整的信息
                cursor.execute(f"""
                    SELECT ID, USER, COMMAND, TIME, STATE, INFO 
                    FROM information_schema.PROCESSLIST 
                    WHERE USER = '{self.datasource.username}'
                    AND COMMAND != 'Sleep'
                """)
                processes = cursor.fetchall()
                # 使用标准字段名称，与SHOW PROCESSLIST保持一致
                processes = [{
                    'Id': p['ID'], 
                    'User': p['USER'],
                    'Command': p['COMMAND'],
                    'Time': p['TIME'],
                    'State': p['STATE'],
                    'Info': p['INFO']
                } for p in processes]
                logger.info(f"通过information_schema.PROCESSLIST获取到 {len(processes)} 个进程")
            except Exception as e:
                logger.warning(f"从information_schema获取进程列表失败: {e}，尝试使用SHOW PROCESSLIST")
                # 如果information_schema访问失败，回退到SHOW PROCESSLIST
                cursor.execute("SHOW PROCESSLIST")
                processes = cursor.fetchall()
                logger.info(f"通过SHOW PROCESSLIST获取到 {len(processes)} 个进程")
            
            # 过滤出当前用户的查询进程（非系统进程）
            user_processes = [p for p in processes if p.get('User') == self.datasource.username and p.get('Command') != 'Sleep']
            
            if user_processes:
                killed_count = 0
                
                # 如果提供了查询哈希值，则尝试终止匹配的查询
                if query_hash:
                    logger.info(f"使用查询哈希值终止特定查询: {query_hash}")
                    
                try:
                  
                    
                    # 尝试从取消请求中获取原始SQL
                    orig_sql = None
                    for process in user_processes:
                        if process.get('Info') and '/* KILL_QUERY_FLAG */' in process.get('Info'):
                            # 从取消查询请求中提取原始SQL（去掉KILL_QUERY_FLAG）
                            orig_sql = process.get('Info').replace('/* KILL_QUERY_FLAG */', '').strip()
                            if orig_sql:
                                logger.info(f"从取消请求中提取到原始SQL: {orig_sql[:100]}...")
                            break
                    
                    # 检查是否有活跃的查询进程
                    user = self.datasource.username
                    logger.info(f"查找用户 '{user}' 的活跃查询...")
                    
                    # 查询所有TIME>0的活跃查询
                    cursor.execute(f"""
                        SELECT ID, USER, COMMAND, TIME, STATE, INFO 
                        FROM information_schema.PROCESSLIST 
                        WHERE USER = '{user}' 
                        AND COMMAND != 'Sleep'
                        AND TIME > 0
                        AND INFO IS NOT NULL
                        AND INFO NOT LIKE '%KILL%'
                        AND INFO NOT LIKE '%PROCESSLIST%'
                    """)
                    active_queries = cursor.fetchall()
                    
                    if active_queries:
                        logger.info(f"找到 {len(active_queries)} 个活跃查询")
                        
                        # 如果有原始SQL，尝试精确匹配
                        if orig_sql:
                            cleaned_sql = ' '.join(orig_sql.split())
                            search_fragment = cleaned_sql[:50].replace("'", "''")  # 使用较短片段避免截断问题
                            
                            # 先找出精确匹配的查询
                            matched_queries = []
                            for query in active_queries:
                                query_info = query['INFO']
                                query_preview = query_info[:100] + '...' if len(query_info) > 100 else query_info
                                logger.info(f"比较进程 ID={query['ID']}, TIME={query['TIME']}s: {query_preview}")
                                
                                # 检查是否包含搜索片段
                                if search_fragment in query_info:
                                    matched_queries.append(query)
                                    logger.info(f"找到匹配查询 ID={query['ID']}, 匹配片段: '{search_fragment}'")
                            
                            # 如果找到精确匹配的查询，优先终止这些查询
                            if matched_queries:
                                logger.info(f"找到 {len(matched_queries)} 个精确匹配的查询")
                                for match in matched_queries:
                                    pid = match['ID']
                                    try:
                                        logger.info(f"终止精确匹配的查询进程 ID: {pid}")
                                        cursor.execute(f"KILL QUERY {pid}")
                                        killed_count += 1
                                    except Exception as e:
                                        # 忽略 "Query execution was interrupted" 错误，这实际上表示成功
                                        if "Query execution was interrupted" in str(e):
                                            logger.info(f"进程 {pid} 已成功终止 (错误信息表示成功: {e})")
                                            killed_count += 1
                                        else:
                                            logger.warning(f"终止进程 {pid} 失败: {e}")
                                
                                if killed_count > 0:
                                    return {
                                        'success': True,
                                        'message': f'已精确匹配并终止 {killed_count} 个查询进程',
                                    }
                        
                        # 如果没有找到精确匹配，返回未找到匹配的信息
                        if killed_count == 0:
                            logger.info("未找到与原始SQL精确匹配的查询进程，不执行任何终止操作")
                            return {
                                'success': False,
                                'message': '未找到与当前SQL精确匹配的查询进程',
                            }
                except Exception as e:
                    logger.error(f"精确匹配查询进程时出错: {e}")
                    
                    # 记录所有查询进程信息，用于调试
                    logger.info(f"当前活跃的查询进程数量: {len(user_processes)}")
                    for idx, process in enumerate(user_processes):
                        proc_id = process.get('Id', 'unknown')
                        proc_time = process.get('Time', 0)
                        proc_state = process.get('State', 'unknown')
                        proc_info = process.get('Info', '')
                        proc_info_preview = proc_info[:100] + '...' if len(proc_info) > 100 else proc_info
                        logger.info(f"进程 {idx+1}/{len(user_processes)}: ID={proc_id}, Time={proc_time}s, State={proc_state}, SQL={proc_info_preview}")
                    
                    # 未找到精确匹配的查询进程，不继续尝试
                    logger.warning("未能通过SQL内容精确匹配找到查询进程，不执行任何终止操作")
                    return {
                        'success': False,
                        'message': '未找到精确匹配的查询进程，请重试或指定进程ID',
                    }
                else:
                    # 如果没有提供查询哈希值，返回错误信息而不是终止所有查询
                    logger.warning("未提供查询哈希值或SQL内容，无法精确匹配查询进程")
                    return {
                        'success': False,
                        'message': '未提供查询哈希值或SQL内容，无法精确终止查询，请指定进程ID',
                    }
            else:
                return {
                    'success': True,
                    'message': '没有找到可终止的查询进程',
                }
                
        except Exception as e:
            logger.error(f"取消MySQL查询失败: {e}")
            # 检查是否是"Query execution was interrupted"错误，这实际上表示成功
            if "Query execution was interrupted" in str(e):
                logger.info(f"查询已成功终止，尽管有错误信息: {e}")
                return {
                    'success': True,
                    'message': '查询已成功终止',
                }
            else:
                return {
                    'success': False,
                    'error': f'取消查询失败: {str(e)}',
                    'message': f'取消查询失败: {str(e)}',
                }
        finally:
            if conn:
                try:
                    conn.close()
                except:
                    pass  # 忽略关闭连接时的错误
                    
    def _calculate_query_hash(self, sql):
        """计算SQL查询的哈希值，与前端使用相同的算法"""
        if not sql:
            return "0"
            
        # 使用与前端相同的哈希算法
        hash_value = 0
        for i in range(len(sql)):
            char_code = ord(sql[i])
            hash_value = ((hash_value << 5) - hash_value) + char_code
            hash_value = hash_value & hash_value  # 转换为32位整数
            
        return str(hash_value)

class ClickHouseAdapter(DatabaseAdapter):
    """ClickHouse数据库适配器"""
    
    def __init__(self, datasource):
        """初始化适配器，保存数据源信息"""
        super().__init__(datasource)
        self.conn = None
        self.http_url = None
        self.http_auth = None
        self.is_http_mode = False
        self.connected = False
        self._current_schema = None
    
    def connect(self):
        """连接到ClickHouse数据库（仅使用Native协议）"""
        logger.info(f"尝试通过Native协议连接ClickHouse: {self.datasource.host}:{self.datasource.port}")
        
        # 基础连接参数
        conn_params = {
            'host': self.datasource.host,
            'port': int(self.datasource.port),
            'user': self.datasource.username,
            'password': self.datasource.password,
            'database': self.datasource.database or '',
            'connect_timeout': 10,
            'settings': {'max_query_size': 10000000}
        }

        # 1. 尝试非安全的原生连接
        try:
            logger.info("尝试标准 (非安全) Native连接")
            conn_params['secure'] = False
            client = clickhouse_driver.Client(**conn_params)
            client.execute('SELECT 1')
            logger.info("标准Native协议连接成功")
            self.conn = client
            self.is_http_mode = False
            self.connected = True
            return self
        except Exception as e:
            logger.warning(f"标准Native协议连接失败: {e}")
            # 如果是认证失败，直接抛出，不再尝试其他方式
            if "Authentication failed" in str(e):
                raise Exception(f"认证失败，请检查用户名和密码: {e}")
            # 如果是"Unexpected packet"，说明端口很可能是HTTP端口，给出明确提示
            if "Unexpected packet" in str(e):
                 raise Exception(f"协议错误: 该端口 ({self.datasource.port}) 很可能是一个HTTP/HTTPS端口，而非原生协议端口。请使用原生TCP端口 (通常是9000)。")

        # 2. 尝试安全的原生连接 (SSL)
        try:
            logger.info("尝试安全 (SSL) Native连接")
            conn_params['secure'] = True
            conn_params['verify'] = False # 通常在内部环境中我们不验证证书
            client = clickhouse_driver.Client(**conn_params)
            client.execute('SELECT 1')
            logger.info("安全 (SSL) Native协议连接成功")
            self.conn = client
            self.is_http_mode = False
            self.connected = True
            return self
        except Exception as e:
            logger.error(f"所有Native协议连接尝试均失败: {e}")
            if "WRONG_VERSION_NUMBER" in str(e):
                 raise Exception(f"SSL版本错误: 该端口 ({self.datasource.port}) 可能是一个非SSL端口，但程序尝试了SSL连接。请检查端口配置。")
            raise Exception(f"无法通过Native协议连接到ClickHouse: {e}。请检查主机、端口、用户名和密码，并确保使用的是原生TCP端口（如9000或9440）。")

    def execute(self, query, *args, **kwargs):
        """执行ClickHouse查询（仅限Native模式）"""
        try:
            if not self.connected:
                self.connect()

            # 在原生模式下，直接调用客户端的execute方法
            return self.conn.execute(query, *args, **kwargs)
            
        except Exception as e:
            # 如果是查询被取消的错误，直接抛出，不要尝试重连和重试
            if "Query was cancelled" in str(e):
                logger.info("查询已被用户取消，不尝试重连")
                raise e
                
            logger.error(f"ClickHouse查询执行失败: {str(e)}", exc_info=True)
            # 尝试重新连接一次
            try:
                logger.info("查询失败，尝试重新连接...")
                self.connect()
                
                # 如果之前设置了schema，重新设置
                if hasattr(self, '_current_schema') and self._current_schema:
                    logger.info(f"重连后恢复schema: {self._current_schema}")
                    self.conn.execute(f"USE `{self._current_schema}`")
                    
                return self.conn.execute(query, *args, **kwargs)
            except Exception as retry_e:
                logger.error(f"重连后查询仍然失败: {retry_e}", exc_info=True)
                raise retry_e
    
    def execute_query(self, sql, schema=None):
        """执行SQL查询"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            # We cannot re-use connections across forks, and we don't know
            # if we have been forked, so we have to reconnect every time.
            # This is slow, but safe.
            self.connect()

            # 如果前端提供了schema，则先切换数据库
            if schema:
                logger.info(f"ClickHouseAdapter: Switching to schema '{schema}'")
                self.execute(f'USE `{schema}`')
                # 保存当前schema以便重连时使用
                self._current_schema = schema
                
            # 检查是否为终止查询的特殊标记
            if '/* KILL_QUERY_FLAG */' in sql:
                logger.info("正在执行终止ClickHouse查询操作")
                
                try:
                    # 获取所有正在运行的查询
                    username = self.datasource.username
                    result = self.execute(f"""
                        SELECT 
                            query_id,
                            user,
                            query
                        FROM system.processes
                        WHERE user = '{username}' AND query NOT LIKE '%KILL%'
                    """, with_column_types=True)
                    
                    # 提取行数据
                    kill_result = result[0] if isinstance(result, tuple) else result
                    
                    if kill_result and len(kill_result) > 0:
                        killed_count = 0
                        for query_info in kill_result:
                            try:
                                query_id = query_info[0]
                                logger.info(f"正在终止ClickHouse查询 ID: {query_id}")
                                self.execute(f"KILL QUERY WHERE query_id = '{query_id}'")
                                killed_count += 1
                            except Exception as e:
                                logger.error(f"终止ClickHouse查询 {query_id} 时出错: {e}")
                        
                        return {
                            'success': True,
                            'message': f'已终止 {killed_count} 个ClickHouse查询',
                            'columns': ['message'],
                            'data': [{'message': f'已终止 {killed_count} 个ClickHouse查询'}]
                        }
                    else:
                        return {
                            'success': True,
                            'message': '没有找到可终止的ClickHouse查询',
                            'columns': ['message'],
                            'data': [{'message': '没有找到可终止的ClickHouse查询'}]
                        }
                except Exception as kill_e:
                    logger.error(f"执行终止查询操作时出错: {kill_e}")
                    return {
                        'success': False,
                        'error': f'终止查询失败: {str(kill_e)}',
                        'columns': ['error'],
                        'data': [{'error': f'终止查询失败: {str(kill_e)}'}]
                    }

            # 对于SELECT、SHOW、DESCRIBE、EXPLAIN等查询，获取结果
            if sql.strip().lower().startswith(('select', 'show', 'describe', 'explain')):
                # 移除错误的 database 参数
                rows, columns_with_types = self.execute(sql, with_column_types=True)
                
                logger.info(f"ClickHouse查询返回 {len(rows)} 行数据")
                
                # 提取列名
                columns = [col[0] for col in columns_with_types]
                
                # 将元组数据转换为字典列表
                data = [dict(zip(columns, row)) for row in rows]
                
                return {
                    'success': True,
                    'columns': columns,
                    'data': data
                }
            else:
                # 对于非查询类SQL, 移除错误的 database 参数
                self.execute(sql)
                return {
                    'success': True,
                    'message': 'Command executed successfully.',
                    'rows_affected': -1 # ClickHouse原生协议通常不直接返回影响行数
                }
        except Exception as e:
            logger.error(f"ClickHouseAdapter 执行查询时出错: {e}", exc_info=True)
            return {'success': False, 'error': str(e)}
    
    def get_schemas(self):
        """获取ClickHouse所有数据库列表"""
        try:
            conn = self.connect()
            
            # 查询ClickHouse所有数据库
            result = conn.execute("SHOW DATABASES", with_column_types=True)
            rows, _ = result
            
            # 提取数据库名称
            schemas = [row[0] for row in rows if row[0] not in ['system', 'information_schema']]
            
            return schemas
        except Exception as e:
            logger.error(f"获取ClickHouse数据库列表失败: {e}")
            if hasattr(e, '__dict__'):
                logger.error(f"错误详情: {e.__dict__}")
            raise
    
    def get_schema(self, schema_name):
        """获取ClickHouse数据库结构"""
        try:
            conn = self.connect()
            
            # 如果没有指定schema，使用当前数据库
            if not schema_name:
                schema_name = self.datasource.database
            
            # 添加详细的调试日志
            logger.info(f"开始获取ClickHouse数据库结构，模式: {schema_name}")
            
            # 获取表和视图
            result = conn.execute(f"SHOW TABLES FROM {schema_name}", with_column_types=True)
            table_rows, _ = result
            
            tables = []
            views = []
            
            # 处理表和视图
            for row in table_rows:
                table_name = row[0]
                
                # 检查是否为视图
                try:
                    table_type_result = conn.execute(
                        f"SELECT engine FROM system.tables WHERE database = '{schema_name}' AND name = '{table_name}'",
                        with_column_types=True
                    )
                    type_rows, _ = table_type_result
                    
                    if type_rows and type_rows[0][0] == 'View':
                        views.append({'name': table_name})
                    else:
                        # 获取表的行数估计
                        try:
                            count_result = conn.execute(
                                f"SELECT count() FROM {schema_name}.{table_name} LIMIT 1",
                                with_column_types=True
                            )
                            count_rows, _ = count_result
                            row_count = count_rows[0][0] if count_rows else 0
                        except:
                            row_count = 0
                        
                        tables.append({
                            'name': table_name,
                            'row_count': row_count
                        })
                except Exception as table_error:
                    logger.warning(f"获取表 {table_name} 类型时出错: {table_error}")
                    # 默认添加为表
                    tables.append({
                        'name': table_name,
                        'row_count': 0
                    })
            
            # 获取函数
            functions = []
            try:
                func_result = conn.execute("SHOW FUNCTIONS", with_column_types=True)
                func_rows, _ = func_result
                
                for row in func_rows:
                    functions.append({'name': row[0]})
            except:
                # ClickHouse可能不支持此命令，忽略错误
                pass
            
            return {
                'name': schema_name,
                'tables': tables,
                'views': views,
                'procedures': [],  # ClickHouse不支持存储过程
                'functions': functions
            }
        except Exception as e:
            logger.error(f"获取ClickHouse数据库结构失败: {e}")
            if hasattr(e, '__dict__'):
                logger.error(f"错误详情: {e.__dict__}")
            raise

    def debug_table_structure(self, schema_name):
        """调试表结构方法，用于检查返回的数据格式"""
        if not schema_name:
            schema_name = self.datasource.database
            
        conn = self.connect()
        
        # 查询表结构
        result = conn.execute(f"SHOW TABLES FROM {schema_name} LIMIT 5", with_column_types=True)
        table_rows, column_types = result
        
        # 输出结果类型和内容
        logger.info(f"表结构类型: {type(table_rows)}")
        logger.info(f"表结构内容: {table_rows}")
        logger.info(f"列类型: {column_types}")
        
        # 获取服务器版本信息
        version_result = conn.execute("SELECT version()", with_column_types=True)
        version_rows, _ = version_result
        version = version_rows[0][0] if version_rows else "未知"
        
        return {
            'success': True,
            'type': str(type(table_rows)),
            'sample': [list(row) for row in table_rows[:5]] if table_rows else [],
            'version': version
        }
        
    
    def cancel_query(self, process_id=None, query_hash=None):
        """取消ClickHouse正在执行的查询进程
        
        参数:
            process_id: 进程ID，如果提供则只终止该进程
            query_hash: 查询哈希值，如果提供则只终止匹配该哈希的查询
        """
        try:
            self.connect()
            
            # 如果提供了具体的进程ID，则直接终止该进程
            if process_id:
                logger.info(f"正在终止ClickHouse查询 ID: {process_id}")
                self.execute(f"KILL QUERY WHERE query_id = '{process_id}'")
                return {
                    'success': True,
                    'message': f'已终止查询 ID: {process_id}',
                }
            
            # 获取所有进程
            try:
                # ClickHouse中参数需要使用引号
                username = self.datasource.username
                # 在ClickHouse中，execute返回的是(rows, columns_with_types)的元组
                result = self.execute(f"""
                    SELECT 
                        query_id,
                        user,
                        query
                    FROM system.processes
                    WHERE user = '{username}' AND query NOT LIKE '%KILL%'
                """, with_column_types=True)
                
                # 提取行数据
                running_queries = result[0] if isinstance(result, tuple) else result
                
                if running_queries and len(running_queries) > 0:
                    killed_count = 0
                    
                    # 如果提供了查询哈希值，则只终止匹配的查询
                    if query_hash:
                        logger.info(f"使用查询哈希值终止特定查询: {query_hash}")
                        for query_info in running_queries:
                            try:
                                query_id = query_info[0]
                                sql = query_info[2] if len(query_info) > 2 else ""
                                
                                # 计算当前查询的哈希值
                                current_hash = self._calculate_query_hash(sql)
                                
                                # 如果哈希值匹配，则终止该查询
                                if current_hash == query_hash:
                                    logger.info(f"找到匹配的查询，正在终止ClickHouse查询 ID: {query_id}")
                                    self.execute(f"KILL QUERY WHERE query_id = '{query_id}'")
                                    killed_count += 1
                                    # 找到匹配的查询后立即返回
                                    return {
                                        'success': True,
                                        'message': f'已终止匹配的查询 ID: {query_id}',
                                    }
                            except Exception as e:
                                logger.error(f"终止ClickHouse查询时出错: {e}")
                        
                        # 如果没有找到匹配的查询
                        if killed_count == 0:
                            return {
                                'success': False,
                                'message': '没有找到匹配的查询进程',
                            }
                    else:
                        # 如果没有提供查询哈希值，则终止所有查询进程（保持原有行为）
                        for query_info in running_queries:
                            try:
                                query_id = query_info[0]
                                logger.info(f"正在终止ClickHouse查询 ID: {query_id}")
                                self.execute(f"KILL QUERY WHERE query_id = '{query_id}'")
                                killed_count += 1
                            except Exception as e:
                                logger.error(f"终止ClickHouse查询 {query_id} 时出错: {e}")
                        
                        return {
                            'success': True,
                            'message': f'已终止 {killed_count} 个ClickHouse查询',
                        }
                else:
                    return {
                        'success': True,
                        'message': '没有找到可终止的ClickHouse查询',
                    }
            except Exception as e:
                logger.error(f"获取ClickHouse运行中的查询失败: {e}")
                return {
                    'success': False,
                    'error': f'获取运行中的查询失败: {str(e)}',
                    'message': f'获取运行中的查询失败: {str(e)}',
                }
                
        except Exception as e:
            logger.error(f"取消ClickHouse查询失败: {e}")
            return {
                'success': False,
                'error': f'取消查询失败: {str(e)}',
                'message': f'取消查询失败: {str(e)}',
            }
            
    def _calculate_query_hash(self, sql):
        """计算SQL查询的哈希值，与前端使用相同的算法"""
        if not sql:
            return "0"
            
        # 使用与前端相同的哈希算法
        hash_value = 0
        for i in range(len(sql)):
            char_code = ord(sql[i])
            hash_value = ((hash_value << 5) - hash_value) + char_code
            hash_value = hash_value & hash_value  # 转换为32位整数
            
        return str(hash_value)

class RedisAdapter(DatabaseAdapter):
    """Redis数据库适配器"""
    
    def connect(self):
        """连接到Redis数据库"""
        try:
            # 创建Redis连接
            conn_params = {
                'host': self.datasource.host,
                'port': int(self.datasource.port),
                'password': self.datasource.password if self.datasource.password else None,
                'decode_responses': True,  # 自动解码响应
                'socket_timeout': 5,
                'socket_connect_timeout': 5
            }
            
            # 如果有数据库编号，添加到连接参数
            if self.datasource.database and self.datasource.database.isdigit():
                conn_params['db'] = int(self.datasource.database)
            
            # 创建Redis连接
            conn = redis.Redis(**conn_params)
            
            # 测试连接
            conn.ping()
            
            logger.info(f"成功连接到Redis数据库: {self.datasource.host}:{self.datasource.port}")
            return conn
        except redis.exceptions.AuthenticationError as e:
            error_msg = str(e)
            logger.error(f"Redis认证失败: {error_msg}")
            raise Exception(f"Redis认证失败，请检查密码: {error_msg}")
        except redis.exceptions.ConnectionError as e:
            error_msg = str(e)
            logger.error(f"Redis连接失败: {error_msg}")
            raise Exception(f"无法连接到Redis服务器，请检查主机名和端口: {error_msg}")
        except redis.exceptions.TimeoutError as e:
            error_msg = str(e)
            logger.error(f"Redis连接超时: {error_msg}")
            raise Exception(f"连接Redis超时，请检查网络或服务器状态: {error_msg}")
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Redis连接失败: {error_msg}")
            raise Exception(f"连接Redis失败: {error_msg}")
    
    def execute_query(self, command, schema=None):
        """执行Redis命令"""
        # (Redis的schema概念不同，但为了保持接口一致，接收该参数但不使用)
        conn = None
        try:
            conn = self.connect()
            
            # 解析命令
            cmd_parts = command.strip().split()
            if not cmd_parts:
                raise Exception("命令不能为空")
            
            cmd = cmd_parts[0].upper()
            args = cmd_parts[1:]
            
            # 执行命令
            if hasattr(conn, cmd.lower()):
                method = getattr(conn, cmd.lower())
                result = method(*args)
            else:
                # 使用通用execute_command方法
                result = conn.execute_command(cmd, *args)
            
            # 处理结果
            processed_result = self._process_redis_result(result)
            
            return {
                'success': True,
                'columns': ['result'],
                'rows': [{'result': processed_result}],
                'data': [{'result': processed_result}],
                'affected_rows': 1 if result else 0
            }
        except Exception as e:
            logger.error(f"执行Redis命令失败: {str(e)}")
            error_msg = f"Redis命令执行错误: {str(e)}"
            raise Exception(error_msg)
        finally:
            if conn:
                try:
                    conn.close()
                except:
                    pass  # 忽略关闭错误
    
    def _process_redis_result(self, result):
        """处理Redis结果，使其可以被JSON序列化"""
        if isinstance(result, (bytes, bytearray)):
            try:
                return result.decode('utf-8')
            except UnicodeDecodeError:
                return str(result)
        elif isinstance(result, list):
            return [self._process_redis_result(item) for item in result]
        elif isinstance(result, dict):
            return {k.decode('utf-8') if isinstance(k, bytes) else k: 
                    self._process_redis_result(v) for k, v in result.items()}
        elif isinstance(result, set):
            return [self._process_redis_result(item) for item in result]
        else:
            return self._process_result_for_json(result)
    
    def get_schemas(self):
        """获取Redis数据库列表"""
        try:
            conn = self.connect()
            
            # Redis默认有16个数据库(0-15)，但可以通过配置文件修改
            # 这里我们尝试获取所有数据库的键数量来确定哪些数据库在使用
            schemas = []
            
            # 获取当前数据库编号
            current_db = int(self.datasource.database) if self.datasource.database and self.datasource.database.isdigit() else 0
            
            # 添加当前数据库
            schemas.append(f"db{current_db}")
            
            # 尝试获取其他数据库信息（需要CONFIG命令权限）
            try:
                config = conn.config_get('databases')
                if config and 'databases' in config:
                    max_db = int(config['databases'])
                    for i in range(max_db):
                        if i != current_db:
                            schemas.append(f"db{i}")
            except:
                # 如果没有CONFIG权限，只返回当前数据库
                pass
            
            conn.close()
            return schemas
        except Exception as e:
            logger.error(f"获取Redis数据库列表失败: {e}")
            if hasattr(e, '__dict__'):
                logger.error(f"错误详情: {e.__dict__}")
            raise
    
    def get_schema(self, schema_name):
        """获取Redis数据库结构（键列表）"""
        try:
            conn = self.connect()
            
            # 如果schema_name格式为"dbN"，切换到指定的数据库
            if schema_name and schema_name.startswith('db') and schema_name[2:].isdigit():
                db_number = int(schema_name[2:])
                conn.select(db_number)
            
            # 获取所有键
            keys = conn.keys('*')
            
            # 构建表结构
            tables = []
            for key in keys:
                key_type = conn.type(key)
                key_ttl = conn.ttl(key)
                
                # 获取键的大小/元素数量
                size = 0
                if key_type == 'string':
                    size = len(conn.get(key) or '')
                elif key_type == 'list':
                    size = conn.llen(key)
                elif key_type == 'set':
                    size = conn.scard(key)
                elif key_type == 'zset':
                    size = conn.zcard(key)
                elif key_type == 'hash':
                    size = conn.hlen(key)
                
                tables.append({
                    'name': key,
                    'type': key_type,
                    'ttl': key_ttl,
                    'size': size,
                    'columns': [
                        {'name': 'key', 'type': 'string'},
                        {'name': 'value', 'type': key_type},
                        {'name': 'ttl', 'type': 'integer'}
                    ]
                })
            
            conn.close()
            
            return {
                'tables': tables,
                'views': []
            }
        except Exception as e:
            logger.error(f"获取Redis数据库结构失败: {e}")
            if hasattr(e, '__dict__'):
                logger.error(f"错误详情: {e.__dict__}")
            raise
    
    def debug_table_structure(self, schema_name):
        """调试Redis数据库结构"""
        try:
            conn = self.connect()
            
            # 如果schema_name格式为"dbN"，切换到指定的数据库
            if schema_name and schema_name.startswith('db') and schema_name[2:].isdigit():
                db_number = int(schema_name[2:])
                conn.select(db_number)
            
            # 获取所有键
            keys = conn.keys('*')
            
            # 获取Redis服务器信息
            info = conn.info()
            
            conn.close()
            
            return {
                'key_count': len(keys),
                'sample_keys': keys[:10],  # 最多返回10个键
                'server_info': info
            }
        except Exception as e:
            logger.error(f"调试Redis数据库结构失败: {e}")
            if hasattr(e, '__dict__'):
                logger.error(f"错误详情: {e.__dict__}")
            raise
