from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import (
    DatabaseTypeViewSet, DatasourceViewSet, 
    DatasourcePermissionViewSet, TablePermissionViewSet,
    test_connection, get_schema, get_schemas,
    datasource_status, single_datasource_status
)

router = DefaultRouter()
router.register(r'types', DatabaseTypeViewSet)
router.register(r'datasources', DatasourceViewSet)
router.register(r'datasource-permissions', DatasourcePermissionViewSet)
router.register(r'table-permissions', TablePermissionViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('test-connection/', test_connection, name='test-connection'),
    path('schema-list/<int:pk>/', get_schemas, name='schema-list'),
    path('database-schema/<int:pk>/', get_schema, name='database-schema-without-schema'),  # 新增：不带 schema_name 的路由
    path('database-schema/<int:pk>/<str:schema_name>/', get_schema, name='database-schema'),
    path('status/', datasource_status, name='datasource-status'),
    path('status/<int:pk>/', single_datasource_status, name='single-datasource-status'),
] 