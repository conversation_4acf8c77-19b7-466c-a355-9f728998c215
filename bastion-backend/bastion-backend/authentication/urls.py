from django.urls import path
from . import views

urlpatterns = [
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('register/', views.register_view, name='register'),
    path('user/profile/', views.user_profile, name='user_profile'),
    path('user/change-password/', views.change_password, name='change_password'),
    path('roles/', views.role_list, name='role_list'),
    path('roles/<int:pk>/', views.role_detail, name='role_detail'),
    path('permissions/', views.permission_list, name='permission_list'),
    
    # 添加用户管理相关API
    path('users/', views.user_list, name='user_list'),
    path('users/<int:pk>/', views.user_detail, name='user_detail'),
    path('users/me/', views.current_user, name='current_user'),
    path('users/change-password/', views.change_user_password, name='change_user_password'),
] 