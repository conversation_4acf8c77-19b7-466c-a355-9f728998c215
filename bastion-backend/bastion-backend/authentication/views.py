from django.shortcuts import render
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from django.db import transaction
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.http import JsonResponse
from rest_framework import status, viewsets
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
import json
import time
import hashlib

from .models import Role, Permission, UserProfile, RolePermission


@api_view(['POST'])
@permission_classes([AllowAny])
def login_view(request):
    """用户登录"""
    # 在REST框架的APIView中，数据已经被解析到request.data中
    # 不应该再次调用json.loads(request.body)
    username = request.data.get('username', '')
    password = request.data.get('password', '')
    
    user = authenticate(username=username, password=password)
    
    if user is not None:
        login(request, user)
        
        # 记录审计日志
        from audit.models import AuditLog
        AuditLog.objects.create(
            user=user,
            action_type='login',
            ip_address=request.META.get('REMOTE_ADDR'),
            content=f"用户 {username} 登录成功"
        )
        
        # 获取用户信息
        try:
            profile = user.profile
            role = profile.role.name if profile.role else None
        except UserProfile.DoesNotExist:
            profile = None
            role = None
        
        # 为了兼容前端可能使用的token认证机制
        # 生成一个简单的token （仅用于兼容，实际认证仍使用会话）
        simple_token = hashlib.md5(f"{username}{time.time()}".encode()).hexdigest()
        
        return Response({
            'success': True,
            'message': '登录成功',
            'token': simple_token,  # 添加token
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'role': role,
                'is_staff': user.is_staff,
                'is_superuser': user.is_superuser,
            }
        })
    else:
        return Response({
            'success': False,
            'message': '用户名或密码错误'
        }, status=status.HTTP_401_UNAUTHORIZED)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_view(request):
    """用户登出"""
    # 记录审计日志
    from audit.models import AuditLog
    AuditLog.objects.create(
        user=request.user,
        action_type='logout',
        ip_address=request.META.get('REMOTE_ADDR'),
        content=f"用户 {request.user.username} 登出系统"
    )
    
    logout(request)
    return Response({
        'success': True,
        'message': '退出登录成功'
    })

@api_view(['POST'])
@permission_classes([AllowAny])
def register_view(request):
    """用户注册"""
    username = request.data.get('username', '')
    password = request.data.get('password', '')
    email = request.data.get('email', '')
    
    # 检查用户名是否存在
    if User.objects.filter(username=username).exists():
        return Response({
            'success': False,
            'message': '用户名已存在'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # 创建用户
    with transaction.atomic():
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password
        )
        
        # 创建用户配置
        UserProfile.objects.create(user=user)
        
        # 记录审计日志
        from audit.models import AuditLog
        AuditLog.objects.create(
            user=user,
            action_type='login',
            ip_address=request.META.get('REMOTE_ADDR'),
            content=f"用户 {username} 注册并登录系统"
        )
        
        # 自动登录
        login(request, user)
    
    return Response({
        'success': True,
        'message': '注册成功',
        'user': {
            'id': user.id,
            'username': user.username,
            'email': user.email,
        }
    })

@api_view(['GET', 'PUT'])
@permission_classes([IsAuthenticated])
def user_profile(request):
    """获取或更新用户资料"""
    user = request.user
    
    # 获取用户资料
    if request.method == 'GET':
        try:
            profile = user.profile
        except UserProfile.DoesNotExist:
            profile = UserProfile.objects.create(user=user)
            
        return Response({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'phone': profile.phone,
            'role': profile.role.name if profile.role else None,
            'is_staff': user.is_staff,
            'is_superuser': user.is_superuser,
            'date_joined': user.date_joined,
            'last_login': user.last_login,
        })
    
    # 更新用户资料
    elif request.method == 'PUT':
        email = request.data.get('email')
        phone = request.data.get('phone')
        
        with transaction.atomic():
            # 更新User表
            if email:
                user.email = email
                user.save(update_fields=['email'])
            
            # 更新UserProfile表
            try:
                profile = user.profile
            except UserProfile.DoesNotExist:
                profile = UserProfile.objects.create(user=user)
            
            if phone:
                profile.phone = phone
                profile.save(update_fields=['phone'])
        
        return Response({
            'success': True,
            'message': '用户资料更新成功'
        })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def change_password(request):
    """修改密码"""
    old_password = request.data.get('old_password')
    new_password = request.data.get('new_password')
    
    user = request.user
    
    # 验证旧密码
    if not user.check_password(old_password):
        return Response({
            'success': False,
            'message': '旧密码不正确'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # 更新密码
    user.set_password(new_password)
    user.save()
    
    # 记录审计日志
    from audit.models import AuditLog
    # AuditLog.objects.create(
    #     user=user,
    #     action_type='password_change',
    #     ip_address=request.META.get('REMOTE_ADDR'),
    #     content=f"用户 {user.username} 修改了密码"
    # )
    
    # 更新会话
    login(request, user)
    
    return Response({
        'success': True,
        'message': '密码修改成功'
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def role_list(request):
    """获取角色列表"""
    roles = Role.objects.all()
    result = []
    
    for role in roles:
        result.append({
            'id': role.id,
            'name': role.name,
            'description': role.description,
        })
    
    return Response(result)

@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def role_detail(request, pk):
    """获取、更新或删除角色"""
    try:
        role = Role.objects.get(pk=pk)
    except Role.DoesNotExist:
        return Response({
            'success': False,
            'message': '角色不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    
    # 获取角色详情
    if request.method == 'GET':
        # 获取角色所有权限
        permissions = [{'id': rp.permission.id, 'name': rp.permission.name, 'code': rp.permission.code} 
                      for rp in role.permissions.all()]
        
        return Response({
            'id': role.id,
            'name': role.name,
            'description': role.description,
            'permissions': permissions,
            'created_at': role.created_at,
            'updated_at': role.updated_at,
        })
    
    # 更新角色
    elif request.method == 'PUT':
        data = request.data
        name = data.get('name')
        description = data.get('description')
        permission_ids = data.get('permission_ids', [])
        
        with transaction.atomic():
            # 更新角色信息
            if name:
                role.name = name
            if description:
                role.description = description
            role.save()
            
            # 更新角色权限
            if permission_ids:
                # 清除原有权限
                RolePermission.objects.filter(role=role).delete()
                
                # 添加新权限
                for permission_id in permission_ids:
                    try:
                        permission = Permission.objects.get(pk=permission_id)
                        RolePermission.objects.create(role=role, permission=permission)
                    except Permission.DoesNotExist:
                        pass
        
        # 记录审计日志
        from audit.models import AuditLog
        AuditLog.objects.create(
            user=request.user,
            action_type='permission_edit',
            ip_address=request.META.get('REMOTE_ADDR'),
            content=f"用户 {request.user.username} 修改了角色 {role.name} 的权限"
        )
        
        return Response({
            'success': True,
            'message': '角色更新成功'
        })
    
    # 删除角色
    elif request.method == 'DELETE':
        role_name = role.name
        role.delete()
        # 记录审计日志
        from audit.models import AuditLog
        AuditLog.objects.create(
            user=request.user,
            action_type='permission_delete',
            ip_address=request.META.get('REMOTE_ADDR'),
            content=f"用户 {request.user.username} 删除了角色 {role_name}"
        )
        return Response({
            'success': True,
            'message': '角色删除成功'
        })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def permission_list(request):
    """获取权限列表"""
    permissions = Permission.objects.all()
    result = []
    
    for permission in permissions:
        result.append({
            'id': permission.id,
            'name': permission.name,
            'code': permission.code,
            'type': permission.type,
            'description': permission.description,
        })
    
    return Response(result)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_role(request):
    """创建角色"""
    # 使用request.data代替json.loads(request.body)
    role_name = request.data.get('name')
    role_description = request.data.get('description')
    permission_ids = request.data.get('permissions', [])
    
    with transaction.atomic():
        # 创建角色
        role = Role.objects.create(
            name=role_name,
            description=role_description
        )
        
        # 添加权限
        for permission_id in permission_ids:
            try:
                permission = Permission.objects.get(id=permission_id)
                RolePermission.objects.create(role=role, permission=permission)
            except Permission.DoesNotExist:
                pass
        
        # 记录审计日志
        from audit.models import AuditLog
        AuditLog.objects.create(
            user=request.user,
            action_type='permission_add',
            ip_address=request.META.get('REMOTE_ADDR'),
            content=f"用户 {request.user.username} 创建了角色 {role_name}"
        )
    
    return Response({
        'success': True,
        'message': '角色创建成功',
        'id': role.id
    })

# 添加用户管理相关视图函数
@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def user_list(request):
    """获取用户列表或创建新用户"""
    # 获取用户列表
    if request.method == 'GET':
        print(f"用户列表API被调用 - 用户: {request.user.username}, 是否超级管理员: {request.user.is_superuser}, 是否管理员: {request.user.is_staff}")
        
        # 检查权限
        if not request.user.is_superuser and not request.user.is_staff:
            print(f"用户 {request.user.username} 没有权限查看用户列表")
            return Response({
                'success': False,
                'message': '没有权限查看用户列表'
            }, status=status.HTTP_403_FORBIDDEN)
            
        users = User.objects.all()
        print(f"数据库中的用户数量: {users.count()}")
        result = []
        
        for user in users:
            try:
                profile = user.profile
                role = profile.role.name if profile.role else None
            except UserProfile.DoesNotExist:
                profile = None
                role = None
                
            user_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'is_active': user.is_active,
                'is_staff': user.is_staff,
                'is_superuser': user.is_superuser,
                'role': role,
                'date_joined': user.date_joined,
                'last_login': user.last_login
            }
            result.append(user_data)
            print(f"添加用户到结果列表: {user.username}")
            
        print(f"返回用户列表，共 {len(result)} 个用户")
        return Response(result)
    
    # 创建新用户
    elif request.method == 'POST':
        # 检查权限
        if not request.user.is_superuser:
            return Response({
                'success': False,
                'message': '没有权限创建用户'
            }, status=status.HTTP_403_FORBIDDEN)
            
        username = request.data.get('username')
        email = request.data.get('email', '')
        password = request.data.get('password')
        is_staff = request.data.get('is_staff', False)
        is_superuser = request.data.get('is_superuser', False)
        role_id = request.data.get('role_id')
        
        # 验证必填字段
        if not username or not password:
            return Response({
                'success': False,
                'message': '用户名和密码不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)
            
        # 检查用户名是否存在
        if User.objects.filter(username=username).exists():
            return Response({
                'success': False,
                'message': '用户名已存在'
            }, status=status.HTTP_400_BAD_REQUEST)
            
        try:
            with transaction.atomic():
                # 创建用户
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    password=password,
                    is_staff=is_staff,
                    is_superuser=is_superuser
                )
                
                # 创建用户配置
                profile = UserProfile.objects.create(user=user)
                
                # 设置角色
                if role_id:
                    try:
                        role = Role.objects.get(pk=role_id)
                        profile.role = role
                        profile.save()
                    except Role.DoesNotExist:
                        pass
                
                # 记录审计日志
                from audit.models import AuditLog
                # AuditLog.objects.create(
                #     user=request.user,
                #     action_type='create_user',
                #     ip_address=request.META.get('REMOTE_ADDR'),
                #     content=f"管理员 {request.user.username} 创建了用户 {username}"
                # )
                
            return Response({
                'success': True,
                'message': '用户创建成功',
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                }
            }, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'创建用户失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def user_detail(request, pk):
    """获取、更新或删除用户"""
    # 检查权限
    if not request.user.is_superuser and not (request.user.id == pk):
        return Response({
            'success': False,
            'message': '没有权限操作此用户'
        }, status=status.HTTP_403_FORBIDDEN)
        
    try:
        user = User.objects.get(pk=pk)
    except User.DoesNotExist:
        return Response({
            'success': False,
            'message': '用户不存在'
        }, status=status.HTTP_404_NOT_FOUND)
        
    # 获取用户详情
    if request.method == 'GET':
        try:
            profile = user.profile
            role = {
                'id': profile.role.id,
                'name': profile.role.name
            } if profile.role else None
        except UserProfile.DoesNotExist:
            profile = None
            role = None
            
        return Response({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'is_active': user.is_active,
            'is_staff': user.is_staff,
            'is_superuser': user.is_superuser,
            'role': role,
            'date_joined': user.date_joined,
            'last_login': user.last_login
        })
    
    # 更新用户
    elif request.method == 'PUT':
        email = request.data.get('email')
        is_active = request.data.get('is_active')
        is_staff = request.data.get('is_staff')
        is_superuser = request.data.get('is_superuser')
        role_id = request.data.get('role_id')
        
        # 普通用户只能修改自己的邮箱
        if not request.user.is_superuser and request.user.id != pk:
            return Response({
                'success': False,
                'message': '没有权限修改此用户'
            }, status=status.HTTP_403_FORBIDDEN)
            
        try:
            with transaction.atomic():
                # 更新User表
                if email is not None:
                    user.email = email
                
                # 超级管理员可以修改用户权限
                if request.user.is_superuser:
                    if is_active is not None:
                        user.is_active = is_active
                    if is_staff is not None:
                        user.is_staff = is_staff
                    if is_superuser is not None:
                        user.is_superuser = is_superuser
                        
                user.save()
                
                # 更新UserProfile表
                try:
                    profile = user.profile
                except UserProfile.DoesNotExist:
                    profile = UserProfile.objects.create(user=user)
                
                # 超级管理员可以修改用户角色
                if request.user.is_superuser and role_id is not None:
                    try:
                        role = Role.objects.get(pk=role_id)
                        if profile.role != role:
                            from audit.models import AuditLog
                            AuditLog.objects.create(
                                user=request.user,
                                action_type='permission_edit',
                                ip_address=request.META.get('REMOTE_ADDR'),
                                content=f"用户 {request.user.username} 将用户 {user.username} 的角色修改为 {role.name}"
                            )
                        profile.role = role
                        profile.save()
                    except Role.DoesNotExist:
                        pass
                
                # 记录审计日志
                from audit.models import AuditLog
                # AuditLog.objects.create(
                #     user=request.user,
                #     action_type='update_user',
                #     ip_address=request.META.get('REMOTE_ADDR'),
                #     content=f"用户 {request.user.username} 更新了用户 {user.username} 的信息"
                # )
                
            return Response({
                'success': True,
                'message': '用户更新成功'
            })
        except Exception as e:
            return Response({
                'success': False,
                'message': f'更新用户失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    # 删除用户
    elif request.method == 'DELETE':
        # 只有超级管理员可以删除用户
        if not request.user.is_superuser:
            return Response({
                'success': False,
                'message': '没有权限删除用户'
            }, status=status.HTTP_403_FORBIDDEN)
            
        try:
            # 记录审计日志
            from audit.models import AuditLog
            # AuditLog.objects.create(
            #     user=request.user,
            #     action_type='delete_user',
            #     ip_address=request.META.get('REMOTE_ADDR'),
            #     content=f"管理员 {request.user.username} 删除了用户 {user.username}"
            # )
            
            user.delete()
            return Response({
                'success': True,
                'message': '用户删除成功'
            })
        except Exception as e:
            return Response({
                'success': False,
                'message': f'删除用户失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def current_user(request):
    """获取当前登录用户信息"""
    user = request.user
    
    try:
        profile = user.profile
        role = {
            'id': profile.role.id,
            'name': profile.role.name
        } if profile.role else None
    except UserProfile.DoesNotExist:
        profile = UserProfile.objects.create(user=user)
        role = None
        
    return Response({
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'is_active': user.is_active,
        'is_staff': user.is_staff,
        'is_superuser': user.is_superuser,
        'role': role,
        'date_joined': user.date_joined,
        'last_login': user.last_login
    })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def change_user_password(request):
    """修改用户密码"""
    user_id = request.data.get('user_id')
    current_password = request.data.get('current_password')
    new_password = request.data.get('new_password')
    
    # 修改自己的密码
    if not user_id:
        user = request.user
        
        # 验证当前密码
        if not user.check_password(current_password):
            return Response({
                'success': False,
                'message': '当前密码不正确'
            }, status=status.HTTP_400_BAD_REQUEST)
            
        # 设置新密码
        user.set_password(new_password)
        user.save()
        
        # 记录审计日志
        from audit.models import AuditLog
        # AuditLog.objects.create(
        #     user=user,
        #     action_type='change_password',
        #     ip_address=request.META.get('REMOTE_ADDR'),
        #     content=f"用户 {user.username} 修改了自己的密码"
        # )
        
        return Response({
            'success': True,
            'message': '密码修改成功'
        })
    
    # 管理员修改他人密码
    else:
        # 检查权限
        if not request.user.is_superuser:
            return Response({
                'success': False,
                'message': '没有权限修改他人密码'
            }, status=status.HTTP_403_FORBIDDEN)
            
        try:
            user = User.objects.get(pk=user_id)
            
            # 设置新密码
            user.set_password(new_password)
            user.save()
            
            # 记录审计日志
            from audit.models import AuditLog
            # AuditLog.objects.create(
            #     user=request.user,
            #     action_type='change_password',
            #     ip_address=request.META.get('REMOTE_ADDR'),
            #     content=f"管理员 {request.user.username} 修改了用户 {user.username} 的密码"
            # )
            
            return Response({
                'success': True,
                'message': '密码修改成功'
            })
        except User.DoesNotExist:
            return Response({
                'success': False,
                'message': '用户不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'修改密码失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
