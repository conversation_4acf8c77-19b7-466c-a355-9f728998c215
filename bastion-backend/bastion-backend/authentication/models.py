from django.db import models
from django.contrib.auth.models import User

class Role(models.Model):
    """
    用户角色模型
    """
    name = models.CharField(max_length=50, unique=True, verbose_name="角色名称")
    description = models.TextField(blank=True, null=True, verbose_name="角色描述")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "角色"
        verbose_name_plural = "角色"

    def __str__(self):
        return self.name

class UserProfile(models.Model):
    """
    用户扩展信息
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="profile", verbose_name="用户")
    role = models.ForeignKey(Role, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="角色")
    phone = models.Char<PERSON><PERSON>(max_length=20, blank=True, null=True, verbose_name="手机号")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "用户信息"
        verbose_name_plural = "用户信息"

    def __str__(self):
        return self.user.username

class Permission(models.Model):
    """
    权限模型
    """
    PERMISSION_TYPES = (
        ('read', '只读'),
        ('write', '读写'),
        ('admin', '管理员'),
    )
    name = models.CharField(max_length=100, verbose_name="权限名称")
    code = models.CharField(max_length=100, unique=True, verbose_name="权限代码")
    type = models.CharField(max_length=20, choices=PERMISSION_TYPES, default='read', verbose_name="权限类型")
    description = models.TextField(blank=True, null=True, verbose_name="权限描述")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "权限"
        verbose_name_plural = "权限"

    def __str__(self):
        return self.name

class RolePermission(models.Model):
    """
    角色权限关联
    """
    role = models.ForeignKey(Role, on_delete=models.CASCADE, related_name="permissions", verbose_name="角色")
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE, verbose_name="权限")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "角色权限"
        verbose_name_plural = "角色权限"
        unique_together = ('role', 'permission')

    def __str__(self):
        return f"{self.role.name} - {self.permission.name}"
