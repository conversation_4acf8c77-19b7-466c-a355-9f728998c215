from django.shortcuts import render
from django.db import transaction
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
import json
from django.db.models import Q, Count
import re
from datetime import datetime, timedelta, timezone
from rest_framework.pagination import PageNumberPagination
from rest_framework import serializers
from django.db.models.signals import post_migrate
from django.dispatch import receiver

from .models import AuditLog, SQLAuditRule, SQLAuditResult
from query.models import Query
from datasource.models import Datasource
from django.contrib.auth.models import User

# 初始化默认审计规则
def create_default_audit_rules():
    """创建默认的SQL审计规则"""
    # 只有当数据库中没有规则时才创建默认规则
    if SQLAuditRule.objects.count() == 0:
        default_rules = [
            {
                "name": "禁止删除表",
                "rule_type": "keyword",
                "pattern": "DROP\\s+TABLE",
                "description": "禁止使用DROP TABLE语句删除表",
                "risk_level": "high",
                "is_enabled": True
            },
            {
                "name": "全表扫描检测",
                "rule_type": "regex",
                "pattern": "SELECT\\s+\\*\\s+FROM\\s+[\\w\\.]+\\s+WHERE\\s+(?!.*\\sLIMIT\\s)",
                "description": "检测不带LIMIT条件的全表扫描操作",
                "risk_level": "medium",
                "is_enabled": True
            },
            {
                "name": "大批量更新检测",
                "rule_type": "regex",
                "pattern": "UPDATE\\s+[\\w\\.]+\\s+SET\\s+.*(?!.*\\sLIMIT\\s)",
                "description": "检测不带LIMIT条件的批量更新操作",
                "risk_level": "medium",
                "is_enabled": True
            },
            {
                "name": "检测WHERE 1=1条件",
                "rule_type": "regex",
                "pattern": "WHERE\\s+1\\s*=\\s*1",
                "description": "检测SQL中是否包含WHERE 1=1这种风险条件",
                "risk_level": "high",
                "is_enabled": True
            },
            {
                "name": "禁止使用TRUNCATE",
                "rule_type": "keyword",
                "pattern": "TRUNCATE\\s+TABLE",
                "description": "禁止使用TRUNCATE TABLE清空表数据",
                "risk_level": "high",
                "is_enabled": False
            }
        ]
        
        for rule_data in default_rules:
            SQLAuditRule.objects.create(**rule_data)
        
        print(f"已创建 {len(default_rules)} 条默认审计规则")

# 在Django应用程序加载后创建默认规则
@receiver(post_migrate)
def initialize_default_data(sender, **kwargs):
    if sender.name == 'audit':
        create_default_audit_rules()

# 添加序列化器
class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username']

class DatasourceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Datasource
        fields = ['id', 'name', 'db_type']

class QuerySerializer(serializers.ModelSerializer):
    class Meta:
        model = Query
        fields = ['id', 'sql_content']

class AuditLogSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    datasource = DatasourceSerializer()
    query = QuerySerializer()
    
    class Meta:
        model = AuditLog
        fields = ['id', 'user', 'action_type', 'action_time', 'ip_address', 
                 'content', 'risk_level', 'datasource', 'query']

class SQLAuditRuleSerializer(serializers.ModelSerializer):
    created_by = UserSerializer()
    
    class Meta:
        model = SQLAuditRule
        fields = '__all__'

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def audit_log_list(request):
    """获取审计日志列表"""
    user = request.user
    
    # 条件筛选参数
    keyword = request.query_params.get('keyword')  # 用于筛选操作内容
    user_id = request.query_params.get('user_id')
    action_type = request.query_params.get('action_type')
    risk_level = request.query_params.get('risk_level')
    start_date = request.query_params.get('start_date')
    end_date = request.query_params.get('end_date')
    exclude_query = request.query_params.get('exclude_query', 'false').lower() == 'true'
    page = int(request.query_params.get('page', 1))
    limit = int(request.query_params.get('limit', 20))
    
    # 计算偏移量
    offset = (page - 1) * limit
    
    # 构建查询条件
    query = Q()
    
    # 关键词搜索 - 只针对操作内容进行筛选
    if keyword:
        query &= Q(content__icontains=keyword)
    
    # 非管理员只能查看自己的日志
    if not (user.is_superuser or user.is_staff):
        query &= Q(user=user)
    else:
        # 管理员可按用户筛选
        if user_id:
            query &= Q(user_id=user_id)
    
    # 按操作类型筛选 - 支持多选
    if action_type:
        action_types = action_type.split(',')
        if action_types:
            action_type_query = Q()
            for action in action_types:
                if action.strip():
                    action_type_query |= Q(action_type=action.strip())
            query &= action_type_query
    
    # 排除查询类SQL
    if exclude_query:
        query &= ~Q(action_type__in=['query', 'select'])
    
    # 按风险等级筛选 - 支持多选
    if risk_level:
        risk_levels = risk_level.split(',')
        if risk_levels:
            risk_level_query = Q()
            for level in risk_levels:
                if level.strip():
                    risk_level_query |= Q(risk_level=level.strip())
            query &= risk_level_query
    
    # 按日期范围筛选
    # 如果没有指定日期范围，默认为今天
    if not start_date and not end_date:
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        query &= Q(action_time__gte=today)
        end_of_day = today.replace(hour=23, minute=59, second=59)
        query &= Q(action_time__lte=end_of_day)
    else:
        if start_date:
            query &= Q(action_time__gte=start_date)
        if end_date:
            # 确保包含结束当天的所有记录
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
            end_date_obj = end_date_obj.replace(hour=23, minute=59, second=59)
            query &= Q(action_time__lte=end_date_obj)
    
    # 获取审计日志
    logs = AuditLog.objects.filter(query).select_related('user', 'query').order_by('-action_time')
    
    # 分页
    total = logs.count()
    logs = logs[offset:offset+limit]
    
    # 计算各风险等级的数量
    risk_count = {}
    if total > 0:
        risk_stats = AuditLog.objects.filter(query).values('risk_level').annotate(count=Count('id'))
        for stat in risk_stats:
            risk_count[stat['risk_level']] = stat['count']
    
    # 使用序列化器处理数据
    serializer = AuditLogSerializer(logs, many=True)
    
    # 将查询结果转换为前端需要的格式
    result = []
    for item in serializer.data:
        log_item = {
            'id': item['id'],
            'username': item['user']['username'] if item['user'] else None,
            'action_type': item['action_type'],
            'timestamp': item['action_time'],
            'clientIp': item['ip_address'],
            'sqlContent': item['query']['sql_content'] if item['query'] else item['content'],
            'content': item['content'],  # 添加操作内容字段
            'riskLevel': item['risk_level'],
        }
        
        # 添加数据库信息
        if item.get('datasource'):
            log_item['database'] = {
                'id': item['datasource']['id'],
                'name': item['datasource']['name'],
                'type': item['datasource']['db_type']
            }
        else:
            log_item['database'] = {
                'id': None,
                'name': 'N/A',
                'type': 'Unknown'
            }
        
        result.append(log_item)
    
    response_data = {
        'total': total,
        'items': result,
        'risk_count': risk_count
    }
    
    return Response(response_data)

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def audit_rules(request):
    """获取或新增SQL审计规则"""
    # 获取规则列表
    if request.method == 'GET':
        rules = SQLAuditRule.objects.all()
        
        # 过滤条件
        name = request.query_params.get('name')
        level = request.query_params.get('level')
        is_enabled = request.query_params.get('is_enabled')
        
        if name:
            rules = rules.filter(name__icontains=name)
        if level:
            rules = rules.filter(level=level)
        if is_enabled is not None:
            is_enabled = is_enabled.lower() == 'true'
            rules = rules.filter(is_enabled=is_enabled)
        
        # 分页
        paginator = PageNumberPagination()
        paginator.page_size = 10
        result_page = paginator.paginate_queryset(rules, request)
        
        serializer = SQLAuditRuleSerializer(result_page, many=True)
        return paginator.get_paginated_response(serializer.data)
    
    # 新增规则
    elif request.method == 'POST':
        serializer = SQLAuditRuleSerializer(data=request.data)
        if serializer.is_valid():
            rule = serializer.save(created_by=request.user)
            # 记录审计日志
            AuditLog.objects.create(
                user=request.user,
                action_type='audit_rule_add',
                ip_address=request.META.get('REMOTE_ADDR'),
                content=f"用户 {request.user.username} 新增了审计规则: {rule.name}"
            )
            return Response(SQLAuditRuleSerializer(rule).data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def audit_rule_detail(request, pk):
    """获取、更新或删除审计规则"""
    user = request.user
    
    try:
        rule = SQLAuditRule.objects.get(pk=pk)
        
        # 权限检查: 只有管理员可以修改或删除规则
        if request.method in ['PUT', 'DELETE'] and not (user.is_superuser or user.is_staff):
            return Response({
                'success': False,
                'message': '您没有权限执行此操作'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # 获取规则详情
        if request.method == 'GET':
            return Response({
                'id': rule.id,
                'name': rule.name,
                'rule_type': rule.rule_type,
                'pattern': rule.pattern,
                'description': rule.description,
                'risk_level': rule.risk_level,
                'is_enabled': rule.is_enabled,
                'created_by': rule.created_by.username if rule.created_by else None,
                'created_at': rule.created_at,
                'updated_at': rule.updated_at,
            })
        
        # 更新规则
        elif request.method == 'PUT':
            serializer = SQLAuditRuleSerializer(rule, data=request.data, partial=True)
            if serializer.is_valid():
                updated_rule = serializer.save()
                # 记录审计日志
                AuditLog.objects.create(
                    user=request.user,
                    action_type='audit_rule_edit',
                    ip_address=request.META.get('REMOTE_ADDR'),
                    content=f"用户 {request.user.username} 修改了审计规则: {updated_rule.name}"
                )
                return Response(SQLAuditRuleSerializer(updated_rule).data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        # 删除规则
        elif request.method == 'DELETE':
            rule_name = rule.name
            rule.delete()
            # 记录审计日志
            AuditLog.objects.create(
                user=request.user,
                action_type='audit_rule_delete',
                ip_address=request.META.get('REMOTE_ADDR'),
                content=f"用户 {request.user.username} 删除了审计规则: {rule_name}"
            )
            return Response(status=status.HTTP_204_NO_CONTENT)
    
    except SQLAuditRule.DoesNotExist:
        return Response({'error': '规则未找到'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'message': f'操作失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def audit_result_list(request, query_id):
    """获取查询的审计结果"""
    user = request.user
    
    try:
        query = Query.objects.get(pk=query_id)
        
        # 权限检查: 非管理员只能查看自己的查询的审计结果
        if not (user.is_superuser or user.is_staff) and query.user != user:
            return Response({
                'success': False,
                'message': '您没有权限查看此查询的审计结果'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # 获取审计结果
        audit_results = SQLAuditResult.objects.filter(query=query)
        
        # 组装结果
        result = []
        for audit in audit_results:
            result.append({
                'id': audit.id,
                'rule': {
                    'id': audit.rule.id,
                    'name': audit.rule.name,
                    'rule_type': audit.rule.rule_type,
                    'description': audit.rule.description,
                },
                'matched_content': audit.matched_content,
                'risk_level': audit.risk_level,
                'created_at': audit.created_at,
            })
        
        return Response(result)
    
    except Query.DoesNotExist:
        return Response({
            'success': False,
            'message': '查询记录不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'message': f'操作失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def export_audit_logs(request):
    """导出审计日志"""
    if request.method == 'POST':
        # 使用request.data替代json.loads(request.body)
        start_date = request.data.get('start_date')
        end_date = request.data.get('end_date')
        user_id = request.data.get('user_id')
        action_type = request.data.get('action_type')
        
        # 构建查询条件
        query = {}
        
        if start_date:
            query['created_at__gte'] = datetime.strptime(start_date, '%Y-%m-%d').replace(tzinfo=timezone.utc)
        if end_date:
            query['created_at__lte'] = (datetime.strptime(end_date, '%Y-%m-%d').replace(tzinfo=timezone.utc) + 
                                       timedelta(days=1))
        if user_id:
            query['user_id'] = user_id
        if action_type:
            query['action_type'] = action_type
        
        # 获取符合条件的日志
        logs = AuditLog.objects.filter(**query).order_by('-created_at')
        
        # 准备导出数据
        export_data = []
        for log in logs:
            export_data.append({
                'id': log.id,
                'user': log.user.username if log.user else 'System',
                'action_type': log.action_type,
                'ip_address': log.ip_address,
                'content': log.content,
                'datasource': log.datasource.name if log.datasource else None,
                'query_id': log.query.id if log.query else None,
                'risk_level': log.risk_level,
                'created_at': log.created_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        
        # 记录导出操作
        AuditLog.objects.create(
            user=request.user,
            action_type='export_audit_logs',
            ip_address=request.META.get('REMOTE_ADDR'),
            content=f"导出审计日志: {len(export_data)}条记录"
        )
        
        return Response({
            'success': True,
            'data': export_data
        })
    else:
        # 获取可用的操作类型
        action_types = AuditLog.objects.values_list('action_type', flat=True).distinct()
        
        return Response({
            'success': True,
            'action_types': list(action_types)
        })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def sql_audit_log_list(request):
    """获取SQL审计日志列表"""
    user = request.user
    
    # 条件筛选参数
    keyword = request.query_params.get('keyword')
    datasource_id = request.query_params.get('database_id')  # 前端传来的是database_id
    risk_level = request.query_params.get('risk_level')
    start_date = request.query_params.get('start_date')
    end_date = request.query_params.get('end_date')
    page = int(request.query_params.get('page', 1))
    limit = int(request.query_params.get('limit', 20))
    
    # 计算偏移量
    offset = (page - 1) * limit
    
    # 构建查询条件
    query = Q()
    
    # 关键词搜索
    if keyword:
        query &= Q(sql_content__icontains=keyword)
    
    # 非管理员只能查看自己的日志
    if not (user.is_superuser or user.is_staff):
        query &= Q(user=user)
    
    # 按数据源筛选
    if datasource_id:
        query &= Q(datasource_id=datasource_id)
    
    # 按风险等级筛选 - 支持多选
    if risk_level:
        risk_levels_list = risk_level.split(',')
        if risk_levels_list:
            # 获取这些风险等级的所有审计结果关联的查询ID
            risk_queries = SQLAuditResult.objects.filter(risk_level__in=risk_levels_list).values_list('query_id', flat=True).distinct()
            if risk_queries:
                query &= Q(id__in=risk_queries)
    
    # 按日期范围筛选
    if start_date:
        query &= Q(start_time__gte=start_date)
    if end_date:
        # 确保包含结束当天的所有记录
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
        end_date_obj = end_date_obj.replace(hour=23, minute=59, second=59)
        query &= Q(start_time__lte=end_date_obj)
    
    # 获取SQL审计日志
    logs = Query.objects.filter(query).select_related('user', 'datasource').order_by('-start_time')
    
    # 分页
    total = logs.count()
    logs = logs[offset:offset+limit]
    
    # 计算各风险等级的数量
    risk_count = {
        'high': 0,
        'medium': 0,
        'low': 0
    }
    
    # 获取所有查询ID
    query_ids = [log.id for log in logs]
    
    # 获取这些查询的最高风险等级
    if query_ids:
        risk_levels = {
            'high': 3,
            'medium': 2,
            'low': 1,
            'none': 0
        }
        
        query_risks = {}
        for result in SQLAuditResult.objects.filter(query_id__in=query_ids):
            query_id = result.query_id
            risk_level = result.risk_level
            risk_score = risk_levels.get(risk_level, 0)
            
            if query_id not in query_risks or risk_score > risk_levels.get(query_risks[query_id], 0):
                query_risks[query_id] = risk_level
        
        # 统计各风险等级的数量
        for risk in query_risks.values():
            if risk in risk_count:
                risk_count[risk] += 1
    
    # 构建响应数据
    result = []
    for log in logs:
        # 获取该查询的所有审计结果
        audit_results = SQLAuditResult.objects.filter(query=log)
        
        # 确定最高风险等级
        max_risk_level = 'none'
        risk_detail = None
        for audit in audit_results:
            if risk_levels.get(audit.risk_level, 0) > risk_levels.get(max_risk_level, 0):
                max_risk_level = audit.risk_level
                risk_detail = f"{audit.rule.name}: {audit.rule.description}"
        
        log_item = {
            'id': log.id,
            'username': log.user.username,
            'execute_time': log.start_time.isoformat(),
            'client_ip': log.user.last_login.isoformat() if log.user.last_login else None,
            'sql_content': log.sql_content,
            'risk_level': max_risk_level,
            'risk_detail': risk_detail,
            'affect_rows': log.affected_rows,
            'database': {
                'id': log.datasource.id,
                'name': log.datasource.name,
                'type': str(log.datasource.db_type)
            }
        }
        
        result.append(log_item)
    
    response_data = {
        'total': total,
        'items': result,
        'risk_count': risk_count
    }
    
    return Response(response_data)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def export_sql_audit_logs(request):
    """导出SQL审计日志"""
    user = request.user
    
    # 条件筛选参数
    keyword = request.query_params.get('keyword')
    datasource_id = request.query_params.get('database_id')
    risk_level = request.query_params.get('risk_level')
    start_date = request.query_params.get('start_date')
    end_date = request.query_params.get('end_date')
    
    # 构建查询条件
    query = Q()
    
    # 关键词搜索
    if keyword:
        query &= Q(sql_content__icontains=keyword)
    
    # 非管理员只能查看自己的日志
    if not (user.is_superuser or user.is_staff):
        query &= Q(user=user)
    
    # 按数据源筛选
    if datasource_id:
        query &= Q(datasource_id=datasource_id)
    
    # 按风险等级筛选 - 支持多选
    if risk_level:
        risk_levels_list = risk_level.split(',')
        if risk_levels_list:
            # 获取这些风险等级的所有审计结果关联的查询ID
            risk_queries = SQLAuditResult.objects.filter(risk_level__in=risk_levels_list).values_list('query_id', flat=True).distinct()
            if risk_queries:
                query &= Q(id__in=risk_queries)
    
    # 按日期范围筛选
    if start_date:
        query &= Q(start_time__gte=start_date)
    if end_date:
        # 确保包含结束当天的所有记录
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
        end_date_obj = end_date_obj.replace(hour=23, minute=59, second=59)
        query &= Q(start_time__lte=end_date_obj)
    
    # 获取SQL审计日志
    logs = Query.objects.filter(query).select_related('user', 'datasource').order_by('-start_time')
    
    # 导出CSV
    import csv
    from django.http import HttpResponse
    
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="sql_audit_logs_{datetime.now().strftime("%Y%m%d%H%M%S")}.csv"'
    
    writer = csv.writer(response)
    writer.writerow(['ID', '执行用户', '执行时间', 'SQL内容', '数据源', '风险等级', '影响行数', '风险详情'])
    
    risk_levels = {
        'high': 3,
        'medium': 2,
        'low': 1,
        'none': 0
    }
    
    for log in logs:
        # 获取该查询的所有审计结果
        audit_results = SQLAuditResult.objects.filter(query=log)
        
        # 确定最高风险等级
        max_risk_level = 'none'
        risk_detail = None
        for audit in audit_results:
            if risk_levels.get(audit.risk_level, 0) > risk_levels.get(max_risk_level, 0):
                max_risk_level = audit.risk_level
                risk_detail = f"{audit.rule.name}: {audit.rule.description}"
        
        risk_level_text = {
            'high': '高风险',
            'medium': '中风险',
            'low': '低风险',
            'none': '无风险'
        }.get(max_risk_level, '未知')
        
        writer.writerow([
            log.id,
            log.user.username,
            log.start_time.strftime('%Y-%m-%d %H:%M:%S'),
            log.sql_content,
            log.datasource.name,
            risk_level_text,
            log.affected_rows,
            risk_detail or ''
        ])
    
    return response

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def check_sql_with_rules(request):
    """检查SQL是否符合审计规则"""
    try:
        # 获取请求数据
        sql_content = request.data.get('sql_content')
        database_id = request.data.get('database_id')
        
        if not sql_content:
            return Response({"error": "SQL内容不能为空"}, status=status.HTTP_400_BAD_REQUEST)
        
        # 获取所有启用的审计规则
        rules = SQLAuditRule.objects.filter(is_enabled=True)
        
        # 检查SQL是否符合规则
        matched_rules = []
        highest_risk = 'none'
        risk_detail = ''
        
        risk_levels = {
            'high': 3,
            'medium': 2,
            'low': 1,
            'none': 0
        }
        
        current_risk_level = 0
        
        for rule in rules:
            pattern = rule.pattern
            if rule.rule_type == 'keyword':
                # 关键字匹配 - 不区分大小写
                if re.search(pattern, sql_content, re.IGNORECASE):
                    matched_rules.append({
                        'id': rule.id,
                        'name': rule.name,
                        'risk_level': rule.risk_level,
                        'description': rule.description
                    })
                    
                    # 更新最高风险等级
                    rule_risk_level = risk_levels.get(rule.risk_level, 0)
                    if rule_risk_level > current_risk_level:
                        current_risk_level = rule_risk_level
                        highest_risk = rule.risk_level
                        risk_detail = rule.description
            
            elif rule.rule_type == 'regex':
                # 正则表达式匹配
                try:
                    if re.search(pattern, sql_content, re.IGNORECASE):
                        matched_rules.append({
                            'id': rule.id,
                            'name': rule.name,
                            'risk_level': rule.risk_level,
                            'description': rule.description
                        })
                        
                        # 更新最高风险等级
                        rule_risk_level = risk_levels.get(rule.risk_level, 0)
                        if rule_risk_level > current_risk_level:
                            current_risk_level = rule_risk_level
                            highest_risk = rule.risk_level
                            risk_detail = rule.description
                except Exception as e:
                    print(f"正则表达式匹配错误: {e}")
        
        # 返回审计结果
        result = {
            'risk_level': highest_risk,
            'risk_detail': risk_detail,
            'matched_rules': matched_rules,
            'sql_content': sql_content
        }
        
        return Response(result, status=status.HTTP_200_OK)
    
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
