from django.db import models
from django.contrib.auth.models import User
from datasource.models import Datasource
from query.models import Query

class SQLAuditRule(models.Model):
    """
    SQL审计规则模型
    """
    RULE_TYPES = (
        ('keyword', '关键字'),
        ('regex', '正则表达式'),
        ('risk_level', '风险等级'),
    )
    
    RISK_LEVELS = (
        ('low', '低'),
        ('medium', '中'),
        ('high', '高'),
        ('critical', '严重'),
    )
    
    name = models.CharField(max_length=100, verbose_name="规则名称")
    rule_type = models.CharField(max_length=20, choices=RULE_TYPES, verbose_name="规则类型")
    pattern = models.CharField(max_length=255, verbose_name="匹配模式")
    description = models.TextField(blank=True, null=True, verbose_name="规则描述")
    risk_level = models.CharField(max_length=20, choices=RISK_LEVELS, default='medium', verbose_name="风险等级")
    is_enabled = models.BooleanField(default=True, verbose_name="是否启用")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name="created_rules", verbose_name="创建人")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "SQL审计规则"
        verbose_name_plural = "SQL审计规则"
        
    def __str__(self):
        return self.name

class AuditLog(models.Model):
    """
    审计日志模型
    """
    ACTION_TYPES = (
        ('login', '登录'),
        ('logout', '登出'),
        ('datasource_add', '新增数据源'),
        ('datasource_edit', '编辑数据源'),
        ('datasource_delete', '删除数据源'),
        ('audit_rule_add', '新增审计规则'),
        ('audit_rule_edit', '编辑审计规则'),
        ('audit_rule_delete', '删除审计规则'),
        ('permission_add', '新增权限'),
        ('permission_edit', '编辑权限'),
        ('permission_delete', '删除权限'),
    )
    
    RISK_LEVELS = (
        ('low', '低'),
        ('medium', '中'),
        ('high', '高'),
        ('critical', '严重'),
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="audit_logs", verbose_name="用户")
    action_type = models.CharField(max_length=30, choices=ACTION_TYPES, verbose_name="操作类型")
    action_time = models.DateTimeField(auto_now_add=True, verbose_name="操作时间")
    ip_address = models.GenericIPAddressField(blank=True, null=True, verbose_name="IP地址")
    datasource = models.ForeignKey(Datasource, on_delete=models.SET_NULL, null=True, blank=True, related_name="audit_logs", verbose_name="数据源")
    query = models.ForeignKey(Query, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="查询记录")
    content = models.TextField(blank=True, null=True, verbose_name="操作内容")
    risk_level = models.CharField(max_length=20, choices=RISK_LEVELS, default='low', verbose_name="风险等级")
    
    class Meta:
        verbose_name = "操作日志"
        verbose_name_plural = "操作日志"
        ordering = ['-action_time']
        db_table = 'audit_operationlog'
        
    def __str__(self):
        return f"{self.user.username} - {self.action_type} - {self.action_time}"

class SQLAuditResult(models.Model):
    """
    SQL审计结果模型
    """
    query = models.ForeignKey(Query, on_delete=models.CASCADE, related_name="audit_results", verbose_name="查询记录")
    rule = models.ForeignKey(SQLAuditRule, on_delete=models.CASCADE, verbose_name="触发规则")
    matched_content = models.TextField(verbose_name="匹配内容")
    risk_level = models.CharField(max_length=20, choices=SQLAuditRule.RISK_LEVELS, verbose_name="风险等级")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    
    class Meta:
        verbose_name = "SQL审计结果"
        verbose_name_plural = "SQL审计结果"
        
    def __str__(self):
        return f"{self.query} - {self.rule.name}"
