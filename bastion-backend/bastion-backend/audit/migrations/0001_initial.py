# Generated by Django 4.2.7 on 2025-05-23 03:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("datasource", "__first__"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("query", "__first__"),
    ]

    operations = [
        migrations.CreateModel(
            name="SQLAuditRule",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="规则名称")),
                (
                    "rule_type",
                    models.CharField(
                        choices=[
                            ("keyword", "关键字"),
                            ("regex", "正则表达式"),
                            ("risk_level", "风险等级"),
                        ],
                        max_length=20,
                        verbose_name="规则类型",
                    ),
                ),
                ("pattern", models.CharField(max_length=255, verbose_name="匹配模式")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="规则描述"),
                ),
                (
                    "risk_level",
                    models.CharField(
                        choices=[
                            ("low", "低"),
                            ("medium", "中"),
                            ("high", "高"),
                            ("critical", "严重"),
                        ],
                        default="medium",
                        max_length=20,
                        verbose_name="风险等级",
                    ),
                ),
                (
                    "is_enabled",
                    models.BooleanField(default=True, verbose_name="是否启用"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_rules",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建人",
                    ),
                ),
            ],
            options={
                "verbose_name": "SQL审计规则",
                "verbose_name_plural": "SQL审计规则",
            },
        ),
        migrations.CreateModel(
            name="SQLAuditResult",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("matched_content", models.TextField(verbose_name="匹配内容")),
                (
                    "risk_level",
                    models.CharField(
                        choices=[
                            ("low", "低"),
                            ("medium", "中"),
                            ("high", "高"),
                            ("critical", "严重"),
                        ],
                        max_length=20,
                        verbose_name="风险等级",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "query",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="audit_results",
                        to="query.query",
                        verbose_name="查询记录",
                    ),
                ),
                (
                    "rule",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="audit.sqlauditrule",
                        verbose_name="触发规则",
                    ),
                ),
            ],
            options={
                "verbose_name": "SQL审计结果",
                "verbose_name_plural": "SQL审计结果",
            },
        ),
        migrations.CreateModel(
            name="AuditLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("login", "登录"),
                            ("logout", "登出"),
                            ("query", "查询"),
                            ("export", "导出"),
                            ("datasource_add", "新增数据源"),
                            ("datasource_edit", "编辑数据源"),
                            ("datasource_delete", "删除数据源"),
                            ("permission_grant", "授权"),
                            ("permission_revoke", "撤销授权"),
                        ],
                        max_length=30,
                        verbose_name="操作类型",
                    ),
                ),
                (
                    "action_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="操作时间"),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="IP地址"
                    ),
                ),
                (
                    "content",
                    models.TextField(blank=True, null=True, verbose_name="操作内容"),
                ),
                (
                    "risk_level",
                    models.CharField(
                        choices=[
                            ("low", "低"),
                            ("medium", "中"),
                            ("high", "高"),
                            ("critical", "严重"),
                        ],
                        default="low",
                        max_length=20,
                        verbose_name="风险等级",
                    ),
                ),
                (
                    "datasource",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="audit_logs",
                        to="datasource.datasource",
                        verbose_name="数据源",
                    ),
                ),
                (
                    "query",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="query.query",
                        verbose_name="查询记录",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="audit_logs",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "操作日志",
                "verbose_name_plural": "操作日志",
                "db_table": "audit_operationlog",
                "ordering": ["-action_time"],
            },
        ),
    ]
