# Bastion 后端

## 数据库维护

### 清理旧查询日志

为了防止 `audit_sqllog` 表数据量过大，系统提供了自动清理旧查询日志的功能。

#### 手动清理

```bash
# 清理30天前的查询日志（默认值）
python manage.py cleanup_query_logs

# 清理指定天数前的查询日志
python manage.py cleanup_query_logs --days 60

# 仅显示将要删除的记录数量，不实际删除
python manage.py cleanup_query_logs --dry-run
```

#### 设置定时清理

建议设置 cron 任务定期清理旧日志，例如：

```bash
# 每天凌晨3点执行清理，保留最近30天的日志
0 3 * * * cd /path/to/bastion-backend && python manage.py cleanup_query_logs >> /path/to/logs/cleanup.log 2>&1
```

添加 cron 任务的方法：

1. 编辑 crontab
   ```bash
   crontab -e
   ```

2. 添加上述命令行
   ```
   0 3 * * * cd /path/to/bastion-backend && python manage.py cleanup_query_logs >> /path/to/logs/cleanup.log 2>&1
   ```

3. 保存并退出

### 查询日志存储优化

系统已优化查询日志的存储方式，不再保存完整的查询结果数据，只保存必要的元数据信息，以减少数据库存储空间的占用。 