from django.contrib.auth.models import User
from authentication.models import UserProfile

def create_user_profiles():
    for user in User.objects.all():
        try:
            # 检查用户是否已有档案
            profile = UserProfile.objects.get(user=user)
            print(f"用户 {user.username} 已有档案")
        except UserProfile.DoesNotExist:
            # 创建新档案
            UserProfile.objects.create(user=user)
            print(f"为用户 {user.username} 创建了档案")

if __name__ == "__main__":
    import os
    import django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'bastion.settings')
    django.setup()
    create_user_profiles() 