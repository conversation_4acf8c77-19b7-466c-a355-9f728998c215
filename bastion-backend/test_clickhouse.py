#!/usr/bin/env python
"""
ClickHouse测试脚本
用于直接测试ClickHouse查询结果的格式
"""

import sys
import json
import datetime
import clickhouse_driver

def json_serializer(obj):
    """处理JSON序列化特殊类型"""
    if isinstance(obj, (datetime.date, datetime.datetime)):
        return obj.isoformat()
    return str(obj)

def main():
    """主函数"""
    print("连接ClickHouse...")
    
    # 连接参数 - 根据实际情况修改
    connection_params = {
        'host': '*************',  # 修改为实际的ClickHouse主机
        'port': 9001,         # 修改为实际的ClickHouse端口
        'user': 'default',    # 修改为实际的用户名
        'password': 'ntK8BR2hDg',       # 修改为实际的密码
        'database': 'sls_log' # 修改为实际的数据库名
    }
    
    try:
        # 创建连接
        client = clickhouse_driver.Client(**connection_params)
        print(f"连接成功: {connection_params['host']}:{connection_params['port']}")
        
        # 执行查询
        query = "SELECT * FROM sls_count LIMIT 5"  # 修改为实际的查询
        print(f"执行查询: {query}")
        
        # 带列类型信息的查询
        result = client.execute(query, with_column_types=True)
        
        # 检查结果格式
        print("\n===== 查询结果格式 =====")
        print(f"结果类型: {type(result)}")
        
        if isinstance(result, tuple) and len(result) == 2:
            rows, columns_with_types = result
            print(f"行数: {len(rows)}")
            print(f"列信息: {columns_with_types}")
            
            # 打印第一行数据详情
            if rows and len(rows) > 0:
                first_row = rows[0]
                print("\n===== 第一行数据 =====")
                print(f"类型: {type(first_row)}")
                
                if isinstance(first_row, (list, tuple)):
                    print(f"长度: {len(first_row)}")
                    for i, value in enumerate(first_row):
                        print(f"  值[{i}]: {value} (类型: {type(value)})")
                else:
                    print(f"值: {first_row}")
            
            # 提取列名
            column_names = [col_name for col_name, col_type in columns_with_types]
            print(f"\n列名: {column_names}")
            
            # 构建字典格式的结果
            dict_rows = []
            for row in rows:
                dict_row = {}
                for i, col_name in enumerate(column_names):
                    if i < len(row):
                        dict_row[col_name] = row[i]
                dict_rows.append(dict_row)
            
            # 打印字典格式的第一行
            if dict_rows:
                print("\n===== 字典格式的第一行 =====")
                try:
                    print(json.dumps(dict_rows[0], indent=2, default=json_serializer))
                except Exception as e:
                    print(f"JSON序列化失败: {e}")
                    print("字段详情:")
                    for key, value in dict_rows[0].items():
                        print(f"  {key}: {value} (类型: {type(value)})")
        else:
            print(f"意外的结果格式: {result}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 