from django.urls import path
from . import views

urlpatterns = [
    path('execute/', views.execute_query, name='execute_query'),
    path('history/', views.query_history, name='query_history'),
    path('history/<int:pk>/', views.query_detail, name='query_detail'),
    path('history/<int:pk>/delete/', views.delete_query_record, name='delete_query_record'),
    path('saved/', views.saved_query_list, name='saved_query_list'),
    path('saved/<int:pk>/', views.saved_query_detail, name='saved_query_detail'),
    path('database/tables/', views.get_database_tables, name='get_database_tables'),
    path('database/columns/', views.get_table_columns, name='get_table_columns'),
    path('database/full-metadata/', views.get_database_full_metadata, name='get_database_full_metadata'),
    path('table/structure/<int:datasource_id>/<str:table_name>/', views.get_table_structure, name='get_table_structure'),
    path('table/indexes/<int:datasource_id>/<str:table_name>/', views.get_table_indexes, name='get_table_indexes'),
    path('table/ddl/<int:datasource_id>/<str:table_name>/', views.get_table_ddl, name='get_table_ddl'),
    path('execute-ddl/', views.execute_ddl, name='execute_ddl'),
    path('table/export/structure/<int:datasource_id>/<str:table_name>/', views.export_table_structure, name='export_table_structure'),
    path('table/export/data/<int:datasource_id>/<str:table_name>/', views.export_table_data, name='export_table_data'),
    path('table/edit/<int:datasource_id>/<str:table_name>/', views.edit_table_data, name='edit_table_data'),
] 