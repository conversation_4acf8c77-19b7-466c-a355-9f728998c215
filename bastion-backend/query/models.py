from django.db import models
from django.contrib.auth.models import User
from datasource.models import Datasource
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

class Query(models.Model):
    """
    SQL审计日志模型
    """
    STATUS_CHOICES = (
        ('running', '运行中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    )
    
    datasource = models.ForeignKey(Datasource, on_delete=models.CASCADE, related_name="queries", verbose_name="数据源")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="queries", verbose_name="执行用户")
    sql_content = models.TextField(verbose_name="SQL内容")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='running', verbose_name="状态")
    start_time = models.DateTimeField(auto_now_add=True, verbose_name="开始时间")
    end_time = models.DateTimeField(blank=True, null=True, verbose_name="结束时间")
    result = models.TextField(blank=True, null=True, verbose_name="执行结果")
    error_message = models.TextField(blank=True, null=True, verbose_name="错误信息")
    affected_rows = models.IntegerField(default=0, verbose_name="影响行数")
    execution_time = models.FloatField(default=0, verbose_name="执行时间(秒)")
    
    class Meta:
        verbose_name = "SQL审计日志"
        verbose_name_plural = "SQL审计日志"
        ordering = ['-start_time']
        db_table = 'audit_sqllog'
        
    def __str__(self):
        return f"{self.user.username} - {self.datasource.name} - {self.start_time}"
    
    @staticmethod
    def cleanup_old_records(days=30):
        """
        清理指定天数前的查询记录
        
        Args:
            days: 保留最近多少天的记录，默认30天
            
        Returns:
            int: 删除的记录数量
        """
        try:
            cutoff_date = timezone.now() - timezone.timedelta(days=days)
            old_records = Query.objects.filter(start_time__lt=cutoff_date)
            count = old_records.count()
            if count > 0:
                logger.info(f"正在删除 {count} 条 {days} 天前的旧查询记录")
                old_records.delete()
                logger.info(f"成功删除 {count} 条旧查询记录")
                return count
            return 0
        except Exception as e:
            logger.exception(f"清理旧查询记录时出错: {str(e)}")
            return 0

class SavedQuery(models.Model):
    """
    保存的查询模型
    """
    name = models.CharField(max_length=100, verbose_name="查询名称")
    datasource = models.ForeignKey(Datasource, on_delete=models.CASCADE, related_name="saved_queries", verbose_name="数据源")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="saved_queries", verbose_name="创建用户")
    sql_content = models.TextField(verbose_name="SQL内容")
    description = models.TextField(blank=True, null=True, verbose_name="描述")
    is_public = models.BooleanField(default=False, verbose_name="是否公开")
    schema = models.CharField(max_length=100, blank=True, null=True, verbose_name="Schema")
    visibility = models.CharField(
        max_length=10, 
        choices=[('private', 'Private'), ('shared', 'Shared'), ('public', 'Public')], 
        default='private', 
        verbose_name="可见性"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "保存的查询"
        verbose_name_plural = "保存的查询"
        ordering = ['-updated_at']
        
    def __str__(self):
        return self.name
