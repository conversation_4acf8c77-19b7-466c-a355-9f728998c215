"""
测试获取数据库 Schema 的 API
使用方法: python test_schema_api.py [datasource_id] [schema_name]
"""

import sys
import os
import requests
import json
import django

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth import get_user_model
from datasource.models import Datasource
from datasource.adapters import get_adapter

def test_api_endpoint(datasource_id, schema_name=''):
    """测试 API 端点"""
    print(f"测试 API 端点: /api/datasource/database-schema/{datasource_id}/{schema_name or '_empty_'}/")
    
    # 获取超级用户作为测试用户
    User = get_user_model()
    admin_user = User.objects.filter(is_superuser=True).first()
    
    if not admin_user:
        print("错误：找不到超级用户，请先创建一个超级用户")
        return
    
    # 创建一个测试会话
    session = requests.Session()
    
    # 直接测试 API 端点
    try:
        # 使用 Django 的测试客户端
        from django.test import Client
        client = Client()
        
        # 强制登录管理员用户
        client.force_login(admin_user)
        
        # 发送 GET 请求
        url = f"/api/datasource/database-schema/{datasource_id}/{schema_name or '_empty_'}/"
        print(f"发送 GET 请求到: {url}")
        response = client.get(url)
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.content.decode('utf-8')[:500]}...")
        
        # 尝试解析 JSON 响应
        try:
            data = json.loads(response.content)
            if 'success' in data:
                print(f"成功状态: {data['success']}")
            if 'schema' in data:
                schema = data['schema']
                print(f"Schema 数据类型: {type(schema)}")
                if isinstance(schema, dict):
                    if 'tables' in schema:
                        print(f"表数量: {len(schema['tables'])}")
                    if 'views' in schema:
                        print(f"视图数量: {len(schema['views'])}")
        except json.JSONDecodeError:
            print("无法解析 JSON 响应")
    
    except Exception as e:
        print(f"请求失败: {e}")

def test_direct_adapter(datasource_id, schema_name=''):
    """直接测试适配器"""
    print(f"\n直接测试适配器 get_schema 方法，数据源 ID: {datasource_id}, Schema: {schema_name}")
    
    try:
        # 获取数据源
        datasource = Datasource.objects.get(pk=datasource_id)
        print(f"找到数据源: {datasource.name}, 类型: {datasource.db_type.name}")
        
        # 获取适配器
        adapter = get_adapter(datasource)
        print(f"获取适配器: {adapter.__class__.__name__}")
        
        # 直接调用 get_schema 方法
        schema = adapter.get_schema(schema_name)
        
        # 打印结果
        print(f"适配器返回的数据类型: {type(schema)}")
        if isinstance(schema, dict):
            if 'tables' in schema:
                print(f"表数量: {len(schema['tables'])}")
                if schema['tables']:
                    print(f"第一个表示例: {schema['tables'][0]}")
            if 'views' in schema:
                print(f"视图数量: {len(schema['views'])}")
                if schema['views']:
                    print(f"第一个视图示例: {schema['views'][0]}")
        else:
            print(f"Schema 数据: {schema}")
    
    except Datasource.DoesNotExist:
        print(f"错误：找不到 ID 为 {datasource_id} 的数据源")
    except Exception as e:
        print(f"测试适配器时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 解析命令行参数
    datasource_id = sys.argv[1] if len(sys.argv) > 1 else "1"
    schema_name = sys.argv[2] if len(sys.argv) > 2 else ""
    
    # 测试 API 端点
    test_api_endpoint(datasource_id, schema_name)
    
    # 测试适配器
    test_direct_adapter(datasource_id, schema_name) 