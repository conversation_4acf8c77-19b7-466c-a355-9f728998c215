<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的SQL | 执行结果</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
            font-size: 12px;
        }
        .container {
            padding: 10px;
        }
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 10px;
            background-color: #fff;
            border-bottom: 1px solid #e8e8e8;
        }
        .toolbar-left, .toolbar-right {
            display: flex;
            align-items: center;
        }
        .btn {
            padding: 4px 8px;
            margin: 0 2px;
            background-color: #fff;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            cursor: pointer;
            font-size: 12px;
        }
        .btn:hover {
            background-color: #f5f5f5;
        }
        .search-input {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            width: 200px;
            margin-left: 10px;
        }
        .result-info {
            padding: 5px 10px;
            background-color: #fff;
            border-bottom: 1px solid #e8e8e8;
            color: #666;
        }
        .result-table {
            width: 100%;
            border-collapse: collapse;
            background-color: #fff;
        }
        .result-table th, .result-table td {
            border: 1px solid #e8e8e8;
            padding: 8px 12px;
            text-align: left;
        }
        .result-table th {
            background-color: #fafafa;
            font-weight: 500;
        }
        .result-table tr:hover {
            background-color: #f5f5f5;
        }
        .pagination {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background-color: #fff;
            border-top: 1px solid #e8e8e8;
        }
        .status-bar {
            padding: 5px 10px;
            background-color: #fff;
            border-top: 1px solid #e8e8e8;
            color: #666;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="toolbar">
            <div class="toolbar-left">
                <span style="font-weight: bold;">我的SQL | @ 执行结果1</span>
            </div>
            <div class="toolbar-right">
                <button class="btn">单行详情</button>
                <button class="btn">开启编辑</button>
                <button class="btn">生成图表</button>
                <button class="btn">刷新</button>
                <input type="text" class="search-input" placeholder="请输入">
            </div>
        </div>
        
        <div class="result-info">
            共 [4] 行
        </div>
        
        <table class="result-table">
            <thead>
                <tr>
                    <th>uid</th>
                    <th>nickname</th>
                    <th>mobile</th>
                    <th>order_id</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1940078264408199169</td>
                    <td>U星7856</td>
                    <td>#*UDREAM"#rgEhQWWM2/leFXSYtR8GLw==#*END*#</td>
                    <td>19400281990</td>
                </tr>
                <tr>
                    <td>1940078339865214978</td>
                    <td>UBA31192558</td>
                    <td>#* UDREAM*#Xjc/Q4hAgTqwhrN6XhVYrA==#*END *#</td>
                    <td>19399975047</td>
                </tr>
                <tr>
                    <td>1940078344013381633</td>
                    <td>UBA64199621</td>
                    <td>#* UDREAM*#3t6EUGXjOmqT4FS6cR9qMg==#*END*#</td>
                    <td>19400453447</td>
                </tr>
                <tr>
                    <td>1940078502606917634</td>
                    <td>、\es%回忆、不想忘 :er</td>
                    <td>#*UDREAM*#CVJc71hlIkFYWNIOIFnRpFw==#*END*#</td>
                    <td>19401 Gas</td>
                </tr>
            </tbody>
        </table>
        
        <div class="pagination">
            <div>
                <select class="btn">
                    <option>每页显示3000</option>
                    <option>每页显示100</option>
                    <option>每页显示500</option>
                    <option>每页显示1000</option>
                </select>
            </div>
            <div>
                <button class="btn">上一页</button>
                <button class="btn">下一页</button>
            </div>
        </div>
        
        <div class="status-bar">
            执行成功，当前返回 [4] 行，总耗时 [194ms] 更多数据请使用【数据导出】
        </div>
    </div>
</body>
</html>

