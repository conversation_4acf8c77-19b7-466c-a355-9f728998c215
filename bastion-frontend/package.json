{"name": "bastion-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@codemirror/lang-sql": "^6.8.0", "@codemirror/language": "^6.11.0", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.6", "axios": "^1.9.0", "echarts": "^5.6.0", "element-plus": "^2.9.8", "highlight.js": "^11.11.1", "monaco-editor": "^0.52.2", "pinia": "^3.0.2", "sql-formatter": "^15.6.6", "vue": "^3.5.13", "vue-codemirror": "^6.1.1", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.2", "sass-embedded": "^1.87.0", "vite": "^6.3.1"}}