/*
 * 这个补丁文件包含了修复单元格编辑后本地数据不更新的必要代码改动。
 * 请按照以下步骤应用这些改动到 ActiveQueryPanel.vue 和 EditableResultTable.vue 文件中。
 */

// =====================================================================
// 1. 修改 ActiveQueryPanel.vue 中的 submitEdit 函数
// 找到 submitEdit 函数（大约在3700行左右），将其替换为以下代码：
// =====================================================================

const submitEdit = () => {
  console.log('保存修改按钮点击');

  // 确保编辑值已设置
  if (cellEditValue.value === undefined || cellEditValue.value === null) {
    cellEditValue.value = cellDetailContent.value;
  }

  console.log('提交编辑 - 开始', { 
    cellEditValue: cellEditValue.value, 
    cellDetailContent: cellDetailContent.value,
    cellDetailTitle: cellDetailTitle.value,
    isEditing: isEditing.value,
    editingCellContext: editingCellContext.value
  });

  // 如果不在编辑状态，直接返回
  if (!isEditing.value) {
    console.warn('不在编辑状态，无法提交修改');
    return;
  }
  
  // 直接使用当前值创建一个简单的编辑上下文
  const processedColumnName = cellDetailTitle.value.replace('编辑', '').replace(':', '').trim();
  console.log('处理后的列名:', processedColumnName);
  
  // 如果还没有编辑上下文，先构建一个
  if (!editingCellContext.value) {
    // 获取当前行数据
    const currentEditRow = props.tab.currentEditingRow;
    if (!currentEditRow) {
      ElMessage.error('无法确定要编辑的行，请重新选择');
      console.error('更新失败：无法确定要编辑的行');
      isEditing.value = false;
      cellDetailVisible.value = false;
      return;
    }
    
    // 构建编辑上下文
    editingCellContext.value = {
      row: currentEditRow,
      column: processedColumnName,
      value: cellDetailContent.value
    };
    console.log('已构建编辑上下文:', editingCellContext.value);
  }
  
  const { row, column, value } = editingCellContext.value;
  
  // 检查列名是否有效
  if (!column || column === 'undefined' || typeof column === 'undefined') {
    ElMessage.error('无效的字段名');
    console.error('更新失败：字段名无效或未定义', { column });
    isEditing.value = false;
    return;
  }
  
  // 检查值是否有变化
  if (cellEditValue.value === value) {
    ElMessage.info('值未变化，无需更新');
    isEditing.value = false;
    return;
  }
  
  // 从SQL中提取表名
  const dbTableName = extractTableName();
  
  // 检查表名是否存在
  if (!dbTableName) {
    ElMessage.error('无法确定要更新的表名，请确保SQL中包含表名');
    console.error('更新失败：无法从SQL中提取表名', { sql: sqlContent.value });
    isEditing.value = false;
    return;
  }
  
  // 检查是否为ClickHouse数据源
  if (isClickhouseDataSource()) {
    ElMessage.error('ClickHouse数据源不支持修改现有数据，仅支持添加和删除操作');
    isEditing.value = false;
    cellDetailVisible.value = false;
    return;
  }
  
  console.log('准备提交更新:', {
    表名: dbTableName,
    字段: column,
    旧值: value,
    新值: cellEditValue.value,
    数据源: selectedDatasourceId.value,
    库: selectedSchema.value
  });
  
  // 创建一个清理过的row对象，排除Vue内部属性
  const rowData = row; // 使用已有的row对象
  // 创建清理过的行数据
  const processedRow = {};
  for (const key in rowData) {
    // 排除Vue内部属性
    if (!key.startsWith('__v') && key !== '_rowKey' && typeof rowData[key] !== 'function') {
      processedRow[key] = rowData[key];
    }
  }
  
  console.log('原始行数据:', rowData);
  console.log('清理后的行数据:', processedRow);

  // 已完成编辑，重置状态
  isEditing.value = false;
  
  // 关闭对话框
  cellDetailVisible.value = false;
  
  // 更新表格中的对应单元格
  if (editableResultTableRef.value) {
    try {
      // 调用表格组件的更新单元格方法
      editableResultTableRef.value.handleUpdateCellValue({
        type: 'update-cell',
        data: {
          row: rowData,
          columnKey: column,
          value: cellEditValue.value,
          originalValue: value
        }
      });
      
      // 确保表格数据更新
      nextTick(() => {
        if (editableResultTableRef.value && editableResultTableRef.value.tableData) {
          // 创建新数组引用以触发视图更新
          editableResultTableRef.value.tableData.value = [...editableResultTableRef.value.tableData.value];
          console.log('已强制更新表格数据视图');
        }
        
        // 显示成功消息
        ElMessage.success('已保存修改，请在底部操作栏点击"提交修改"完成更新');
      });
    } catch (error) {
      console.error('更新单元格值失败:', error);
      ElMessage.error('更新单元格失败: ' + error.message);
    }
  } else {
    console.error('找不到表格组件引用');
    ElMessage.error('无法更新表格，请刷新页面后重试');
  }
};

// =====================================================================
// 2. 在 EditableResultTable.vue 文件中，确保以下代码存在于 handleUpdateCellValue 函数中：
// =====================================================================

/*
// 添加处理update-cell-value事件的函数
const handleUpdateCellValue = (eventData) => {
  console.log('接收到更新单元格事件:', eventData);
  
  if (props.isReadOnly) {
    ElMessage.warning('当前处于只读模式，无法编辑数据');
    return;
  }
  
  if (props.isClickhouse) {
    ElMessage.warning('ClickHouse数据源不支持直接编辑操作');
    return;
  }
  
  if (eventData.type === 'update-cell' && eventData.data) {
    const { row, columnKey, value, originalValue } = eventData.data;
    
    // 在表格数据中查找并更新对应行的单元格值
    let targetRow = null;
    let rowKey = null;
    
    // 1. 首先尝试通过__v_key匹配
    if (row && row.__v_key !== undefined) {
      targetRow = tableData.value.find(r => r.__v_key === row.__v_key);
      if (targetRow) {
        rowKey = row.__v_key;
        console.log('通过__v_key找到目标行:', targetRow, '，rowKey:', rowKey);
      } else {
        console.log('通过__v_key未找到目标行，key值:', row.__v_key);
      }
    } else {
      console.log('行对象没有__v_key属性:', row);
    }
    
    // 2. 如果没找到，尝试通过id匹配
    if (!targetRow && row && row.id) {
      targetRow = tableData.value.find(r => r.id === row.id);
      if (targetRow) {
        // 确保目标行有__v_key
        if (targetRow.__v_key === undefined) {
          targetRow.__v_key = rowKeyCounter++;
          console.log('为找到的行分配新的__v_key:', targetRow.__v_key);
        }
        rowKey = targetRow.__v_key;
        console.log('通过id找到目标行:', targetRow, '，rowKey:', rowKey);
      }
    }
    
    // 3. 如果还没找到，尝试通过内容匹配
    if (!targetRow && row) {
      console.log('尝试通过内容匹配行');
      targetRow = tableData.value.find(r => {
        let matchCount = 0;
        let totalFields = 0;
        
        for (const key in row) {
          if (!key.startsWith('__v') && key !== columnKey) {
            totalFields++;
            if (r[key] === row[key]) {
              matchCount++;
            }
          }
        }
        
        // 如果80%以上的字段匹配，认为是同一行
        return totalFields > 0 && (matchCount / totalFields) > 0.8;
      });
      
      if (targetRow) {
        // 确保目标行有__v_key
        if (targetRow.__v_key === undefined) {
          targetRow.__v_key = rowKeyCounter++;
          console.log('为内容匹配的行分配新的__v_key:', targetRow.__v_key);
        }
        rowKey = targetRow.__v_key;
        console.log('通过内容匹配找到目标行:', targetRow, '，rowKey:', rowKey);
      }
    }
    
    if (targetRow) {
      // 构建变更对象
      const change = {
        type: 'update',
        row: targetRow,
        rowKey: rowKey,
        columnKey: columnKey,
        columnName: columnKey,
        value: value,
        newValue: value,
        originalValue: originalValue
      };
      
      console.log('准备添加变更到pendingChanges:', change);
      
      // 更新表格数据中的单元格值
      targetRow[columnKey] = value;
      
      // 添加到待提交更改列表
      addToPendingChanges(change);
      
      // 强制更新UI - 修改这里来确保视图更新
      nextTick(() => {
        console.log('变更后的pendingChanges:', pendingChanges.value);
        console.log('待提交状态:', pendingChanges.value.length > 0 ? '有待提交变更' : '无待提交变更');
        
        // 确保底部操作栏显示
        const bar = document.querySelector('.pending-changes-bar');
        if (bar && pendingChanges.value.length > 0) {
          bar.classList.add('show-changes-bar');
          console.log('已强制显示底部操作栏');
        }
        
        // 强制更新表格数据 - 创建新引用以触发视图更新
        tableData.value = [...tableData.value];
      });
    } else {
      console.error('无法找到匹配的行进行更新:', row);
      ElMessage.error('无法找到要更新的行，请刷新数据后重试');
    }
  }
};
*/ 