<template>
  <div class="sql-editor-component-wrapper">
    <Codemirror
      v-model="currentSql"
      :placeholder="props.placeholder"
      :style="{ height: '100%', width: '100%' }"
      :autofocus="props.autofocus"
      :indent-with-tab="props.indentWithTab"
      :tab-size="props.tabSize"
      :extensions="currentExtensions"
      @ready="handleEditorReady"
      @change="handleContentChange"
    />
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, onMounted, computed } from 'vue';
import { Codemirror } from 'vue-codemirror';
import { EditorView } from '@codemirror/view'; // Basic view, useful for some extensions or direct manipulation

// Define Props
const props = defineProps({
  sqlContent: { type: String, default: '' },
  extensions: { type: Array, default: () => [] },
  placeholder: { type: String, default: '请输入SQL...' },
  autofocus: { type: <PERSON>olean, default: true },
  indentWithTab: { type: Boolean, default: true },
  tabSize: { type: Number, default: 2 },
  readonly: { type: Boolean, default: false } // Added readonly prop
});

// Define Emits
const emit = defineEmits(['update:sqlContent', 'editorReady', 'change']);

// Local reactive state for SQL content
const currentSql = ref(props.sqlContent);
let editorView = null; // Store the editor view instance

// Default minimal extensions, can be overridden or augmented by props.extensions
const defaultExtensions = [
  EditorView.lineWrapping, 
  // Add other truly essential defaults if any, otherwise keep it minimal
  // as props.extensions should provide the main configuration (like sql language, theme etc.)
];

const currentExtensions = computed(() => {
  // Combine default extensions with those passed via props
  // Ensure no duplicates if props.extensions might also include similar base setup
  const combined = [...defaultExtensions, ...props.extensions];
  if (props.readonly) {
    combined.push(EditorView.editable.of(false));
  }
  return combined;
});

// Watch for external changes to sqlContent prop
watch(() => props.sqlContent, (newVal) => {
  if (newVal !== currentSql.value) {
    currentSql.value = newVal;
  }
});

// Handle editor ready event
function handleEditorReady(payload) {
  editorView = payload.view; // Capture the editor view instance
  emit('editorReady', payload); // Forward the original payload (includes view and state)
  
  // Apply readonly state if editor is already ready and prop changes
  if (props.readonly) {
    editorView.dispatch({
      effects: EditorView.editable.reconfigure(EditorView.editable.of(false))
    });
  }
}

// Handle content changes from the editor
function handleContentChange(value) {
  currentSql.value = value; // Update local ref
  emit('update:sqlContent', value); // Emit for v-model like binding
  emit('change', value); // Emit a general change event
}

// Watch for readonly prop changes to update editor state
watch(() => props.readonly, (newVal) => {
  if (editorView) {
    editorView.dispatch({
      effects: EditorView.editable.reconfigure(EditorView.editable.of(!newVal))
    });
  }
});

// Lifecycle hook (optional, for initial setup if needed)
onMounted(() => {
  // If props.sqlContent is initially set, Codemirror v-model should handle it.
});

// Expose any methods if parent needs to interact, e.g., focus
defineExpose({
  focus: () => editorView?.focus(),
  getView: () => editorView,
  getSelection: () => {
    if (!editorView) return '';
    
    const selection = editorView.state.selection.main;
    if (selection.empty) return '';
    
    return editorView.state.doc.sliceString(selection.from, selection.to);
  }
});

</script>

<style scoped>
.sql-editor-component-wrapper {
  height: 100%;
  width: 100%;
  overflow: hidden; /* Ensure Codemirror itself handles scrolling */
}

/* Ensure Codemirror takes full height/width via its own class if needed */
:deep(.cm-editor) {
  height: 100% !important;
  width: 100% !important;
}
</style> 