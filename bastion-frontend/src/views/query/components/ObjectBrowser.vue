<template>
  <div class="object-browser">
    <div class="browser-header">
      <div class="browser-title">
        <h3><el-icon><Fold /></el-icon> 数据库对象</h3>
      </div>
      <div class="search-container">
        <el-input 
          :model-value="objectFilter"
          @update:model-value="$emit('update:objectFilter', $event)"
          placeholder="搜索数据库对象..." 
          clearable 
          size="default"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-tooltip content="刷新数据源" placement="top">
          <el-button 
            type="primary" 
            size="small" 
            circle
            @click="refreshWithDelay"
            :disabled="isRefreshingDatasource" 
            class="refresh-button"
          >
            <el-icon :class="{ 'is-loading': isRefreshingDatasource }"><Refresh /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>
    
    <div class="browser-body">
      <!-- 加载中或刚切换Schema时显示骨架屏 -->
      <div v-if="treeLoading || isLoadingDelayed || justSwitchedSchema" class="loading-state">
        <el-skeleton :rows="8" animated />
        <div class="loading-message">
          <el-icon class="is-loading"><Refresh /></el-icon>
          {{ justSwitchedSchema ? '正在加载Schema数据...' : '加载中...' }}
        </div>
      </div>
      
      <!-- 未选择数据源时显示提示 -->
      <el-empty 
        v-else-if="!props.activeTabDatasourceId" 
        class="empty-state"
      >
        <template #image>
          <el-icon :size="48"><Connection /></el-icon>
        </template>
        <template #description>
          <p>请先选择数据源</p>
        </template>
      </el-empty>
      
      <!-- 无数据但非加载状态时显示空提示 -->
      <el-empty 
        v-else-if="filteredData.length === 0 && !objectFilter" 
        class="empty-state"
      >
        <template #image>
          <el-icon :size="48"><Box /></el-icon>
        </template>
        <template #description>
          <p>{{ emptyStateText }}</p>
          <el-button type="primary" size="small" @click="refreshWithDelay">
            刷新数据源
          </el-button>
        </template>
      </el-empty>
      
      <!-- 有过滤条件但无匹配结果 -->
      <el-empty 
        v-else-if="filteredData.length === 0 && objectFilter" 
        description="没有找到匹配的对象"
        class="empty-state"
      />
      
      <!-- 数据树 -->
      <el-tree
        v-else
        :data="filteredData" 
        :props="{
          label: 'label',
          children: 'children'
        }"
        node-key="id"
        :expand-on-click-node="false"
        @node-click="(data, node, e) => $emit('node-click', data, node, e)"
        @node-contextmenu="(event, data, node) => $emit('node-right-click', { node, event })"
        default-expand-all
        highlight-current
        ref="treeRef" 
      >
        <template #default="{ node, data }">
          <div class="tree-node">
            <el-icon v-if="data.type === 'table'"><Grid /></el-icon>
            <el-icon v-else-if="data.type === 'view'"><View /></el-icon>
            <el-icon v-else-if="data.type === 'schema'"><Collection /></el-icon>
            <el-icon v-else-if="data.type && data.type.includes('group')"><Folder /></el-icon>
            <el-icon v-else><Operation /></el-icon>
            <span>{{ node.label }}</span>
            <span v-if="data.type === 'table'" class="row-count">{{ data.rowCount }}</span>
          </div>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { Fold, Refresh, Search, Grid, View, Collection, Operation, Folder, Box, Connection } from '@element-plus/icons-vue';
import { ElMessage, ElIcon } from 'element-plus';

// 添加开发环境调试标志
const showDebugInfo = ref(process.env.NODE_ENV === 'development');

const props = defineProps({
  databaseObjects: { //接收未过滤的原始数据
    type: Array,
    default: () => []
  },
  treeLoading: {
    type: Boolean,
    default: false
  },
  isRefreshingDatasource: {
    type: Boolean,
    default: false
  },
  objectFilter: { // 由父组件通过 v-model:objectFilter 传入
    type: String,
    default: ''
  },
  activeTabDatasourceId: { 
    type: [String, Number],
    default: null
  },
  currentSchema: { // 添加新的prop来接收当前选择的schema
    type: String,
    default: ''
  }
});

const emit = defineEmits([
  'refresh-datasource', 
  'update:objectFilter', 
  'node-click', 
  'node-right-click'
]);

const treeRef = ref(null); // el-tree 的引用，如果需要手动调用其方法

// 添加监听器以打印接收到的数据变化
watch(() => props.databaseObjects, (newVal) => {
  console.log('[ObjectBrowser] 收到新的数据库对象数据:', newVal.length, '个对象');
  if (newVal.length === 0) {
    console.log('[ObjectBrowser] 警告: 收到空的数据库对象数组');
  } else {
    console.log('[ObjectBrowser] 第一个对象示例:', newVal[0]);
  }
}, { deep: true });

// 监听加载状态变化
watch(() => props.treeLoading, (newVal) => {
  console.log('[ObjectBrowser] 树加载状态变更为:', newVal ? '加载中' : '已完成');
});

// 监听激活的数据源ID变化
watch(() => props.activeTabDatasourceId, (newVal) => {
  console.log('[ObjectBrowser] 活动数据源ID变更为:', newVal);
});

onMounted(() => {
  console.log('[ObjectBrowser] 组件已挂载, 初始数据:',{
    databaseObjects: props.databaseObjects.length,
    treeLoading: props.treeLoading,
    activeTabDatasourceId: props.activeTabDatasourceId
  });
});

// 内部计算属性，根据 props.objectFilter 过滤 props.databaseObjects
const filteredData = computed(() => {
  console.log('[ObjectBrowser] 计算过滤数据, 原始数据量:', props.databaseObjects.length, '过滤条件:', props.objectFilter, '数据源ID:', props.activeTabDatasourceId);
  
  // 如果没有选择数据源，直接返回空数组
  if (!props.activeTabDatasourceId) {
    console.log('[ObjectBrowser] 未选择数据源，返回空数组');
    return [];
  }
  
  // 额外检查，确保在有数据源但无数据时也能正确处理
  if (!props.databaseObjects || props.databaseObjects.length === 0) {
    console.log('[ObjectBrowser] 虽然选择了数据源，但没有数据库对象数据，返回空数组');
    return [];
  }
  
  if (!props.objectFilter) {
    return props.databaseObjects;
  }
  const filterValue = props.objectFilter.toLowerCase();
  
  function filterNodes(nodes) {
    if (!nodes) return [];
    return nodes.reduce((acc, node) => {
      const isMatch = (node.label && node.label.toLowerCase().includes(filterValue)) ||
                      (node.value && node.value.toLowerCase().includes(filterValue)) ||
                      (node.name && node.name.toLowerCase().includes(filterValue));
      
      if (node.children && node.children.length > 0) {
        const filteredChildren = filterNodes(node.children);
        if (filteredChildren.length > 0) {
          // 如果子节点有匹配，则保留当前节点并附加过滤后的子节点
          acc.push({ ...node, children: filteredChildren });
        } else if (isMatch) {
          // 如果子节点无匹配，但当前节点匹配，则保留当前节点（不带子节点，除非它们也匹配）
           acc.push({ ...node, children: [] }); // 或者根据需求决定是否保留空的children
        }
      } else if (isMatch) {
        acc.push(node);
      }
      return acc;
    }, []);
  }
  const result = filterNodes(props.databaseObjects);
  console.log('[ObjectBrowser] 过滤后数据量:', result.length);
  return result;
});

// 添加新的状态变量
const isLoadingDelayed = ref(false);
const justSwitchedSchema = ref(false);
let loadingTimer = null;

// 计算空状态显示的文本
const emptyStateText = computed(() => {
  if (!props.activeTabDatasourceId) {
    return '请先选择数据源';
  } else if (props.currentSchema) {
    return `Schema '${props.currentSchema}' 中没有表或视图。`;
  }
  return '没有可用的表或视图。请选择数据库和Schema。';
});

// 刷新时添加短暂延迟
function refreshWithDelay() {
  console.log('[ObjectBrowser] 准备刷新数据源，将清除缓存, key:', `database_objects_${props.activeTabDatasourceId}`);
  // 强制清除前端缓存
  if (props.activeTabDatasourceId) {
    const cacheKey = `database_objects_${props.activeTabDatasourceId}`;
    localStorage.removeItem(cacheKey);
    const autocompleteCacheKey = `autocomplete_cache_${props.activeTabDatasourceId}`;
    localStorage.removeItem(autocompleteCacheKey);
    ElMessage.success('缓存已清除，正在刷新...');
  }

  isLoadingDelayed.value = true;
  setTimeout(() => {
    emit('refresh-datasource');
    setTimeout(() => {
      isLoadingDelayed.value = false;
    }, 1000);
  }, 300);
}

// 监听活动标签页数据源ID和当前schema的变化
watch(
  () => [props.activeTabDatasourceId, props.currentSchema],
  ([newDatasourceId, newSchema], [oldDatasourceId, oldSchema]) => {
    console.log(`[ObjectBrowser] 数据源或Schema变更: 数据源ID ${oldDatasourceId} -> ${newDatasourceId}, Schema ${oldSchema} -> ${newSchema}`);
    
    // 设置正在加载状态
    isLoadingDelayed.value = true;
    justSwitchedSchema.value = true;
    
    // 延迟一段时间后关闭加载状态，让数据有时间加载
    setTimeout(() => {
      isLoadingDelayed.value = false;
      
      // 如果切换后数据源/Schema后还是没有数据，可能需要主动刷新
      if (props.databaseObjects.length === 0) {
        console.log('[ObjectBrowser] 切换后无数据，自动刷新');
        emit('refresh-datasource');
      }
    }, 300); // 延迟1.5秒
    
    // 3秒后重置Schema切换状态
    setTimeout(() => {
      justSwitchedSchema.value = false;
    }, 300);
  }
);

// 组件销毁时清理定时器
onUnmounted(() => {
  clearTimeout(loadingTimer);
});

</script>

<style scoped>
.object-browser {
  width: 280px;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  background-color: #fcfcfc;
  height: 100%; 
  position: relative;
}

.browser-header {
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
  position: relative;
  z-index: 20;
}

.browser-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.browser-title h3 {
  margin: 0;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #303133;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-container .el-input {
  flex: 1;
}

.refresh-button {
  flex-shrink: 0;
  position: relative;
  z-index: 30;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.browser-body {
  flex-grow: 1;
  overflow-y: auto;
  padding: 8px;
  position: relative;
  z-index: 10;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
}

.tree-node .el-icon {
  font-size: 16px;
}

.row-count {
  font-size: 0.8em;
  color: #909399;
  margin-left: 8px;
}

/* 优化el-tree的加载和空状态显示 */
.browser-body .el-empty {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.browser-body .el-tree {
  height: 100%; /* 确保树在有数据时填充空间 */
}

/* 调试信息样式 */
.debug-info {
  background-color: #f8f8f8;
  border: 1px dashed #ccc;
  padding: 8px;
  margin-bottom: 10px;
  font-size: 12px;
  font-family: monospace;
}

.debug-info p {
  margin: 2px 0;
}

.loading-state {
  padding: 15px;
}

.loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
  color: #909399;
  font-size: 14px;
}

.loading-message .el-icon {
  margin-right: 5px;
}

.empty-state {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100% - 50px);
}
</style> 