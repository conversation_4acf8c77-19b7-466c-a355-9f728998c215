<template>
  <div class="query-tabs-container">
    <div class="query-tabs-toolbar">
      <el-button-group>
        <el-tooltip content="执行" placement="bottom">
          <el-button type="primary" @click="debugExecuteQuery" :icon="CaretRight" :loading="activeTab?.loading" />
        </el-tooltip>
        
        <el-tooltip content="格式化" placement="bottom">
          <el-button @click="debugFormatSql" :icon="Files" />
        </el-tooltip>
        
        <el-tooltip content="保存" placement="bottom">
          <el-button @click="debugSaveQuery" :icon="FolderAdd" />
        </el-tooltip>
        
        <el-tooltip content="已保存查询" placement="bottom">
          <el-button @click="handleShowSavedQueries" :icon="Document" />
        </el-tooltip>
        
        <el-tooltip content="刷新补全缓存" placement="bottom">
          <el-button @click="handleRefreshCache" :icon="Refresh" />
        </el-tooltip>
        
        <el-tooltip content="新建查询" placement="bottom">
          <el-button @click="addNewQueryTab" :icon="Plus" />
        </el-tooltip>
        
        <el-tooltip content="清除所有标签页" placement="bottom">
          <el-button type="danger" @click="confirmClearAllTabs" :icon="Delete" />
        </el-tooltip>
      </el-button-group>
    </div>

    <div class="tabs-header">
      <el-tabs
        v-model="activeTabId"
        type="card"
        closable
        @tab-remove="removeQueryTab"
        @tab-change="handleTabChange"
      >
        <el-tab-pane
          v-for="tab in queryTabs"
          :key="tab.id"
          :label="tab.name || '查询 ' + tab.id"
          :name="tab.id"
        >
          <template #label>
            <span>{{ tab.name || '查询 ' + tab.id }}</span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div class="tabs-content">
      <ActiveQueryPanel
        ref="activeQueryPanelRef"
        v-if="activeTab"
        :tab="activeTab"
        :databases="databases"
        :extensions="extensions"
        :is-admin="isAdmin"
        :user-permissions="userPermissions"
        @execute-query="handleExecuteQuery"
        @format-sql="handleFormatSql"
        @tab-database-change="handleTabDatabaseChange"
        @update:tab-schema="handleUpdateTabSchema"
        @tab-schema-change="handleTabSchemaChange"
        @editor-ready="handleEditorReady"
        @update:sql="(newSql) => updateTabSql(activeTabId, newSql)"
        @delete-row="(payload) => emit('delete-row', payload)"
        @save-query="handleSaveQueryRequest"
        @update-cell-value="handleUpdateCellValue"
        @request-edit-permission="handleRequestEditPermission"
        @update:tab="handleUpdateTab"
        @submit:changes="handleSubmitChanges"
        @execution-state-change="handleExecutionStateChange"
        @clear-result-tabs="handleClearResultTabs"
        @add-result-tab="handleAddResultTab"
        @cancel-query="handleCancelQuery"
      />
    </div>

    <!-- 保存查询对话框 -->
    <SaveQueryDialog
      v-model:visible="saveQueryDialogVisible"
      :query-data="currentQueryData"
      :datasource-name="currentDatasourceName"
      :schema-name="currentSchemaName"
      :is-saving="isSaving"
      @confirm="handleConfirmSave"
    />

    <!-- 已保存查询列表对话框 -->
    <SavedQueriesListDialog
      v-model:visible="savedQueriesDialogVisible"
      :databases="databases"
      :saved-queries-list="savedQueries"
      :is-loading-externally="isLoadingQueries"
      @import-query="handleImportSavedQuery"
      @delete-query="handleDeleteSavedQuery"
      @refresh-queries="handleRefreshQueries"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, onUnmounted, watch, defineEmits, defineExpose, defineProps, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Delete, Files, Refresh, CaretRight, FolderAdd, Document } from '@element-plus/icons-vue';
import ActiveQueryPanel from './ActiveQueryPanel.vue';
import SaveQueryDialog from './SaveQueryDialog.vue'
import SavedQueriesListDialog from './SavedQueriesListDialog.vue'
import { saveQuery, getSavedQueries, deleteSavedQuery } from '@/api/query'

const editorInstances = new Map();
const activeQueryPanelRef = ref(null);

const props = defineProps({
  databases: {
    type: Array,
    required: true
  },
  extensions: {
    type: Array,
    default: () => []
  },
  isAdmin: {
    type: Boolean,
    default: false
  },
  userInfo: { type: Object, default: () => ({}) },
});

const emit = defineEmits([
  'active-tab-changed',
  'open-save-query-dialog',
  'cell-dblclick',
  'delete-row',
  'execute-query',
  'refresh-autocomplete-cache',
  'tab-database-change',
  'update-cell-value',
  'request-edit-permission',
  'submit:changes',
  'cancel-query'
]);

const queryTabs = ref([]);
const activeTabId = ref('');
const tabIdCounter = ref(1);

const activeTab = computed(() => {
  return queryTabs.value.find(tab => tab.id === activeTabId.value) || null;
});

watch(activeTabId, (newId, oldId) => {
  console.log(`[QueryTabsManager] activeTabId changed from ${oldId} to ${newId}`);
  const newActiveTab = queryTabs.value.find(tab => tab.id === newId);
  if (newActiveTab) {
    // 发送一个深拷贝的纯对象，避免响应式问题
    emit('active-tab-changed', JSON.parse(JSON.stringify(newActiveTab)));
  } else {
    emit('active-tab-changed', null);
  }
});

// 此处移除了重复的 handleUpdateTab 函数定义
// 实际的 handleUpdateTab 函数定义在下方

function generateTabId() {
  return `tab-${tabIdCounter.value++}`;
}

function createDefaultTab() {
  const id = generateTabId();
  return {
    id,
    name: `查询 ${tabIdCounter.value - 1}`,
    sql: '',
    loading: false,
    executed: false,
    error: null,
    results: [],
    columns: [],
    activeResultTab: 'results',
    currentPage: 1,
    pageSize: 20,
    tableLoading: false,
    resultCount: 0,
    totalTime: 0,
    dbExecutionTime: 0,
    networkTime: 0,
    messages: '',
    datasourceId: null,
    schema: null,
    schemas: [],
    backupSql: null,
    currentQueryTableName: null,
    executionLogs: [], // 添加每个标签页独立的执行日志
    editModeEnabled: false, // 添加每个标签页独立的编辑模式状态
    resultTabs: [], // 添加结果标签页数组
  };
}

function addNewQueryTab() {
  const newTab = createDefaultTab();
  queryTabs.value.push(newTab);
  activeTabId.value = newTab.id;
}

function removeQueryTab(targetId) {
  console.log('[QueryTabsManager] 删除标签页:', targetId);
  if (queryTabs.value.length <= 1) {
    ElMessage.warning('至少需要保留一个查询标签页');
    return;
  }
  const index = queryTabs.value.findIndex(tab => tab.id === targetId);
  if (index !== -1) {
    queryTabs.value.splice(index, 1);
    if (activeTabId.value === targetId) {
      activeTabId.value = queryTabs.value[Math.max(0, Math.min(index, queryTabs.value.length - 1))].id;
    }
    // 保存到本地存储
    saveTabsToStorage();
  }
}

function getTabTitle(tab) {
  return tab.title;
}

function loadTabsFromStorage() {
  try {
    const savedTabsJson = localStorage.getItem('sql_query_tabs');
    const savedActiveTabId = localStorage.getItem('sql_query_active_tab');
    
    if (savedTabsJson) {
      const parsedTabs = JSON.parse(savedTabsJson);
      if (Array.isArray(parsedTabs) && parsedTabs.length > 0) {
        queryTabs.value = parsedTabs.map(savedTab => ({
          ...createDefaultTab(), 
          ...savedTab, 
          id: savedTab.id || generateTabId(), 
          loading: false,
          tableLoading: false,
          executed: false,
          error: null,
        }));

        let maxIdNum = 0;
        queryTabs.value.forEach(tab => {
          const idMatch = tab.id.match(/tab-(\d+)/);
          if (idMatch && idMatch[1]) {
            const idNum = parseInt(idMatch[1]);
            if (idNum > maxIdNum) {
              maxIdNum = idNum;
            }
          }
        });
        tabIdCounter.value = maxIdNum >= tabIdCounter.value ? maxIdNum + 1 : tabIdCounter.value;

        if (savedActiveTabId && queryTabs.value.some(tab => tab.id === savedActiveTabId)) {
          activeTabId.value = savedActiveTabId;
        } else if (queryTabs.value.length > 0) {
          activeTabId.value = queryTabs.value[0].id;
        }
        return;
      }
    }
  } catch (error) {
    console.error('从本地存储恢复标签页失败:', error);
    localStorage.removeItem('sql_query_tabs');
    localStorage.removeItem('sql_query_active_tab');
  }
  addNewQueryTab();
}

function saveTabsToStorage() {
  try {
    const tabsToSave = queryTabs.value.map(tab => ({
      id: tab.id,
      name: tab.name,
      sql: tab.sql,
      datasourceId: tab.datasourceId,
      schema: tab.schema,
      executionLogs: tab.executionLogs || [], // 保存执行日志
      editModeEnabled: tab.editModeEnabled || false, // 保存编辑模式状态
      // 显式排除 editor 实例和其他运行时状态
    }));
    localStorage.setItem('sql_query_tabs', JSON.stringify(tabsToSave));
    localStorage.setItem('sql_query_active_tab', activeTabId.value);
  } catch (error) {
    console.error('保存标签页到localStorage失败:', error);
  }
}

watch(
  () => ({
    active: activeTabId.value,
    tabs: queryTabs.value.map(t => ({ 
      id: t.id, 
      name: t.name, 
      sql: t.sql, 
      datasourceId: t.datasourceId, 
      schema: t.schema,
      editModeEnabled: t.editModeEnabled // 监听编辑模式状态变化
    }))
  }),
  saveTabsToStorage,
  { deep: true }
);

onMounted(() => {
  loadTabsFromStorage();
  
  // 注意：我们不再在这里添加查询结果事件监听器，因为ActiveQueryPanel中已经有了
  console.log('[QueryTabsManager] 不添加查询结果事件监听器，依赖ActiveQueryPanel中的事件处理');
});

// 处理查询结果事件
function handleQueryResult(event) {
  const result = event.detail;
  console.log('[QueryTabsManager] 收到查询结果:', result);
  
  if (!result || !result.tabId) {
    console.error('[QueryTabsManager] 收到无效的查询结果事件:', event);
    return;
  }
  
  // 更新标签页状态
  const tab = queryTabs.value.find(tab => tab.id === result.tabId);
  if (!tab) {
    console.error(`[QueryTabsManager] 未找到标签页: ${result.tabId}`);
    return;
  }
  
  // 更新标签页状态
  tab.loading = false;
  tab.executed = true;
  // 不在主标签页设置错误，错误信息会在对应的结果标签页中显示
  tab.error = null;

  if (result.error) {
    // 处理错误 - 不在主标签页显示错误，错误信息会在对应的结果标签页中显示
    tab.results = [];
    tab.columns = [];
    ElMessage.error(result.error);
  } else {
    // 处理成功结果
    tab.results = result.results || [];
    tab.columns = result.columns || [];
    
    // 如果是选中执行的SQL，则添加到结果标签页
    if (tab.selectedSqlExecution || result.selectedSql) {
      // 创建新的结果标签页
      if (!tab.resultTabs) {
        tab.resultTabs = [];
      }
      
      // 限制结果标签页数量
      if (tab.resultTabs.length >= 5) {
        tab.resultTabs.shift();
        console.log('[QueryTabsManager] 已达到结果标签页上限(5)，移除最早的标签页');
      }
      
      // 添加新结果标签页
      const newResultTab = {
        id: `result-tab-${Date.now()}`,
        results: result.results || [],
        columns: result.columns || [],
        timestamp: Date.now(),
        sql: tab.displaySql || result.sql || tab.sql // 优先使用displaySql
      };
      
      tab.resultTabs.push(newResultTab);
      console.log(`[QueryTabsManager] 已添加新的结果标签页，当前共 ${tab.resultTabs.length} 个标签页`);
    }
    
    // 如果有多结果集，处理它们
    if (result.is_multi_statement && result.all_results && Array.isArray(result.all_results)) {
      // 第一个结果已经作为主结果显示，所以我们从第二个开始
      for (let i = 1; i < result.all_results.length; i++) {
        if (tab.resultTabs.length >= 5) {
          // 已达到最大标签页数量
          break;
        }
        
        const additionalResult = result.all_results[i];
        
        // 创建额外的结果标签页
        const additionalResultTab = {
          id: `result-tab-${Date.now()}-${i}`,
          results: additionalResult.rows || [],
          columns: additionalResult.columns || [],
          timestamp: Date.now(),
          sql: additionalResult.sql || `Part ${i+1} of ${result.all_results.length}`
        };
        
        tab.resultTabs.push(additionalResultTab);
        console.log(`[QueryTabsManager] 已添加多结果集标签页 ${i+1}/${result.all_results.length}`);
      }
    }
  }
  
  // 更新标签页
  handleUpdateTab(tab);
}

function clearAllTabs() {
  queryTabs.value = [];
  tabIdCounter.value = 1;
  addNewQueryTab();
  localStorage.removeItem('queryTabs');
  emit('active-tab-changed', null);
}

function confirmClearAllTabs() {
  ElMessageBox.confirm('确定要清除所有查询标签页吗?', '确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    clearAllTabs();
    ElMessage.success('所有查询标签页已清除');
  }).catch(() => {});
}

function handleFormatSql() {
  if (!activeTab.value) {
    console.log('[handleFormatSql] 没有活动标签页，不执行格式化');
    ElMessage.warning('请先选择一个查询标签页');
    return;
  }
  
  console.log('[handleFormatSql] 准备格式化SQL，活动标签页:', activeTab.value.id);
  
  // 构建格式化参数
  const payload = {
    tabId: activeTab.value.id,
    sql: activeTab.value.sql
  };
  
  console.log('[handleFormatSql] 构建的格式化参数:', payload);
  
  // 调用格式化SQL的方法
  console.log('[handleFormatSql] 发送format-sql事件:', payload);
  emit('format-sql', payload);
}

function handleEditorReadyForTab(payload) {
  if (payload.tabId && payload.editor) {
    editorInstances.set(payload.tabId, payload.editor);
  }
}

function handleTabChange(newActiveTabName) {
  // Watcher on 'activeTab' will emit 'active-tab-changed'.
}

async function handleInternalTabDatabaseChange(payload) {
  console.log('[QueryTabsManager] handleInternalTabDatabaseChange received, payload:', payload);
  const tab = queryTabs.value.find(t => t.id === payload.tabId);
  if (tab) {
    if (tab.datasourceId !== payload.newDbId) {
      console.log(`[QueryTabsManager] Updating tab ${tab.id} datasource: ${tab.datasourceId} -> ${payload.newDbId}`);
      
      // 先清空 schema 相关数据
      tab.schema = null;
      tab.schemas = [];
      tab.datasourceId = payload.newDbId;
      
      // 如果是当前活动标签，立即通知父组件
      if (tab.id === activeTabId.value) {
        console.log('[QueryTabsManager] 数据源变更，立即通知父组件');
        try {
          // 等待下一个 tick，确保状态已更新
          await nextTick();
          // 发送事件通知父组件
          emit('active-tab-changed', { 
            ...tab,
            _forceSchemaRefresh: true // 添加标记，表示需要强制刷新 schema
          });
          
          // 等待一段时间后检查是否收到了新的 schemas
          setTimeout(() => {
            if (tab.schemas.length === 0) {
              console.log('[QueryTabsManager] Schema列表仍为空，再次请求刷新');
              emit('active-tab-changed', { 
                ...tab,
                _forceSchemaRefresh: true
              });
            }
          }, 1000); // 1秒后检查
        } catch (error) {
          console.error('[QueryTabsManager] 通知数据源变更失败:', error);
        }
      }
    } else {
      console.log(`[QueryTabsManager] Tab ${tab.id} datasource already ${payload.newDbId}, no change.`);
    }
  } else {
    console.warn(`[QueryTabsManager] Tab not found for ID: ${payload.tabId}`);
  }
}

function handleInternalTabSchemaChange(payload) {
  console.log('[QueryTabsManager] handleInternalTabSchemaChange received, payload:', payload);
  const tab = queryTabs.value.find(t => t.id === payload.tabId);
  if (tab) {
    if (tab.schema !== payload.newSchema) {
      console.log(`[QueryTabsManager] Updating tab ${tab.id} schema: ${tab.schema} -> ${payload.newSchema}`);
      tab.schema = payload.newSchema;
    } else {
      console.log(`[QueryTabsManager] Tab ${tab.id} schema already ${payload.newSchema}, no change.`);
    }
  } else {
    console.warn(`[QueryTabsManager] Tab not found for ID: ${payload.tabId} in handleInternalTabSchemaChange`);
  }
}

// 状态管理
const saveQueryDialogVisible = ref(false)
const savedQueriesDialogVisible = ref(false)
const currentQueryData = ref(null)
const savedQueries = ref([])
const isLoadingQueries = ref(false)
const isSaving = ref(false)
const resultTabs = ref([]) // 添加结果标签页跟踪

// 计算属性
const currentDatasourceName = computed(() => {
  const activeTab = queryTabs.value.find(tab => tab.id === activeTabId.value)
  if (activeTab?.datasourceId) {
    const db = props.databases.find(d => d.id === activeTab.datasourceId)
    return db?.name || '未知数据源'
  }
  return ''
})

const currentSchemaName = computed(() => {
  const activeTab = queryTabs.value.find(tab => tab.id === activeTabId.value)
  return activeTab?.schema || ''
})

// 添加用户权限计算属性
const userPermissions = computed(() => {
  // 如果是管理员，直接有所有权限
  if (props.isAdmin) {
    return {
      canEdit: true,
      canRequestEdit: false // 管理员不需要请求权限
    };
  }
  
  // 如果有用户信息，检查相关权限
  if (props.userInfo && props.userInfo.permissions) {
    return {
      canEdit: props.userInfo.permissions.includes('DATA_EDIT'),
      canRequestEdit: props.userInfo.permissions.includes('REQUEST_EDIT_PERMISSION')
    };
  }
  
  // 默认情况
  return {
    canEdit: false,
    canRequestEdit: true // 默认可以请求权限
  };
});

// 方法 - 处理从ActiveQueryPanel接收到的保存查询请求
const handleSaveQueryRequest = (queryData) => {
  if (!queryData) {
    ElMessage.warning('无效的查询数据')
    return
  }

  if (!queryData.datasourceId) {
    ElMessage.warning('请先选择数据源')
    return
  }

  if (!queryData.sql?.trim()) {
    ElMessage.warning('请先输入SQL查询语句')
    return
  }

  console.log('保存查询数据:', queryData)
  currentQueryData.value = {
    sql: queryData.sql,
    datasourceId: queryData.datasourceId,
    schema: queryData.schema
  }
  saveQueryDialogVisible.value = true
}

// 清除当前标签页的所有结果标签页
function clearResultTabs() {
  console.log('[QueryTabsManager] 清除所有结果标签页');
  if (activeTab.value) {
    // 重置结果相关属性
    activeTab.value.results = [];
    activeTab.value.columns = [];
    activeTab.value.executed = false;
    activeTab.value.error = null;
    // 清空结果标签页数组
    resultTabs.value = [];
    activeTab.value.resultTabs = [];
    
    // 更新标签页
    handleUpdateTab(activeTab.value);
  }
}

// 添加结果标签页
function addResultTab(resultData) {
  console.log('[QueryTabsManager] 添加结果标签页:', resultData);
  if (activeTab.value) {
    // 确保resultTabs已初始化
    if (!resultTabs.value) {
      resultTabs.value = [];
    }
    
    // 确保activeTab.resultTabs已初始化
    if (!activeTab.value.resultTabs) {
      activeTab.value.resultTabs = [];
    }
    
    // 检查结果标签页数量是否已达到上限
    if (activeTab.value.resultTabs.length >= 5) {
      // 移除最早的结果标签页
      activeTab.value.resultTabs.shift();
      console.log('[QueryTabsManager] 已达到结果标签页上限，移除最早的标签页');
    }
    
    // 创建新的结果标签页
    const newResultTab = {
      id: `result-tab-${Date.now()}`,
      results: resultData.results || [],
      columns: resultData.columns || [],
      timestamp: Date.now(),
      sql: resultData.sql || activeTab.value.sql
    };
    
    // 添加到结果标签页数组
    activeTab.value.resultTabs.push(newResultTab);
    resultTabs.value = [...activeTab.value.resultTabs];
    
    // 更新当前标签页的结果
    activeTab.value.results = resultData.results || [];
    activeTab.value.columns = resultData.columns || [];
    activeTab.value.executed = true;
    activeTab.value.error = null;
    
    // 更新标签页
    handleUpdateTab(activeTab.value);
  }
}

// 处理来自ActiveQueryPanel的清除结果标签页事件
function handleClearResultTabs() {
  clearResultTabs();
}

// 处理来自ActiveQueryPanel的添加结果标签页事件
function handleAddResultTab(resultData) {
  addResultTab(resultData);
}

const handleConfirmSave = async (saveData) => {
  isSaving.value = true
  try {
    console.log('保存查询请求数据:', {
      ...saveData,
      sql_content: currentQueryData.value.sql,
      datasource_id: currentQueryData.value.datasourceId,
      schema: currentQueryData.value.schema
    })
    
    const response = await saveQuery({
      ...saveData,
      sql_content: currentQueryData.value.sql,
      datasource_id: currentQueryData.value.datasourceId,
      schema: currentQueryData.value.schema
    })
    
    console.log('保存查询响应:', response)
    
    if (response && (response.success || response.id)) {
      ElMessage.success('查询保存成功')
      saveQueryDialogVisible.value = false
      await handleRefreshQueries()
    } else {
      throw new Error(response?.message || '保存查询失败，服务器返回未知错误')
    }
  } catch (error) {
    console.error('保存查询失败:', error)
    ElMessage.error('保存查询失败：' + (error.message || '未知错误'))
  } finally {
    isSaving.value = false
  }
}

const handleShowSavedQueries = async () => {
  savedQueriesDialogVisible.value = true
  await handleRefreshQueries()
}

const handleRefreshQueries = async () => {
  isLoadingQueries.value = true
  try {
    const response = await getSavedQueries()
    console.log('获取已保存查询列表响应:', response)
    // 检查响应格式，确保正确处理
    if (Array.isArray(response)) {
      // 如果直接返回数组，直接使用
      savedQueries.value = response
    } else if (response.data) {
      // 如果返回对象中包含data字段，使用data字段
      savedQueries.value = response.data
    } else {
      // 如果都不是，可能是空数据或其他格式
      console.warn('获取已保存查询列表返回格式异常:', response)
      savedQueries.value = []
    }
  } catch (error) {
    console.error('获取已保存查询列表失败:', error)
    ElMessage.error('获取已保存查询列表失败：' + (error.message || '未知错误'))
    savedQueries.value = [] // 确保在出错时设置为空数组
  } finally {
    isLoadingQueries.value = false
  }
}

const handleImportSavedQuery = (query) => {
  console.log('导入已保存查询:', query)
  
  // 检查查询对象是否有效
  if (!query || !query.sql_content) {
    ElMessage.warning('无效的查询数据')
    return
  }
  
  try {
    // 创建新标签页
    const newTab = createNewTab({
      sql: query.sql_content,
      datasourceId: query.datasource_id || (query.datasource && query.datasource.id),
      schema: query.schema,
      name: query.name
    })
    
    // 设置为活动标签
    activeTabId.value = newTab.id
    
    // 关闭对话框
    savedQueriesDialogVisible.value = false
    
    ElMessage.success(`已导入查询: ${query.name}`)
  } catch (error) {
    console.error('导入查询失败:', error)
    ElMessage.error('导入查询失败: ' + (error.message || '未知错误'))
  }
}

const handleDeleteSavedQuery = async (query) => {
  try {
    await deleteSavedQuery(query.id)
    ElMessage.success('删除成功')
    await handleRefreshQueries()
  } catch (error) {
    console.error('删除已保存查询失败:', error)
    ElMessage.error('删除失败：' + (error.message || '未知错误'))
  }
}

// 添加更新标签页查询状态的方法
function updateTabQueryState(tabId, state) {
  console.log('[updateTabQueryState] 被调用，参数:', { tabId, state });
  const tab = queryTabs.value.find(t => t.id === tabId);
  if (tab) {
    console.log('[updateTabQueryState] 找到标签页，当前状态:', {
      executed: tab.executed,
      hasMultiDmlResults: !!tab.multiDmlResults,
      resultTabsLength: tab.resultTabs?.length || 0
    });

    // 如果设置了 multiDmlResults 且不为 null，清除 resultTabs
    if (state.multiDmlResults !== undefined && state.multiDmlResults !== null) {
      console.log('[updateTabQueryState] 设置 multiDmlResults，清除 resultTabs');
      console.log('[updateTabQueryState] multiDmlResults 内容:', state.multiDmlResults);
      tab.resultTabs = [];
    }

    Object.assign(tab, state);

    console.log('[updateTabQueryState] 更新后的标签页状态:', {
      tabId,
      executed: tab.executed,
      hasMultiDmlResults: !!tab.multiDmlResults,
      multiDmlResultsLength: tab.multiDmlResults?.length || 0,
      resultTabsLength: tab.resultTabs?.length || 0,
      messages: tab.messages
    });
  } else {
    console.error('[updateTabQueryState] 未找到标签页:', tabId);
  }
}

function isRiskyOperation(sql) {
  if (!sql) return false;
  const lowerSql = sql.trim().toLowerCase();
  return lowerSql.startsWith('delete') || lowerSql.startsWith('update') || lowerSql.startsWith('alter table');
}

async function handleExecuteQuery(payload) {
  if (!activeTab.value) {
    ElMessage.warning('请先选择一个查询标签页');
    return;
  }
  
  console.log('[handleExecuteQuery] 准备执行查询，活动标签页:', activeTab.value.id);
  
  // 获取编辑器实例
  const editor = editorInstances.get(activeTab.value.id);
  let sqlToExecute = activeTab.value.sql;
  let isSelectedSql = false;
  
  // 检查是否有选中的文本
  if (editor && editor.getSelection) {
    const selectedText = editor.getSelection();
    if (selectedText && selectedText.trim()) {
      console.log('[handleExecuteQuery] 检测到选中的SQL:', selectedText);

      // 检查是否是全选（选中的文本等于完整的SQL）
      const fullSql = activeTab.value.sql.trim();
      const isFullSelection = selectedText.trim() === fullSql;

      if (isFullSelection) {
        console.log('[handleExecuteQuery] 检测到全选执行，按直接执行处理');
        // 全选执行按直接执行处理，不标记为选中执行
        sqlToExecute = fullSql;
        isSelectedSql = false;
      } else {
        console.log('[handleExecuteQuery] 检测到部分选中执行');
        sqlToExecute = selectedText.trim();
        isSelectedSql = true;
      }
    }
  }
  
  // 检查高危操作
  if (isRiskyOperation(sqlToExecute) && activeQueryPanelRef.value && activeQueryPanelRef.value.checkAndBackupBeforeExecute) {
    await activeQueryPanelRef.value.checkAndBackupBeforeExecute(sqlToExecute);
    return;
  }
  
  // 构建查询参数
  const queryPayload = {
    tabId: activeTab.value.id,
    sql: sqlToExecute, // 使用选中的SQL或完整SQL
    datasourceId: activeTab.value.datasourceId,
    schema: activeTab.value.schema,
    page: activeTab.value.currentPage || 1,
    pageSize: activeTab.value.pageSize || 20,
    selectedSql: isSelectedSql // 标记是否为选中执行
  };
  
  console.log('[handleExecuteQuery] 构建的查询参数:', queryPayload);
  
  // 更新标签页状态为加载中
  activeTab.value.loading = true;
  activeTab.value.error = null;
  
  // 如果是选中执行，标记当前标签页
  if (isSelectedSql) {
    activeTab.value.selectedSqlExecution = true;
    activeTab.value.displaySql = sqlToExecute; // 添加此行，保存选中的SQL
    console.log('[handleExecuteQuery] 标记为选中SQL执行');
  } else {
    activeTab.value.selectedSqlExecution = false;
    activeTab.value.displaySql = null; // 清空displaySql字段
  }
  
  // 将查询请求传递给父组件
  console.log('[handleExecuteQuery] 发送execute-query事件:', queryPayload);
  emit('execute-query', queryPayload);
}

// 添加保存查询的方法
function handleSaveQuery() {
  if (!activeTab.value) {
    ElMessage.warning('请先选择一个查询标签页');
    return;
  }
  
  // 检查SQL是否为空
  if (!activeTab.value.sql || !activeTab.value.sql.trim()) {
    ElMessage.warning('SQL内容不能为空');
    return;
  }
  
  // 设置当前查询数据
  currentQueryData.value = {
    sql: activeTab.value.sql,
    datasourceId: activeTab.value.datasourceId,
    schema: activeTab.value.schema
  };
  
  // 显示保存对话框
  saveQueryDialogVisible.value = true;
}

// 暴露方法给父组件
defineExpose({
  queryTabs,
  activeTab,
  activeTabId,
  addNewQueryTab,
  updateTabQueryState,
  updateTabSql,
  handleFormatSql,
  refreshTabResults
});

// 添加缺失的方法
function handleTabDatabaseChange(payload) {
  console.log('[QueryTabsManager] handleTabDatabaseChange received:', payload);
  
  // 更新当前标签页的数据源和schema信息
  const tab = queryTabs.value.find(t => t.id === payload.tabId);
  if (tab) {
    // 检查是否有数据源变更
    if (payload.datasourceId && tab.datasourceId !== payload.datasourceId) {
      console.log(`[QueryTabsManager] 更新标签页 ${tab.id} 的数据源: ${tab.datasourceId} -> ${payload.datasourceId}`);
      tab.datasourceId = payload.datasourceId;
    }
    
    // 检查是否有schema变更
    if (payload.schema && tab.schema !== payload.schema) {
      console.log(`[QueryTabsManager] 更新标签页 ${tab.id} 的schema: ${tab.schema} -> ${payload.schema}`);
      tab.schema = payload.schema;
    }
    
    // 确保我们在处理活动标签
    if (tab.id === activeTabId.value) {
      // 转发事件到父组件
      console.log('[QueryTabsManager] 转发tab-database-change事件到父组件:', payload);
      emit('tab-database-change', payload);
      
      // 保存到本地存储
      saveTabsToStorage();
    }
  } else {
    console.warn(`[QueryTabsManager] 未找到标签页 ID: ${payload.tabId}`);
  }
}

function handleTabSchemaChange(payload) {
  handleInternalTabSchemaChange({
    tabId: payload.tabId,
    newSchema: payload.schema
  });
}

function handleEditorReady(payload) {
  handleEditorReadyForTab(payload);
}

function updateTabSql(tabId, newSql) {
  const tab = queryTabs.value.find(t => t.id === tabId);
  if (tab) {
    tab.sql = newSql;
  }
}

// 创建新标签页的辅助方法
function createNewTab(options = {}) {
  const newTab = {
    ...createDefaultTab(),
    sql: options.sql || '',
    datasourceId: options.datasourceId || null,
    schema: options.schema || null,
    name: options.name || ''
  };
  queryTabs.value.push(newTab);
  return newTab;
}

function handleUpdateTabSchema(payload) {
  const tab = queryTabs.value.find(t => t.id === payload.tabId);
  if (tab) {
    tab.schema = payload.schema;
  }
}

function handleRefreshCache() {
  emit('refresh-autocomplete-cache');
}

// 定义一个空的 closeContextMenu 函数，因为在 onUnmounted 中引用了它，但没有定义
function closeContextMenu() {
  // 这个函数实际上在 index.vue 中定义，这里只是一个空实现，防止报错
  console.log('[QueryTabsManager] closeContextMenu called (dummy implementation)');
}

onUnmounted(() => {
  console.log('[QueryTabsManager] Unmounting. Ensuring all resources are properly cleaned up.');
  
  try {
    // 0. 我们不再需要移除事件监听器，因为我们没有添加
    console.log('[QueryTabsManager] 不需要移除查询结果事件监听器，因为我们没有添加');
    
    // 1. 清除编辑器实例映射
    if (editorInstances) {
      editorInstances.clear();
      console.log('[QueryTabsManager] 已清除编辑器实例映射');
    }
    
    // 2. 只移除与本组件相关的事件监听器
    const safeRemoveEventListener = (element, event, handler) => {
      try {
        if (element && typeof element.removeEventListener === 'function' && typeof handler === 'function') {
          element.removeEventListener(event, handler);
          console.log(`[QueryTabsManager] 已安全移除 ${event} 事件监听器`);
        }
      } catch (e) {
        console.warn(`[QueryTabsManager] 移除 ${event} 事件监听器失败:`, e);
      }
    };
    
    // 只有当closeContextMenu存在且是函数时才移除特定事件监听器
    if (typeof closeContextMenu === 'function') {
      safeRemoveEventListener(document, 'click', closeContextMenu);
    } else {
      console.warn('[QueryTabsManager] closeContextMenu 不是函数，跳过移除事件监听器');
    }
    
    // 3. 清除本组件内的引用
    if (queryTabs && queryTabs.value) {
      // 使用浅拷贝防止修改到响应式数据
      const tabsCopy = [...queryTabs.value];
      for (const tab of tabsCopy) {
        // 清除可能导致循环引用的字段
        if (tab) {
          tab.editor = null;
          // 不清除结果数据，防止影响其他组件
          // tab.results = [];
          // tab.columns = [];
        }
      }
      console.log('[QueryTabsManager] 已清除查询标签页上的循环引用');
    }
    
    // 4. 通知父组件组件已卸载，但不传递空值
    try {
      if (typeof emit === 'function') {
        // 不再发送空值，防止影响父组件状态
        // emit('active-tab-changed', null);
        console.log('[QueryTabsManager] 组件卸载完成');
      }
    } catch (emitError) {
      console.warn('[QueryTabsManager] 通知父组件失败:', emitError);
    }
    
    console.log('[QueryTabsManager] Cleanup completed successfully');
  } catch (error) {
    console.error('[QueryTabsManager] Error during cleanup:', error);
    
    // 最后的应急清理 - 只清理本组件资源
    try {
      console.log('[QueryTabsManager] 执行应急清理');
      editorInstances?.clear();
    } catch (e) {
      console.error('[QueryTabsManager] 应急清理也失败:', e);
    }
  }
});

// 处理单元格值更新
function handleUpdateCellValue(payload) {
  console.log('[QueryTabsManager] 处理单元格值更新:', payload);
  
  // 检查用户是否有权限进行编辑操作
  if (!props.isAdmin && !(props.userInfo?.permissions?.includes('DATA_EDIT'))) {
    ElMessage.warning('很抱歉，您暂时没有数据编辑权限，请联系系统管理员申请');
    return;
  }
  
  // 将事件向上传递到父组件进行实际处理
  emit('update-cell-value', {
    ...payload,
    tabId: activeTabId.value
  });
}

// 处理请求编辑权限
function handleRequestEditPermission(payload) {
  console.log('[QueryTabsManager] 处理请求编辑权限:', payload);
  
  // 将请求转发给父组件
  emit('request-edit-permission', {
    ...payload,
    userId: props.userInfo?.id,
    userName: props.userInfo?.name
  });
  
  ElMessage.info('您的编辑权限申请已提交，请等待管理员审核');
}

// 处理更新Tab对象
function handleUpdateTab(updatedTab) {
  console.log('[QueryTabsManager] 处理更新Tab对象:', updatedTab);
  
  if (!updatedTab || !updatedTab.id) return;
  
  // 查找并更新对应的tab
  const tabIndex = queryTabs.value.findIndex(tab => tab.id === updatedTab.id);
  if (tabIndex !== -1) {
    // 保留现有的属性，只更新传入的属性
    queryTabs.value[tabIndex] = {
      ...queryTabs.value[tabIndex],
      ...updatedTab
    };
    
    // 如果更新的是当前活动tab，额外通知父组件
    if (activeTabId.value === updatedTab.id) {
      emit('active-tab-changed', JSON.parse(JSON.stringify(queryTabs.value[tabIndex])));
    }
    
    // 保存到本地存储
    saveTabsToStorage();
  }
}

// 处理查询执行状态变化
function handleExecutionStateChange(isExecuting) {
  console.log('[QueryTabsManager] 查询执行状态变化:', isExecuting);
  
  if (!activeTab.value) return;
  
  // 更新当前活动标签页的执行状态
  const tabIndex = queryTabs.value.findIndex(tab => tab.id === activeTab.value.id);
  if (tabIndex !== -1) {
    queryTabs.value[tabIndex] = {
      ...queryTabs.value[tabIndex],
      isExecuting: isExecuting
    };
    
    // 通知父组件
    emit('active-tab-changed', JSON.parse(JSON.stringify(queryTabs.value[tabIndex])));
  }
}

// 刷新指定标签页的查询结果
async function refreshTabResults(tabId) {
  console.log('[QueryTabsManager] 刷新标签页结果:', tabId);
  
  // 查找对应的tab
  const tab = queryTabs.value.find(tab => tab.id === tabId);
  if (!tab) {
    console.warn(`[QueryTabsManager] 未找到标签页: ${tabId}`);
    return;
  }
  
  // 检查是否有SQL和数据源
  if (!tab.sql || !tab.datasourceId) {
    console.warn(`[QueryTabsManager] 标签页 ${tabId} 缺少SQL或数据源ID`);
    return;
  }
  
  // 重新执行查询
  await handleExecuteQuery({
    tabId: tab.id,
    sql: tab.sql,
    datasourceId: tab.datasourceId,
    schema: tab.schema,
    page: tab.currentPage || 1,
    pageSize: tab.pageSize || 20
  });
  
  console.log(`[QueryTabsManager] 已刷新标签页 ${tabId} 的结果`);
}

async function handleSubmitChanges(payload) {
  console.log('[QueryTabsManager] 收到批量提交更改请求:', payload);
  
  // 处理来自ActiveQueryPanel的单个更改数组情况
  if (Array.isArray(payload)) {
    console.log('[QueryTabsManager] 收到更改数组，转换为标准格式');
    
    if (payload.length === 0) {
      ElMessage.warning('没有需要提交的更改');
      return;
    }
    
    // 从第一个更改中提取元数据
    const firstChange = payload[0];
    
    // 构建标准payload
    payload = {
      changes: payload,
      tableName: firstChange.tableName,
      datasourceId: firstChange.datasourceId || activeTab.value?.datasourceId,
      database: firstChange.schema || activeTab.value?.schema,
      schema: firstChange.schema || activeTab.value?.schema
    };
    
    console.log('[QueryTabsManager] 转换后的标准格式:', payload);
  }
  
  // 检查数据结构
  if (!payload.changes || !Array.isArray(payload.changes) || !payload.changes.length) {
    console.error('[QueryTabsManager] 无效的变更数据结构:', payload);
    ElMessage.error('无效的变更数据结构');
    return;
  }
  
  // 提取必要的参数
  const tableName = payload.tableName;
  const datasourceId = payload.datasourceId || activeTab.value?.datasourceId;
  const schema = payload.database || payload.schema || activeTab.value?.schema;
  
  // 检查必要参数
  if (!tableName) {
    console.error('[QueryTabsManager] 缺少表名');
    ElMessage.error('缺少表名，无法提交更改');
    return;
  }
  
  if (!datasourceId) {
    console.error('[QueryTabsManager] 缺少数据源ID');
    ElMessage.error('缺少数据源ID，无法提交更改');
    return;
  }
  
  if (!schema) {
    console.error('[QueryTabsManager] 缺少schema/database信息');
    ElMessage.error('缺少schema/database信息，无法提交更改');
    return;
  }
  
  console.log('[QueryTabsManager] 提交参数校验通过:', {
    tableName,
    datasourceId,
    schema
  });
  
  try {
    // 发送批量提交事件到父组件
    const eventData = {
      datasourceId: datasourceId,
      database: schema, // 确保使用database字段
      schema: schema,
      tableName: tableName,
      changes: payload.changes, // 确保直接传递changes数组
      tabId: activeTab.value?.id
    };
    
    console.log('[QueryTabsManager] 向父组件发送的数据:', eventData);
    
    emit('submit:changes', eventData);
    console.log('[QueryTabsManager] emit事件已触发');
    
    // 提示用户已成功提交，但需要手动刷新查看最新数据
    ElMessage.success('数据修改已提交，刷新查看最新数据');
    
    // 不再自动刷新结果
    console.log('[QueryTabsManager] 提交变更后不自动刷新');
  } catch (error) {
    console.error('[QueryTabsManager] 批量提交事件触发失败:', error);
    ElMessage.error('批量提交失败: ' + (error.message || '未知错误'));
  }
}

async function handleDeleteRow(payload) {
  const { tabId, rowData } = payload;
  const tab = queryTabs.value.find(t => t.id === tabId);
  if (!tab) {
    ElMessage.error('找不到对应的标签页');
    return;
  }

  try {
    // 发送删除请求到后端
    await emit('delete:row', {
      tabId,
      datasourceId: tab.datasourceId,
      schema: tab.schema,
      tableName: tab.tableName,
      rowData
    });
    
    ElMessage.success('删除行成功');
  } catch (error) {
    console.error('删除行失败:', error);
    ElMessage.error('删除行失败: ' + error.message);
    throw error;
  }
}

async function handleAddRow(payload) {
  const { tabId, rowData } = payload;
  const tab = queryTabs.value.find(t => t.id === tabId);
  if (!tab) {
    ElMessage.error('找不到对应的标签页');
    return;
  }

  try {
    // 发送添加请求到后端
    await emit('add:row', {
      tabId,
      datasourceId: tab.datasourceId,
      schema: tab.schema,
      tableName: tab.tableName,
      rowData
    });
    
    ElMessage.success('添加行成功');
  } catch (error) {
    console.error('添加行失败:', error);
    ElMessage.error('添加行失败: ' + error.message);
    throw error;
  }
}

// 处理取消查询
function handleCancelQuery(payload) {
  console.log('[QueryTabsManager] 处理取消查询:', payload);
  emit('cancel-query', payload);
}

// 添加调试函数
function debugExecuteQuery() {
  console.log('[DEBUG] 点击执行按钮');
  console.log('[DEBUG] activeTab:', activeTab.value);
  if (activeTab.value) {
    console.log('[DEBUG] activeTab.id:', activeTab.value.id);
    console.log('[DEBUG] activeTab.sql:', activeTab.value.sql?.substring(0, 50));
    console.log('[DEBUG] activeTab.datasourceId:', activeTab.value.datasourceId);
    console.log('[DEBUG] activeTab.schema:', activeTab.value.schema);
    console.log('[DEBUG] 当前结果标签页数量:', activeTab.value.resultTabs?.length || 0);
    
    // 获取编辑器实例
    const editor = editorInstances.get(activeTab.value.id);
    console.log('[DEBUG] 编辑器实例:', editor ? '存在' : '不存在');
    console.log('[DEBUG] editorInstances键列表:', Array.from(editorInstances.keys()));
    
    if (editor) {
      console.log('[DEBUG] 编辑器getSelection方法:', editor.getSelection ? '存在' : '不存在');
      if (editor.getSelection) {
        try {
          const selectedText = editor.getSelection();
          console.log('[DEBUG] 获取选中内容:', selectedText ? `成功: "${selectedText.substring(0, 50)}"` : '为空');
          if (selectedText && selectedText.trim()) {
            console.log('[DEBUG] 选中的SQL:', selectedText.trim().substring(0, 50));
          } else {
            console.log('[DEBUG] 没有选中任何文本或选中的文本为空');
          }
        } catch (error) {
          console.error('[DEBUG] 获取选中文本时出错:', error);
        }
      } else {
        console.log('[DEBUG] 编辑器没有getSelection方法，可能是因为编辑器实例未正确初始化');
      }
    } else {
      console.log('[DEBUG] 找不到当前标签页的编辑器实例，ID:', activeTab.value.id);
    }
  }
  handleExecuteQuery();
}

function debugFormatSql() {
  console.log('[DEBUG] 点击格式化按钮');
  console.log('[DEBUG] activeTab:', activeTab.value);
  if (activeTab.value) {
    console.log('[DEBUG] activeTab.id:', activeTab.value.id);
    console.log('[DEBUG] activeTab.sql:', activeTab.value.sql?.substring(0, 50));
  }
  handleFormatSql();
}

function debugSaveQuery() {
  console.log('[DEBUG] 点击保存按钮');
  console.log('[DEBUG] activeTab:', activeTab.value);
  if (activeTab.value) {
    console.log('[DEBUG] activeTab.id:', activeTab.value.id);
    console.log('[DEBUG] activeTab.sql:', activeTab.value.sql?.substring(0, 50));
    console.log('[DEBUG] activeTab.datasourceId:', activeTab.value.datasourceId);
    console.log('[DEBUG] activeTab.schema:', activeTab.value.schema);
  }
  handleSaveQuery();
}

</script>

<style scoped>
.query-tabs-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.query-tabs-toolbar {
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.el-button-group {
  margin-right: 8px;
  display: flex;
  flex-wrap: wrap;
}

.el-button-group .el-button {
  margin-right: 4px;
  margin-bottom: 4px;
  padding: 8px 12px;
}

.tabs-header {
  flex-shrink: 0;
}

.tabs-content {
  flex-grow: 1;
  overflow: hidden;
  position: relative;
}

/* 让tooltip在按钮上方正确显示 */
.el-tooltip {
  display: inline-flex;
}

/* 让按钮更紧凑 */
.el-button + .el-button {
  margin-left: 2px;
}

/* 让按钮组更明显 */
.query-tabs-toolbar .el-button-group {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  margin-right: 12px;
}
</style> 