<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`编辑表结构: ${schemaName ? schemaName + '.' : ''}${tableName}`"
    width="clamp(800px, 80vw, 1200px)"
    class="edit-table-structure-dialog"
    @closed="onDialogClosed"
    @open="onDialogOpen"
    destroy-on-close
    append-to-body
  >
    <div v-if="loading" v-loading="loading" style="height: 300px;"></div>
    <div v-else>
      <el-table :data="localStructure" style="width: 100%" border max-height="60vh">
        <el-table-column label="字段名">
          <template #default="scope">
            <el-input v-model="scope.row.Field" placeholder="字段名"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="数据类型">
          <template #default="scope">
            <el-select
              v-model="scope.row.Type"
              filterable
              allow-create
              default-first-option
              placeholder="选择或输入数据类型"
              style="width: 100%;"
            >
              <el-option-group
                v-for="group in commonDataTypes"
                :key="group.label"
                :label="group.label"
              >
                <el-option
                  v-for="item in group.options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-option-group>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="可为空" width="80" align="center">
          <template #default="scope">
            <el-checkbox v-model="scope.row.Null" true-label="YES" false-label="NO"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column label="默认值">
          <template #default="scope">
            <el-input v-model="scope.row.Default" placeholder="NULL 或具体值"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="额外信息">
          <template #default="scope">
            <el-input v-model="scope.row.Extra" placeholder="例如: ON UPDATE CURRENT_TIMESTAMP"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template #default="scope">
            <el-button type="danger" :icon="Delete" circle @click="removeColumn(scope.$index)" size="small"></el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="table-actions" style="margin-top: 15px; display: flex; gap: 10px;">
        <el-button @click="addColumn" type="primary" :icon="Plus" size="default">
          添加字段
        </el-button>
        <el-button @click="resetTable" type="info" size="default">
          重置
        </el-button>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave" :disabled="loading">
          应用更改
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { ElDialog, ElTable, ElTableColumn, ElInput, ElCheckbox, ElButton, ElMessage, ElSelect, ElOption, ElOptionGroup, ElMessageBox } from 'element-plus';
import { Delete, Plus } from '@element-plus/icons-vue';

const props = defineProps({
  show: { type: Boolean, default: false },
  tableName: { type: String, default: '' },
  schemaName: { type: String, default: '' },
  structure: { type: Array, default: () => [] },
  loading: { type: Boolean, default: false },
});

const emit = defineEmits(['update:show', 'apply-changes']);

const dialogVisible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

const commonDataTypes = [
  {
    label: '常用类型',
    options: [
      { value: 'INT', label: 'INT' },
      { value: 'VARCHAR(255)', label: 'VARCHAR(255)' },
      { value: 'TEXT', label: 'TEXT' },
      { value: 'DATETIME', label: 'DATETIME' },
      { value: 'TIMESTAMP', label: 'TIMESTAMP' },
      { value: 'DECIMAL(10, 2)', label: 'DECIMAL(10, 2)' },
      { value: 'JSON', label: 'JSON' },
    ],
  },
  {
    label: '数字类型',
    options: [
      { value: 'TINYINT', label: 'TINYINT' },
      { value: 'SMALLINT', label: 'SMALLINT' },
      { value: 'MEDIUMINT', label: 'MEDIUMINT' },
      { value: 'BIGINT', label: 'BIGINT' },
      { value: 'FLOAT', label: 'FLOAT' },
      { value: 'DOUBLE', label: 'DOUBLE' },
      { value: 'DECIMAL', label: 'DECIMAL' },
    ],
  },
  {
    label: '字符串类型',
    options: [
      { value: 'CHAR(255)', label: 'CHAR' },
      { value: 'VARCHAR(255)', label: 'VARCHAR' },
      { value: 'TINYTEXT', label: 'TINYTEXT' },
      { value: 'TEXT', label: 'TEXT' },
      { value: 'MEDIUMTEXT', label: 'MEDIUMTEXT' },
      { value: 'LONGTEXT', label: 'LONGTEXT' },
    ],
  },
    {
    label: '日期与时间',
    options: [
      { value: 'DATE', label: 'DATE' },
      { value: 'TIME', label: 'TIME' },
      { value: 'DATETIME', label: 'DATETIME' },
      { value: 'TIMESTAMP', label: 'TIMESTAMP' },
      { value: 'YEAR', label: 'YEAR' },
    ],
  },
  {
    label: '二进制与其他',
    options: [
        { value: 'BLOB', label: 'BLOB' },
        { value: 'LONGBLOB', label: 'LONGBLOB' },
        { value: 'JSON', label: 'JSON' },
        { value: 'ENUM()', label: 'ENUM' },
        { value: 'SET()', label: 'SET' },
    ]
  }
];

// Use a local copy for editing
const localStructure = ref([]);
const originalStructure = ref([]);

watch(() => props.show, (isVisible) => {
  if (isVisible && !props.loading) {
    // Deep copy for editing to avoid mutating props directly
    console.log('表结构数据:', props.structure);
    localStructure.value = normalizeStructure(JSON.parse(JSON.stringify(props.structure)));
    originalStructure.value = normalizeStructure(JSON.parse(JSON.stringify(props.structure)));
    console.log('标准化后的表结构:', localStructure.value);
  }
}, { immediate: true });

watch(() => props.structure, (newStructure) => {
    if (props.show && !props.loading) {
        localStructure.value = normalizeStructure(JSON.parse(JSON.stringify(newStructure)));
        originalStructure.value = normalizeStructure(JSON.parse(JSON.stringify(newStructure)));
    }
});

// 标准化不同API返回的表结构格式
function normalizeStructure(structure) {
  console.log('正在标准化表结构:', structure);
  return structure.map(col => {
    // 创建一个标准化的列对象
    const normalizedCol = {
      Field: col.Field || col.field || col.name || col.column_name || '',
      Type: col.Type || col.type || col.data_type || '',
      Null: col.Null || col.nullable || (col.is_nullable === 'YES' ? 'YES' : 'NO') || 'YES',
      Key: col.Key || col.key || '',
      Default: col.Default || col.default || col.default_value || null,
      Extra: col.Extra || col.extra || '',
    };
    
    // 确保Null字段是正确的格式
    normalizedCol.Null = (normalizedCol.Null === true || normalizedCol.Null === 'YES' || normalizedCol.Null === 'yes') ? 'YES' : 'NO';
    
    return normalizedCol;
  });
}

const addColumn = () => {
  console.log('添加新字段函数被调用');
  try {
    localStructure.value.push({
      Field: '',
      Type: 'varchar(255)',
      Null: 'YES',
      Key: '',
      Default: null,
      Extra: '',
      isNew: true, // Mark as a new column
    });
    console.log('新字段已添加，当前字段数量:', localStructure.value.length);
  } catch (error) {
    console.error('添加字段时出错:', error);
  }
};

const resetTable = () => {
  console.log('重置表格');
  ElMessageBox.confirm('确定要重置所有更改吗？', '确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    localStructure.value = JSON.parse(JSON.stringify(originalStructure.value));
    console.log('表格已重置');
  }).catch(() => {
    // 用户取消操作
  });
};

const removeColumn = (index) => {
  console.log('正在删除索引为', index, '的字段');
  try {
    localStructure.value.splice(index, 1);
    console.log('删除成功，剩余字段数量:', localStructure.value.length);
  } catch (error) {
    console.error('删除字段时出错:', error);
  }
};

const handleSave = () => {
  const sql = generateAlterSql();
  if (!sql) {
    ElMessage.info('未检测到任何更改。');
    return;
  }
  
  ElMessageBox.confirm(
    `将要直接在数据库中执行以下操作，此操作不可逆，请确认：<br/><br/><strong><pre style="white-space: pre-wrap; word-break: break-all;">${sql}</pre></strong>`,
    '确认修改',
    {
      confirmButtonText: '确认执行',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
    }
  ).then(() => {
    emit('apply-changes', sql);
  }).catch(() => {
    ElMessage.info('操作已取消');
  });
};

const onDialogClosed = () => {
  // Reset local state when dialog is fully closed
  localStructure.value = [];
  originalStructure.value = [];
};

function onDialogOpen() {
  console.log('编辑表结构对话框已打开');
  console.log('表名:', props.tableName);
  console.log('模式名:', props.schemaName);
  console.log('初始表结构数据:', props.structure);
  console.log('当前本地表结构数据:', localStructure.value);
  
  // 如果没有数据，则添加一个空字段行
  if (localStructure.value.length === 0) {
    console.log('表结构为空，添加一个默认字段');
    addColumn();
  }
}

function generateAlterSql() {
  const originalCols = originalStructure.value.reduce((acc, col) => {
    acc[col.Field] = col;
    return acc;
  }, {});
  
  const currentCols = localStructure.value.reduce((acc, col) => {
    if (col.Field) {
      acc[col.Field] = col;
    }
    return acc;
  }, {});

  const added = [];
  const modified = [];
  const dropped = [];
  const renamed = {}; // For columns that are renamed

  // A more robust check for changes
  const originalFieldNames = new Set(originalStructure.value.map(c => c.Field));
  const currentFieldNames = new Set(localStructure.value.map(c => c.Field));

  // Find dropped columns
  originalFieldNames.forEach(fieldName => {
    if (!currentFieldNames.has(fieldName)) {
      dropped.push(`DROP COLUMN \`${fieldName}\``);
    }
  });

  // Find added columns
  localStructure.value.forEach(currentCol => {
    if (currentCol.isNew || !originalFieldNames.has(currentCol.Field)) {
       const defaultClause = currentCol.Default !== null ? ` DEFAULT '${currentCol.Default}'` : (currentCol.Null === 'NO' ? '' : ' DEFAULT NULL');
       added.push(`ADD COLUMN \`${currentCol.Field}\` ${currentCol.Type}${currentCol.Null === 'NO' ? ' NOT NULL' : ''}${defaultClause} ${currentCol.Extra}`);
    } else {
      // Find modified columns (in-place edits)
      const originalCol = originalCols[currentCol.Field];
      if (originalCol && JSON.stringify(originalCol) !== JSON.stringify(currentCol)) {
        const defaultClause = currentCol.Default !== null ? ` DEFAULT '${currentCol.Default}'` : (currentCol.Null === 'NO' ? '' : ' DEFAULT NULL');
        modified.push(`MODIFY COLUMN \`${currentCol.Field}\` ${currentCol.Type}${currentCol.Null === 'NO' ? ' NOT NULL' : ''}${defaultClause} ${currentCol.Extra}`);
      }
    }
  });

  if (added.length === 0 && modified.length === 0 && dropped.length === 0) {
    return '';
  }

  const allChanges = [...added, ...modified, ...dropped];
  const fullSql = `ALTER TABLE \`${props.tableName}\`\n  ` + allChanges.join(',\n  ');
  
  return fullSql;
}
</script>

<style>
.edit-table-structure-dialog .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 10px;
}

.edit-table-structure-dialog .table-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.edit-table-structure-dialog .el-table {
  margin-bottom: 15px;
}

.edit-table-structure-dialog .el-button.is-circle {
  margin: 0 5px;
}

/* 确保表格内的输入框正确显示 */
.edit-table-structure-dialog .el-input__inner {
  width: 100%;
}

/* 确保表格有足够的空间 */
.edit-table-structure-dialog .el-dialog__body {
  overflow-y: auto;
  max-height: 70vh;
}

/* 增加行之间的间距 */
.edit-table-structure-dialog .el-table__row {
  height: 50px;
}
</style> 