<template>
  <div class="query-toolbar">
    <el-button-group>
      <el-button type="primary" @click="$emit('add-new-query-tab')" :icon="Plus">
        新建查询
      </el-button>
      <el-button type="danger" @click="$emit('confirm-clear-all-tabs')" :icon="Delete">
        清除所有标签页
      </el-button>
      <el-button @click="$emit('show-saved-queries-dialog')" :icon="Files">
        已保存查询
      </el-button>
    </el-button-group>
  </div>
</template>

<script setup>
import { Plus, Delete, Files } from '@element-plus/icons-vue'

defineEmits(['add-new-query-tab', 'confirm-clear-all-tabs', 'show-saved-queries-dialog'])
</script>

<style scoped>
.query-toolbar {
  padding: 10px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #fff;
}

.el-button-group {
  margin-right: 10px;
}

.el-button {
  margin-right: 5px;
}

.el-button:last-child {
  margin-right: 0;
}
</style> 