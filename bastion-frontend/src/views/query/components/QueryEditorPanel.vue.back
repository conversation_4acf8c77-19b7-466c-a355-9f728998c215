<template>
  <div class="query-editor-panel-container">
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button type="primary" @click="onExecuteQuery" :loading="props.tab.loading">
            <el-icon><CaretRight /></el-icon> 执行
          </el-button>
          <el-button @click="onSaveCurrentQuery">
            <el-icon><FolderAdd /></el-icon> 保存
          </el-button>
          <el-button @click="onFormatSql">
            <el-icon><Files /></el-icon> 格式化
          </el-button>
        </el-button-group>
      </div>
      
      <div class="toolbar-center">
        <el-select 
          v-model="currentDatasourceId"
          placeholder="选择数据源"
          class="tab-db-select"
          size="small"
          :loading="databaseLoadingState"
          clearable
        >
          <template #prefix><el-icon><Connection /></el-icon></template>
          <el-option
            v-for="db in props.databases"
            :key="db.id"
            :label="db.name"
            :value="db.id"
          >
            <div class="db-option-item">
              <el-icon><Connection /></el-icon>
              <span>{{ db.name }}</span>
              <el-tag v-if="db.isProduction" type="danger" size="small">生产</el-tag>
            </div>
          </el-option>
        </el-select>

        <el-select 
          v-if="currentDatasourceId"
          v-model="currentSchema"
          placeholder="选择Schema"
          class="tab-schema-select"
          size="small"
          clearable
          :loading="schemaLoadingState"
        >
          <template #prefix><el-icon><Collection /></el-icon></template>
          <el-option
            v-for="schemaEntry in availableSchemasForTab"
            :key="schemaEntry.value"
            :label="schemaEntry.label"
            :value="schemaEntry.value"
          />
        </el-select>
      </div>
      
      <div class="toolbar-right">
        <!-- Placeholder for other options if needed -->
      </div>
    </div>
    
    <div class="editor-area-wrapper">
      <SqlEditor
        v-if="showEditor"
        :sqlContent="props.tab.sql"
        @update:sqlContent="(newSql) => emit('update:sql', newSql)"
        :extensions="props.extensions"
        :key="editorKey" 
        @editor-ready="(editorInstance) => emit('editorReady', editorInstance)"
      />
      <!-- <Codemirror 
        v-model="editableSql" 
        placeholder="输入SQL查询..."
        :style="{ height: '100%' }"
        :autofocus="true"
        :indent-with-tab="true"
        :tabSize="2"
        :extensions="props.extensions"
        @ready="(payload) => emit('editorReady', payload.view)" 
      /> -->
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits, nextTick } from 'vue';
import { ElSelect, ElOption, ElButton, ElButtonGroup, ElIcon, ElTag } from 'element-plus';
import { CaretRight, FolderAdd, Files, Connection, Collection } from '@element-plus/icons-vue';
import SqlEditor from './SqlEditor.vue'; // Existing component for CodeMirror
// import { Codemirror } from 'vue-codemirror'; // Using SqlEditor.vue instead

const props = defineProps({
  tab: { type: Object, required: true },
  databases: { type: Array, default: () => [] }, // Full list of DBs
  allSchemas: { type: Array, default: () => [] }, // Potentially all schemas from parent, or tab.schemas is already populated
  extensions: { type: Array, default: () => [] },
  isAdmin: { type: Boolean, default: false }, // For permissions if needed here
  // databaseLoadingState: { type: Boolean, default: false }, // If parent controls global loading for DBs
  // schemaLoadingState: { type: Boolean, default: false }, // If parent controls global loading for Schemas
});

const emit = defineEmits([
  'executeQuery',
  'saveCurrentQuery',
  'formatSql',
  'tabDatabaseChange',
  'tabSchemaChange',
  'editorReady',
  'update:sql'
]);

// To manage the SqlEditor instance, e.g., forcing re-render when tab changes if SqlEditor doesn't react to prop changes well
const editorKey = ref(0);
const showEditor = ref(true);

// Local computed properties bound to v-model, emitting changes upwards
const currentDatasourceId = computed({
  get: () => props.tab.datasourceId,
  set: (value) => {
    emit('tabDatabaseChange', value);
  }
});

const currentSchema = computed({
  get: () => props.tab.schema,
  set: (value) => {
    emit('tabSchemaChange', value);
  }
});

// Schemas available for the currently selected datasource in this tab
const availableSchemasForTab = computed(() => {
  if (props.tab.schemas && props.tab.schemas.length > 0) {
    return props.tab.schemas;
  }
  return props.allSchemas || [];
});

// Mimic loading states, parent should ideally pass these if they are global.
// For now, local placeholders or assume parent handles loading indicators outside.
const databaseLoadingState = ref(false); // Placeholder
const schemaLoadingState = ref(false);   // Placeholder

watch(() => props.tab.id, () => {
  // Force re-render of SqlEditor if underlying tab context changes significantly
  // This helps if SqlEditor has internal state that doesn't reset easily on prop changes.
  showEditor.value = false;
  editorKey.value++;
  nextTick(() => {
    showEditor.value = true;
  });
}, { immediate: true });


function onExecuteQuery() {
  emit('executeQuery');
}
function onSaveCurrentQuery() {
  emit('saveCurrentQuery');
}
function onFormatSql() {
  emit('formatSql');
}
function onTabDatabaseChange(newDbId) {
  // Emit the change to the parent, which will update the tab object
  // and potentially reload schemas for the tab.
  emit('tabDatabaseChange', newDbId);
}
function onTabSchemaChange(newSchema) {
  emit('tabSchemaChange', newSchema);
}

</script>

<style scoped>
.query-editor-panel-container {
  display: flex;
  flex-direction: column;
  /* Assuming it takes a portion of the height, e.g., 40%, resizable from parent or via splitter */
  min-height: 150px; /* Minimum sensible height */
  background-color: #fdfdfd;
}

.editor-toolbar {
  padding: 8px 12px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f7f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.tab-db-select {
  width: 200px;
}

.tab-schema-select {
  width: 180px;
}

.db-option-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-area-wrapper {
  flex-grow: 1;
  overflow: hidden; /* SqlEditor should handle its own scrolling */
  border-top: 1px solid #e0e0e0; /* Add a separator */
  position: relative; /* For potential absolute positioned elements inside SqlEditor or overlays */
}

/* Ensure SqlEditor takes full height of its wrapper */
:deep(.sql-editor-component) { /* Assuming SqlEditor.vue has a root class like this */
  height: 100%;
}
</style> 