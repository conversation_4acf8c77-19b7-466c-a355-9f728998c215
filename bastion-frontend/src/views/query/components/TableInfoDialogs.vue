<template>
  <div>
    <!-- 表结构弹窗 -->
    <el-dialog
      v-model="structureDialogVisible"
      :title="`表字段: ${schemaName ? schemaName + '.' : ''}${tableName}`"
      width="clamp(600px, 70vw, 800px)"
      class="table-structure-dialog"
      @closed="$emit('close-dialog', 'structure')"
      destroy-on-close
      append-to-body
    >
      <el-table
        :data="structureData"
        border
        style="width: 100%"
        max-height="60vh"
        stripe
        v-loading="loadingStructure"
        empty-text="未能加载表结构信息"
        table-layout="auto"
      >
        <el-table-column prop="Field" label="字段名" sortable show-overflow-tooltip />
        <el-table-column prop="Type" label="数据类型" sortable show-overflow-tooltip />
        <el-table-column prop="Null" label="可为空" sortable show-overflow-tooltip />
        <el-table-column prop="Key" label="键类型" sortable show-overflow-tooltip />
        <el-table-column prop="Default" label="默认值" sortable show-overflow-tooltip />
        <el-table-column prop="Extra" label="额外信息" sortable show-overflow-tooltip />
        <el-table-column prop="Comment" label="注释" sortable show-overflow-tooltip />
      </el-table>
       <template #footer>
        <el-button @click="structureDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 表索引弹窗 -->
    <el-dialog
      v-model="indexesDialogVisible"
      :title="`表索引: ${schemaName ? schemaName + '.' : ''}${tableName}`"
      width="clamp(700px, 80vw, 900px)"
      class="table-indexes-dialog"
      @closed="$emit('close-dialog', 'indexes')"
      destroy-on-close
      append-to-body
    >
      <el-table
        :data="indexesData"
        border
        style="width: 100%"
        max-height="60vh"
        stripe
        v-loading="loadingIndexes"
        empty-text="未能加载表索引信息"
        table-layout="auto"
      >
        <el-table-column prop="Table" label="表名" sortable show-overflow-tooltip />
        <el-table-column prop="Non_unique" label="是否唯一" sortable show-overflow-tooltip>
          <template #default="scope">{{ scope.row.Non_unique ? '否' : '是' }}</template>
        </el-table-column>
        <el-table-column prop="Key_name" label="索引名" sortable show-overflow-tooltip />
        <el-table-column prop="Seq_in_index" label="序号" sortable show-overflow-tooltip />
        <el-table-column prop="Column_name" label="列名" sortable show-overflow-tooltip />
        <el-table-column prop="Index_type" label="索引类型" sortable show-overflow-tooltip />
        <el-table-column prop="Comment" label="注释" sortable show-overflow-tooltip />
      </el-table>
      <template #footer>
        <el-button @click="indexesDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 表结构SQL弹窗 -->
    <el-dialog
      v-model="ddlDialogVisible"
      :title="`表结构SQL (DDL): ${schemaName ? schemaName + '.' : ''}${tableName}`"
      width="clamp(600px, 70vw, 800px)"
      class="table-ddl-dialog"
      @closed="$emit('close-dialog', 'ddl')"
      destroy-on-close
      append-to-body
    >
      <div class="sql-code-container" v-loading="loadingDdl">
        <pre v-if="!loadingDdl && ddlData">{{ ddlData }}</pre>
        <el-empty v-if="!loadingDdl && !ddlData" description="未能加载DDL信息" />
      </div>
      <template #footer>
        <el-button type="primary" @click="copyToClipboard(ddlData)" :disabled="loadingDdl || !ddlData">复制SQL</el-button>
        <el-button @click="ddlDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { ElDialog, ElTable, ElTableColumn, ElButton, ElMessage, ElEmpty } from 'element-plus';

const props = defineProps({
  showStructure: { type: Boolean, default: false },
  showIndexes: { type: Boolean, default: false },
  showDdl: { type: Boolean, default: false },
  datasourceId: { type: [String, Number], default: null },
  schemaName: { type: String, default: '' },
  tableName: { type: String, default: '' },
  structureData: { type: Array, default: () => [] },
  indexesData: { type: Array, default: () => [] },
  ddlData: { type: String, default: '' },
  loadingStructure: { type: Boolean, default: false },
  loadingIndexes: { type: Boolean, default: false },
  loadingDdl: { type: Boolean, default: false },
});

const emit = defineEmits([
  'update:showStructure',
  'update:showIndexes',
  'update:showDdl',
  'close-dialog'
]);

const structureDialogVisible = computed({
  get: () => props.showStructure,
  set: (value) => emit('update:showStructure', value)
});

const indexesDialogVisible = computed({
  get: () => props.showIndexes,
  set: (value) => emit('update:showIndexes', value)
});

const ddlDialogVisible = computed({
  get: () => props.showDdl,
  set: (value) => emit('update:showDdl', value)
});

async function copyToClipboard(text) {
  if (!text) {
    ElMessage.warning('没有内容可复制');
    return;
  }
  try {
    await navigator.clipboard.writeText(text);
    ElMessage.success('已复制到剪贴板');
  } catch (err) {
    console.error('复制失败:', err);
    ElMessage.error('复制失败');
  }
}
</script>

<style scoped>
.sql-code-container {
  position: relative;
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 15px;
  min-height: 100px; /* Ensure it has some height when loading */
  max-height: 60vh;
  overflow-y: auto;
}
.sql-code-container pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: 'Courier New', Courier, monospace;
  font-size: 13px;
  color: #333;
}
.el-dialog__footer {
    padding-top: 10px; /* Add some space if footer feels too close */
}
</style> 