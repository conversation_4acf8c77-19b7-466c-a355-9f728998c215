<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="600px"
    :close-on-click-modal="true"
    :append-to-body="true"
    :modal-append-to-body="false"
  >
    <div v-if="!isEditing" class="cell-detail-content">
      <pre>{{ content }}</pre>
    </div>
    <div v-else class="cell-edit-content">
      <el-input
        v-model="editValue"
        :type="inputType"
        :rows="5"
        :autosize="{ minRows: 3, maxRows: 10 }"
        placeholder="请输入新的值"
      />
    </div>
    <template #footer>
      <div v-if="!isEditing">
        <el-button type="primary" @click="handleCopy">复制内容</el-button>
        <el-button 
          type="warning" 
          @click="startEditing" 
          v-if="canEdit"
        >编辑</el-button>
        <el-button @click="closeDialog">关闭</el-button>
      </div>
      <div v-else>
        <el-button type="primary" @click="submitEdit">保存修改</el-button>
        <el-button @click="cancelEditing">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 定义props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  content: {
    type: String,
    default: ''
  },
  canEdit: {
    type: Boolean,
    default: false
  },
  rowData: {
    type: Object,
    default: () => ({})
  },
  columnName: {
    type: String,
    default: ''
  }
})

// 定义emit
const emit = defineEmits([
  'update:visible',
  'copy',
  'edit',
  'close',
  'submit'
])

// 本地状态
const isEditing = ref(false)
const editValue = ref('')

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const inputType = computed(() => {
  if (!props.content) return 'text'
  
  // 如果内容很长，使用textarea
  if (props.content.length > 100) {
    return 'textarea'
  }
  
  // 尝试判断是否为数字
  if (!isNaN(props.content)) {
    return 'text' // 使用text而非number，以支持大整数
  }
  
  // 尝试判断是否为日期
  if (/^\d{4}-\d{2}-\d{2}/.test(props.content)) {
    return 'text'
  }
  
  return 'text'
})

// 方法
const handleCopy = async () => {
  try {
    await navigator.clipboard.writeText(props.content)
    ElMessage.success('内容已复制到剪贴板')
    emit('copy', props.content)
  } catch (err) {
    ElMessage.error('复制失败: ' + err.message)
  }
}

const startEditing = () => {
  if (!props.canEdit) {
    ElMessage.warning('您没有编辑权限')
    return
  }
  isEditing.value = true
  editValue.value = props.content
  emit('edit', true)
}

const cancelEditing = () => {
  isEditing.value = false
  editValue.value = ''
  emit('edit', false)
}

const submitEdit = () => {
  if (editValue.value === props.content) {
    ElMessage.info('值未变化，无需更新')
    isEditing.value = false
    return
  }
  
  emit('submit', {
    newValue: editValue.value,
    oldValue: props.content,
    columnName: props.columnName,
    rowData: props.rowData
  })
  
  isEditing.value = false
  editValue.value = ''
}

const closeDialog = () => {
  dialogVisible.value = false
  emit('close')
}
</script>

<style scoped>
.cell-detail-content {
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
  max-height: 400px;
  overflow: auto;
}

.cell-detail-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: Consolas, Monaco, monospace;
  line-height: 1.5;
}

.cell-edit-content {
  padding: 15px;
}

:deep(.el-input__inner), :deep(.el-textarea__inner) {
  font-family: Consolas, Monaco, monospace;
}
</style> 