<template>
  <div class="query-results-container">
    <div v-if="props.queryError" class="query-error-panel">
        <el-alert
        title="查询执行遇到问题"
          type="warning"
        :description="formatErrorMessage(props.queryError)"
          show-icon
          :closable="false"
        >
          <template #default>
            <div class="error-help-text">
              <p>可能的解决方法:</p>
              <ul>
                <li>检查SQL语法是否正确</li>
                <li>确认表名和字段名拼写无误</li>
                <li>如需帮助，请联系系统管理员</li>
              </ul>
            </div>
          </template>
        </el-alert>
      </div>

    <div v-if="props.queryInProgress && (!props.results || props.results.length === 0)" class="loading-results">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>正在查询，请稍候...</span>
    </div>

    <div v-if="!props.queryInProgress && props.results && props.results.length === 0 && !props.queryError && props.executedQuery" class="no-results">
      <el-empty description="查询成功，但没有返回数据">
        <template #description>
          <div>
            <p>查询成功执行，但未找到匹配的数据</p>
            <small class="empty-tips">
              <p>可能的原因：</p>
              <ul>
                <li>表中没有符合条件的数据</li>
                <li>表是空的</li>
                <li>查询条件过于严格</li>
              </ul>
              <p>建议：</p>
              <ul>
                <li>检查表是否有数据</li>
                <li>放宽查询条件</li>
                <li>确认表名正确</li>
              </ul>
            </small>
          </div>
        </template>
      </el-empty>
    </div>
          
    <div v-show="props.results && props.results.length > 0 && !props.queryError" class="results-table-wrapper">
      <div class="query-meta-info">
        <span>执行耗时: {{ props.executionTime }} ms</span>
        <span>返回行数: {{ props.rowCount }}</span>
        <span v-if="props.selectedTable">当前表: {{ props.selectedTable }}</span>
      </div>

      <!-- 调试信息 -->
      <div class="debug-info" v-if="showDebugInfo">
        <h4>调试信息</h4>
        <div><strong>结果数量:</strong> {{ props.results.length }}</div>
        <div><strong>列信息:</strong> {{ JSON.stringify(props.columns) }}</div>
        <div v-if="props.results.length > 0">
          <strong>第一行数据:</strong> {{ JSON.stringify(props.results[0]) }}
        </div>
        <el-button size="small" @click="showDebugInfo = false">隐藏调试信息</el-button>
      </div>

      <!-- 使用可编辑表格组件 -->
      <EditableResultTable
        v-if="props.selectedTable"
        ref="editableTableRef"
        :data="props.results"
        :columns="tableColumns"
        :is-read-only="isReadOnly"
        :is-clickhouse="isClickhouseDataSource"
        @cell-dblclick="handleCellDoubleClick"
        @update-cell-value="handleUpdateCellValue"
        @submit:changes="handleSubmitChanges"
        @toggle-edit-mode="handleToggleEditMode"
        @generate-backup-sql="handleGenerateBackupSql"
        @pending-changes-updated="handlePendingChangesUpdated"
        @selection-changed="handleSelectionChanged"
      />
      
      <!-- 对于非表查询结果，使用普通表格 -->
      <el-table
        v-else
        :data="props.results" 
        border
        stripe
        style="width: 100%"
        height="100%" 
        @cell-dblclick="handleCellDoubleClick"
        v-loading="props.queryInProgress"
      >
        <el-table-column 
          v-for="columnName in props.columns"
          :key="columnName"
          :prop="columnName"
          :label="columnName"
          sortable
          show-overflow-tooltip
        >
          <template #default="scope">
            <span v-if="scope.row[columnName] === null || scope.row[columnName] === undefined">
              <em>NULL</em>
            </span>
            <span v-else>{{ scope.row[columnName] }}</span>
          </template>
        </el-table-column>
      </el-table>
            
      <el-pagination
        v-if="props.totalRows > 0 && props.totalRows > props.pageSize" 
        class="results-pagination"
        :current-page="props.currentPage"
        :page-size="props.pageSize"
        :total="props.totalRows"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[50, 100, 200, 500]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentPageChange"
      />
    </div>
    
    <CellOperationsDialog
      v-model:visible="longContentDialogVisible"
      :title="longContentTitle"
      :content="longContentToShow"
    />
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue';
import { ElTable, ElTableColumn, ElPagination, ElAlert, ElEmpty, ElIcon, ElButton } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import CellOperationsDialog from './CellOperationsDialog.vue';
import EditableResultTable from '../result/EditableResultTable.vue';

const props = defineProps({
  results: { type: Array, default: () => [] },
  columns: { type: Array, default: () => [] },
  queryError: { type: String, default: null },
  queryInProgress: { type: Boolean, default: false },
  executionTime: { type: Number, default: 0 },
  rowCount: { type: Number, default: 0 },
  selectedTable: { type: String, default: '' },
  currentPage: { type: Number, default: 1 },
  pageSize: { type: Number, default: 50 },
  totalRows: { type: Number, default: 0 },
  executedQuery: { type: Boolean, default: false },
  isClickhouseDataSource: { type: Boolean, default: false }
});

const emit = defineEmits([
  'page-change',
  'size-change',
  'cell-action',
  'update-cell-value',
  'submit:changes',
  'generate-backup-sql',
  'pending-changes-updated',
  'selection-changed'
]);

const longContentDialogVisible = ref(false);
const longContentTitle = ref('查看单元格内容');
const longContentToShow = ref('');
const showDebugInfo = ref(false); // 默认关闭调试信息
const isReadOnly = ref(false); // 默认只读模式

// 表格引用
const editableTableRef = ref(null);

// 表格列直接使用 props.columns，不再需要复杂的计算属性
const tableColumns = computed(() => props.columns || []);

const handleCellDoubleClick = (row, column) => {
  const property = column.property;
  const displayValue = row[property]; // 直接使用原始值
  
  if (displayValue === null || displayValue === undefined) return;
  
  // 如果内容较长或包含换行符，显示详情对话框
  if (displayValue.length > 80 || displayValue.includes('\n')) {
    longContentTitle.value = `查看 ${column.label || property} 内容`;
    longContentToShow.value = displayValue;
    longContentDialogVisible.value = true;
    emit('cell-action', { action: 'view_long_content', row, column, value: displayValue });
  }
};

const handleSizeChange = (newPageSize) => {
  emit('size-change', newPageSize);
};

const handleCurrentPageChange = (newCurrentPage) => {
  emit('page-change', newCurrentPage);
};

// 处理单元格值更新
const handleUpdateCellValue = (payload) => {
  emit('update-cell-value', payload);
};

// 处理提交更改
const handleSubmitChanges = (changes) => {
  emit('submit:changes', changes);
};

// 处理切换编辑模式
const handleToggleEditMode = (isReadOnlyMode) => {
  isReadOnly.value = isReadOnlyMode;
};

// 处理生成备份SQL事件
const handleGenerateBackupSql = (payload) => {
  emit('generate-backup-sql', payload);
};

// 处理待提交变更更新事件
const handlePendingChangesUpdated = (changes) => {
  emit('pending-changes-updated', changes);
};

// 处理选择状态变化事件
const handleSelectionChanged = (count) => {
  emit('selection-changed', count);
};

// 格式化错误消息，使其更友好
const formatErrorMessage = (errorMsg) => {
  if (!errorMsg) return '未知错误';
  
  // 处理常见的错误提示，使其更友好
  if (errorMsg.includes('permission denied') || errorMsg.includes('没有权限')) {
    return '您暂时没有执行此查询的权限，请联系系统管理员申请';
  }
  
  if (errorMsg.includes('syntax error') || errorMsg.includes('语法错误')) {
    return '查询语法错误，请检查SQL语句';
  }
  
  if (errorMsg.includes('table') && (errorMsg.includes('not exist') || errorMsg.includes('不存在'))) {
    return '表不存在，请确认表名是否正确';
  }
  
  if (errorMsg.includes('column') && (errorMsg.includes('not exist') || errorMsg.includes('不存在'))) {
    return '字段不存在，请确认字段名是否正确';
  }
  
  // 如果没有匹配到常见错误，返回原始错误
  return errorMsg;
};

console.log('当前页数据:', props.results);

// 暴露表格的方法给父组件
defineExpose({
  handleDeleteSelected: () => {
    if (editableTableRef.value && editableTableRef.value.handleDeleteSelected) {
      editableTableRef.value.handleDeleteSelected();
    }
  },
  handleAddRow: () => {
    if (editableTableRef.value && editableTableRef.value.handleAddRow) {
      editableTableRef.value.handleAddRow();
    }
  },
  handleSubmitChanges: () => {
    if (editableTableRef.value && editableTableRef.value.handleSubmitChanges) {
      editableTableRef.value.handleSubmitChanges();
    }
  },
  selectedRowKeys: computed(() => {
    return editableTableRef.value?.selectedRowKeys || new Set();
  }),
  pendingChanges: computed(() => {
    return editableTableRef.value?.pendingChanges || [];
  })
});

</script>

<style lang="scss" scoped>
.query-results-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .query-error-panel {
    margin-bottom: 16px;
  }
  
  .loading-results {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #909399;
    
    .el-icon {
      margin-right: 8px;
      font-size: 20px;
    }
  }
  
  .no-results {
    padding: 40px;
    
    .empty-tips {
      color: #909399;
      font-size: 13px;
      margin-top: 16px;
      text-align: left;
      display: block;

      p {
        margin: 8px 0;
        font-weight: 500;
      }

      ul {
        margin: 4px 0;
        padding-left: 20px;
        
        li {
          margin: 4px 0;
          list-style-type: disc;
        }
      }
    }
  }
  
  .results-table-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    
    .query-meta-info {
      margin-bottom: 8px;
      font-size: 13px;
      color: #606266;
      
      span {
        margin-right: 16px;
        
        &:last-child {
          margin-right: 0;
        }
      }
    }
    
    .el-table {
      flex: 1;
      min-height: 0;
      
      :deep(.el-table__empty-text) {
        display: none;
      }
    }
    
    .results-pagination {
      margin-top: 16px;
      justify-content: flex-end;
    }
  }
  
  .debug-info {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    
    h4 {
      margin: 0 0 8px;
      font-size: 14px;
    }
    
    div {
      margin-bottom: 4px;
      font-size: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
<style scoped>
.query-results-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background-color: #fff;
}

.query-error-panel {
  margin: 15px;
}

.loading-results {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 150px;
  color: #909399;
}
.loading-results .el-icon {
  font-size: 28px;
  margin-bottom: 10px;
}

.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 150px;
}

.results-table-wrapper {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; 
}

.query-meta-info {
  padding: 8px 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  font-size: 12px;
  color: #606266;
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  flex-shrink: 0;
}

.debug-info {
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  margin-bottom: 10px;
  font-size: 12px;
  overflow: auto;
  max-height: 200px;
}

.debug-info h4 {
  margin-top: 0;
  margin-bottom: 8px;
}

.debug-info div {
  margin-bottom: 5px;
}

.el-table {
  flex-grow: 1;
  width: 100%;
}

.el-table em {
  color: #909399;
  font-style: italic;
}

.results-pagination {
  padding: 10px 5px;
  background-color: #fff;
  border-top: 1px solid #ebeef5;
  flex-shrink: 0;
}

.error-help-text {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 165, 0, 0.2);
  font-size: 13px;
}

.error-help-text p {
  margin: 0 0 5px 0;
  font-weight: bold;
}

.error-help-text ul {
  margin: 0;
  padding-left: 20px;
}

.error-help-text li {
  margin-bottom: 3px;
}

.empty-tips {
  color: #909399;
  display: block;
  margin-top: 5px;
}
</style> 