<template>
  <el-dialog
    v-model="isDialogVisible"
    title="已保存查询"
    width="clamp(600px, 80vw, 900px)"
    class="saved-queries-dialog"
    :before-close="handleClose"
    @opened="onDialogOpened"
    destroy-on-close
    :close-on-click-modal="false"
    :append-to-body="true"
    :modal-append-to-body="false"
    :z-index="2000"
  >
    <div class="saved-queries-header">
      <el-input
        v-model="searchQuery"
        placeholder="搜索名称、描述、SQL内容..."
        clearable
        size="default"
        class="search-input"
      >
        <template #prefix><el-icon><Search /></el-icon></template>
      </el-input>
      
      <el-select
        v-model="datasourceFilter"
        placeholder="按数据源筛选"
        clearable
        size="default"
        class="datasource-filter-select"
      >
        <el-option
          v-for="db in props.databases"
          :key="db.id"
          :label="db.name"
          :value="db.id"
        />
      </el-select>
      <el-button @click="refreshList" :loading="isLoading" icon="RefreshRight" circle />
    </div>
    
    <el-table
      :data="filteredQueries"
      border
      stripe
      style="width: 100%; margin-top: 16px;"
      v-loading="isLoading"
      height="450px"
      empty-text="没有找到已保存的查询"
    >
      <el-table-column prop="name" label="查询名称" min-width="150" show-overflow-tooltip sortable />
      <el-table-column label="SQL内容预览" min-width="250" show-overflow-tooltip>
        <template #default="scope">
          <div class="sql-preview-cell">
            <span class="sql-text">{{ scope.row.sql_content ? previewSql(scope.row.sql_content) : '-' }}</span>
            <el-button 
              v-if="scope.row.sql_content"
              type="primary" 
              link 
              size="small" 
              @click.stop="viewFullSql(scope.row)"
            >
              查看完整SQL
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" min-width="180" show-overflow-tooltip sortable />
      <el-table-column label="数据源" min-width="130" show-overflow-tooltip sortable prop="datasource_name">
        <template #default="scope">
          <el-tag size="small" type="info" v-if="scope.row.datasource_name || (scope.row.datasource && scope.row.datasource.name)">
            {{ scope.row.datasource_name || (scope.row.datasource && scope.row.datasource.name) }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="schema" label="Schema" min-width="100" show-overflow-tooltip sortable>
        <template #default="scope">
          <span>{{ scope.row.schema || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" min-width="160" show-overflow-tooltip sortable prop="created_at">
        <template #default="scope">
          {{ formatTimeString(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="180" align="center">
        <template #default="scope">
          <el-button size="small" type="primary" @click="() => handleImport(scope.row)" icon="Download">导入</el-button>
          <el-button size="small" type="danger" @click="() => handleDelete(scope.row)" icon="Delete">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- Dialog for viewing full SQL -->
    <el-dialog
      v-model="isSqlViewerVisible"
      :title="`SQL内容: ${currentViewingQueryName}`"
      width="clamp(500px, 70vw, 800px)"
      append-to-body
      destroy-on-close
    >
      <div class="full-sql-viewer">
        <pre>{{ currentFullSql }}</pre>
      </div>
      <template #footer>
        <el-button type="primary" @click="copySqlToClipboard(currentFullSql)">复制SQL</el-button>
        <el-button @click="isSqlViewerVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits, onMounted } from 'vue';
import { ElDialog, ElInput, ElSelect, ElOption, ElTable, ElTableColumn, ElButton, ElTag, ElIcon, ElMessage, ElMessageBox } from 'element-plus';
import { Search, RefreshRight, Download, Delete } from '@element-plus/icons-vue';

const props = defineProps({
  visible: { type: Boolean, default: false },
  databases: { type: Array, default: () => [] }, // For datasource filter
  isLoadingExternally: { type: Boolean, default: false }, // If parent controls initial loading
  savedQueriesList: {type: Array, default: () => [] } // Parent can pass pre-fetched queries
});

const emit = defineEmits(['update:visible', 'importQuery', 'deleteQuery', 'refreshQueries']);

const searchQuery = ref('');
const datasourceFilter = ref(null);
const internalSavedQueries = ref([]);
const isLoading = ref(false);

const isSqlViewerVisible = ref(false);
const currentFullSql = ref('');
const currentViewingQueryName = ref('');

const isDialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

watch(() => props.visible, (newVal) => {
  if (newVal) {
    // When dialog becomes visible, if savedQueriesList prop is provided, use it.
    // Otherwise, parent should listen to 'refreshQueries' or trigger it.
    if (props.savedQueriesList.length > 0) {
        internalSavedQueries.value = [...props.savedQueriesList];
    } else {
        // If not pre-filled, ask parent to refresh or do it here if this component owns fetching
        // For now, assume parent will provide or emit refreshQueries will be called by parent if needed.
    }
  }
});

watch(() => props.savedQueriesList, (newList) => {
    internalSavedQueries.value = [...newList];
}, { deep: true });

const filteredQueries = computed(() => {
  let result = [...internalSavedQueries.value];
  if (datasourceFilter.value) {
    result = result.filter(q => 
      q.datasource_id === datasourceFilter.value || 
      (q.datasource && q.datasource.id === datasourceFilter.value)
    );
  }
  if (searchQuery.value) {
    const keyword = searchQuery.value.toLowerCase();
    result = result.filter(q => 
      (q.name?.toLowerCase().includes(keyword)) ||
      (q.description?.toLowerCase().includes(keyword)) ||
      (q.sql_content?.toLowerCase().includes(keyword))
    );
  }
  return result;
});

function previewSql(sql) {
  if (!sql) return '-';
  const cleanedSql = sql.replace(/\s+/g, ' ').trim();
  return cleanedSql.length > 100 ? cleanedSql.substring(0, 97) + '...' : cleanedSql;
}

function formatDisplayDate(dateString) {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    return date.toLocaleString(); // Or a more specific format
  } catch (e) {
    return dateString; // Fallback to original string if parsing fails
  }
}

function viewFullSql(query) {
  currentFullSql.value = query.sql_content || '';
  currentViewingQueryName.value = query.name || '未命名查询';
  isSqlViewerVisible.value = true;
}

async function copySqlToClipboard(sql) {
  try {
    await navigator.clipboard.writeText(sql);
    ElMessage.success('SQL已复制到剪贴板');
  } catch (err) {
    ElMessage.error('复制SQL失败');
  }
}

function handleImport(query) {
  emit('importQuery', query);
  // isDialogVisible.value = false; // Parent can decide to close dialog after import
}

function handleDelete(query) {
  ElMessageBox.confirm(
    `确定要永久删除查询 "${query.name}" 吗？此操作不可撤销。`,
    '确认删除',
    { confirmButtonText: '确定删除', cancelButtonText: '取消', type: 'warning' }
  ).then(() => {
    emit('deleteQuery', query.id);
  }).catch(() => {
    ElMessage.info('已取消删除操作');
  });
}

function refreshList() {
    // This component can either fetch itself or ask parent via event
    emit('refreshQueries'); 
}

function handleClose() {
  isDialogVisible.value = false;
}

function onDialogOpened() {
    // If list is empty and not externally loading, trigger a refresh
    if (internalSavedQueries.value.length === 0 && !props.isLoadingExternally) {
        refreshList();
    }
}

function formatTimeString(timeStr) {
  if (!timeStr) return '-';
  
  if (typeof timeStr === 'string' && timeStr.includes('T')) {
    return timeStr.replace('T', ' ');
  }
  
  return timeStr;
}

// Expose refresh method if parent wants to trigger it externally
// defineExpose({ refreshList });

</script>

<style scoped>
.saved-queries-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
}
.search-input {
  flex-grow: 1;
  min-width: 200px;
}
.datasource-filter-select {
  width: 220px;
}

.sql-preview-cell {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.sql-preview-cell .sql-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%; /* Ensure it doesn't overflow cell */
  display: inline-block; /* Or block if it should take full width before ellipsis */
}
.sql-preview-cell .el-button {
  margin-top: 4px;
  padding-left: 0; /* Align with text */
}

.full-sql-viewer pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  white-space: pre-wrap; 
  word-break: break-all; 
  max-height: 60vh;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
}

.el-dialog__body .el-table {
    margin-top: 0 !important; /* Override if necessary */
}

:deep(.el-dialog) {
  z-index: 2000 !important;
}

:deep(.el-dialog__wrapper) {
  z-index: 2000 !important;
}

:deep(.v-modal) {
  z-index: 1999 !important;
}
</style> 