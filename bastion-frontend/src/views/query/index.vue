<template>
  <div class="sql-query-container">
    <div class="sql-query-main">
      <div class="left-panel">
        <ObjectBrowser
          :database-objects="databaseObjects"
          :tree-loading="treeLoading"
          :is-refreshing-datasource="isRefreshingDatasource"
          v-model:object-filter="objectFilter"
          :active-tab-datasource-id="selectedDatabase"
          :current-schema="selectedSchema"
          @refresh-datasource="forceRefreshObjectBrowser"
          @node-click="handleNodeClickInObjectBrowser"
          @node-right-click="handleNodeRightClickInObjectBrowser"
        />
      </div>

      <div class="query-content-area">
        <QueryTabsManager
          ref="queryTabsManagerRef"
          :databases="databases"
          :extensions="extensions"
          :is-admin="isAdmin"
          :user-info="userInfo"
          @active-tab-changed="handleActiveTabChange"
          @execute-query="handleExecuteQuery"
          @format-sql="handleFormatSql"
          @refresh-autocomplete-cache="handleRefreshAutocompleteCache"
          @tab-database-change="handleTabDatabaseChange"
          @update-cell-value="handleUpdateCellValue"
          @request-edit-permission="handleRequestEditPermission"
          @submit:changes="handleSubmitChanges"
          @cancel-query="handleCancelQuery"
        />
      </div>
    </div>
    
    <!-- Dialogs -->
    <SaveQueryDialog
      v-model:visible="saveQueryDialogVisible"
      :query-data="queryDataForSaveDialog"
      :datasource-name="currentDatasourceNameForDialog"
      :schema-name="currentSchemaNameForDialog"
      :is-saving="savingQuery"
      @confirm="handleConfirmSaveQuery"
    />

    <SavedQueriesListDialog
      v-model:visible="savedQueriesListDialogVisible"
      :databases="databases"
      :saved-queries-list="savedQueries"
      :is-loading-externally="savedQueriesLoading"
      @import-query="handleImportSavedQuery"
      @delete-query="handleDeleteSavedQuery"
      @refresh-queries="handleShowSavedQueriesList"
    />

    <TableInfoDialogs
      v-model:show-structure="tableStructureDialogVisible"
      v-model:show-indexes="tableIndexesDialogVisible"
      v-model:show-ddl="tableDDLDialogVisible"
      :datasource-id="currentTableForInfo.datasourceId"
      :schema-name="currentTableForInfo.schema"
      :table-name="currentTableForInfo.name"
      :structure-data="tableStructureData"
      :indexes-data="tableIndexesData"
      :ddl-data="tableDDLData"
      :loading-structure="tableInfoLoading" 
      :loading-indexes="tableInfoLoading"
      :loading-ddl="tableInfoLoading"
      @close-dialog="handleTableInfoSubDialogClose"
    />

    <EditTableStructureDialog
      v-model:show="editTableStructureDialogVisible"
      :table-name="currentTableForInfo.name"
      :schema-name="currentTableForInfo.schema"
      :structure="tableStructureData"
      :loading="tableInfoLoading"
      @apply-changes="handleApplyAlterSql"
    />

    <CellOperationsDialog
      v-model:visible="cellOperationsDialogVisible"
      :title="cellContentTitle"
      :content="cellContentToShow"
    />
  </div>

  <!-- Custom Context Menu for Object Browser -->
  <div
    v-if="contextMenu.visible"
    class="custom-context-menu"
    :style="{ top: `${contextMenu.top}px`, left: `${contextMenu.left}px` }"
  >
  <ul>
    <li @click="viewTableStructure"><el-icon><View /></el-icon> 查看表结构</li>
    <li @click="viewTableIndexes"><el-icon><Grid /></el-icon> 查看表索引</li>
    <li @click="viewTableDDL"><el-icon><Files /></el-icon> 查看建表语句</li>
    <li @click="editTableStructure"><el-icon><Edit /></el-icon> 编辑表结构</li>
</ul>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick, onMounted, watch, onBeforeUnmount } from 'vue';
import { ElMessage, ElMessageBox, ElNotification, ElLoading } from 'element-plus';
import { 
  Connection, Search, Collection, Document, 
  Refresh, Fold, View, Grid, Operation, 
  More, Download, Upload, CopyDocument, Edit,
  FolderAdd, CaretRight, Files, Plus, CircleClose, Delete, ArrowDown, Lock, Setting
} from '@element-plus/icons-vue';
import { useRoute, useRouter } from 'vue-router';

// API Imports
import { 
  getDatasources, // Used for fetchDatabases
  getSchemaList,
  getDatabaseSchema
} from '@/api/datasource';

import {
  executeQuery as executeQueryAPI,
  saveQuery as saveQueryDialogAPI,
  getSavedQueries,
  getSavedQueryById,
  deleteSavedQuery as deleteSavedQueryAPI,
  updateSavedQuery as updateSavedQueryAPI,
  getTableStructure as getTableStructureAPI,
  getTableIndexes as getTableIndexesAPI,
  exportTableStructure as exportTableStructureAPI,
  getTableDDL as getTableDDLAPI,
  executeDdl as executeDdlAPI,
  updateCellValue as updateCellValueAPI // 添加更新单元格值的API
  // getQueryHistory, // Deferred
  // exportTableData as exportTableDataAPI, // Deferred
  // editTableData as editTableDataAPI, // Deferred
  // deleteQueryRecord, // Deferred
} from '@/api/query.js';

import { 
  getDatabaseTables, 
  getTableColumns, 
  getDatabaseFullMetadata,
  submitBatchChanges // 导入批量提交函数
} from '@/api/database';

import { 
  hasTablePermission, 
  // getUserPermittedTables, // Functionality will be integrated if needed
  clearPermissionCache,
  refreshTablePermissions
} from '@/utils/permission';
import { getCurrentUser as getCurrentUserAPI } from '@/api/user'; // Import for current user
// import { getTablePermissions } from '@/api/permission'; // Likely not needed directly, refreshTablePermissions uses getUserTables

// Component Imports
import ObjectBrowser from './components/ObjectBrowser.vue';
import QueryTabsManager from './components/QueryTabsManager.vue';
import ActiveQueryPanel from './components/ActiveQueryPanel.vue';
import SaveQueryDialog from './components/SaveQueryDialog.vue';
import SavedQueriesListDialog from './components/SavedQueriesListDialog.vue';
import TableInfoDialogs from './components/TableInfoDialogs.vue';
import CellOperationsDialog from './components/CellOperationsDialog.vue';
import EditTableStructureDialog from './components/EditTableStructureDialog.vue';
// import SqlEditor from '@/components/SqlEditor.vue'; // Will be part of ActiveQueryPanel

const route = useRoute();
const router = useRouter();

// Global loading states
const mainLoading = ref(false); // For overall page/initial load

// Datasource and Schema state (for Object Browser and global context)
const databases = ref([]); // List of all available/permitted datasources for selection
const schemas = ref([]);   // List of schemas for the selectedDatabase in Object Browser
const selectedDatabase = ref(null); // Bound to Object Browser's selected datasource
const selectedSchema = ref('');   // Bound to Object Browser's selected schema
const databaseObjects = ref([]);  // Data for the Object Browser tree
const treeLoading = ref(false);   // Loading state for the Object Browser tree
const objectFilter = ref('');     // Filter text for the Object Browser
const isRefreshingDatasource = ref(false); // For datasource refresh button state
const activeTab = ref(null); // To store active tab context, mainly for OB datasourceId sync
const allSchemas = ref([]); // For QueryTabsManager schema dropdown if needed globally
const extensions = ref([]); // For CodeMirror setup in QueryTabsManager
const userInfo = ref({}); // For user info to QueryTabsManager
const isProgrammaticRefresh = ref(false); // Lock to prevent race conditions on refresh

let databaseObjectsCache = {};

// Query Tabs State (managed by QueryTabsManager, but index.vue might need to interact)
// activeTab, queryTabs, etc. will be managed by QueryTabsManager and communicated via events/props.
// However, index.vue will handle the execution requests originating from a tab.

// Save Query Dialog State
const saveQueryDialogVisible = ref(false);
const queryDataForSaveDialog = ref({}); // To pass data to the save dialog
const savingQuery = ref(false); // Loading state for the save operation

const currentDatasourceNameForDialog = computed(() => {
  if (queryDataForSaveDialog.value?.datasourceId && databases.value) {
    const ds = databases.value.find(db => db.id === queryDataForSaveDialog.value.datasourceId);
    return ds ? ds.name : '未知数据源';
  }
  return 'N/A';
});

const currentSchemaNameForDialog = computed(() => {
  return queryDataForSaveDialog.value?.schema || 'N/A';
});


// Saved Queries List Dialog State
const savedQueriesListDialogVisible = ref(false);
const savedQueries = ref([]); // Raw list from API
const savedQueriesLoading = ref(false);
// savedQueriesSearch, savedQueriesDatasourceFilter will be internal to SavedQueriesListDialog.vue

// Table Info Dialogs State (for viewing table structure, indexes, DDL)
const tableInfoDialogsVisible = ref(false); // A single master visibility for the container
const tableStructureDialogVisible = ref(false);
const tableIndexesDialogVisible = ref(false);
const tableDDLDialogVisible = ref(false); // Renamed from tableStructureSqlDialogVisible for clarity
const editTableStructureDialogVisible = ref(false);

const currentTableForInfo = ref({ name: '', schema: '', datasourceId: null }); // Holds context for which table's info is being viewed
const tableStructureData = ref([]);
const tableIndexesData = ref([]);
const tableDDLData = ref(''); // For DDL SQL string
const tableInfoLoading = ref(false); // Common loading for fetching any table info

// Cell Operations Dialog State (for viewing long cell content)
const cellOperationsDialogVisible = ref(false);
const cellContentToShow = ref('');
const cellContentTitle = ref('');

// User Info & Permissions
const currentUser = ref(null); 
const isAdmin = computed(() => !!(currentUser.value && (currentUser.value.is_superuser || currentUser.value.is_staff)));

const queryTabsManagerRef = ref(null); // Ref for QueryTabsManager component instance
const autocompleteCache = ref({});

// Context Menu State
const contextMenu = reactive({
  visible: false,
  top: 0,
  left: 0,
  nodeData: null,
});


// Context Menu State (for Object Browser right-click)
// The actual menu DOM creation will be handled by a function, similar to index.vue.back

// API Function Mapping is just for reference, direct calls will be used.

async function fetchCurrentUser() {
  try {
    const response = await getCurrentUserAPI();
    currentUser.value = response.data || response; // Adjust based on actual API response structure
    userInfo.value = currentUser.value; // Pass to QueryTabsManager
    console.log("Current user fetched:", currentUser.value);
  } catch (error) {
    console.error("Failed to fetch current user:", error);
    ElMessage.error("获取当前用户信息失败，部分权限检查可能受影响");
    // Fallback to a non-admin user to prevent accidental privilege
    currentUser.value = { id: null, username: 'anonymous', is_superuser: false, is_staff: false };
    userInfo.value = currentUser.value;
  }
}

async function fetchDatabases(forceRefresh = false) {
  if (!forceRefresh && databases.value.length > 0) {
    return;
  }
  isRefreshingDatasource.value = true;
  mainLoading.value = true; // Indicate loading for datasources
  try {
    const response = await getDatasources(); // No specific permission param, will filter client-side if needed or rely on backend
    databases.value = response.data || response.results || response || [];
    
    // If not admin, and if we need to filter datasources client-side (based on index.vue.back logic)
    // This part would require fetching datasource specific permissions or user's global datasource access list.
    // For simplicity now, we assume `getDatasources()` returns a list already permissioned for the user
    // or that all users see all datasources and further ops are permission-checked.
    // If `getDatasources({ onlyAuthorized: true })` or similar exists and works, that's preferred.

    // 注释掉自动选择第一个数据库的逻辑，让用户必须手动选择
    /*
    if (databases.value.length > 0 && !selectedDatabase.value) {
      // 自动选择第一个数据库
      selectedDatabase.value = databases.value[0].id;
      console.log(`自动选择第一个数据库: ID=${selectedDatabase.value}, 名称=${databases.value[0].name}`);
      
      // 如果有活动标签，更新它的数据源
      if (activeTab.value) {
        // 更新活动标签的数据源
        activeTab.value.datasourceId = selectedDatabase.value;
        console.log(`更新活动标签的数据源: tabId=${activeTab.value.id}, datasourceId=${selectedDatabase.value}`);
        
        // 触发对象浏览器刷新
        forceRefreshObjectBrowser();
      }
    }
    */
    
    console.log(`[fetchDatabases] 获取到 ${databases.value.length} 个数据源，但不自动选择，等待用户手动选择`);
  } catch (error) {
    console.error("Failed to fetch databases:", error);
    ElMessage.error("获取数据源列表失败");
    databases.value = [];
  } finally {
    isRefreshingDatasource.value = false;
    mainLoading.value = false;
  }
}

async function fetchDatabasesAndPermissions(forceRefresh = false) {
  console.log('[fetchDatabasesAndPermissions] 开始刷新数据源列表...');
  databaseObjects.value = []; // 清空对象列表，显示加载状态
  schemas.value = []; // 清空模式列表
  
  try {
    // 刷新数据源列表
    await fetchDatabases(forceRefresh);
    
    // 如果当前有选择的数据源，重新加载其模式和对象
    if (selectedDatabase.value) {
      console.log(`[fetchDatabasesAndPermissions] 重新加载当前选择的数据源 ${selectedDatabase.value} 的模式和对象`);
      
      // 通过调用refreshObjectBrowser来重新加载模式和对象
      await refreshObjectBrowser();
    }
    
    console.log('[fetchDatabasesAndPermissions] 数据源刷新完成');
  } catch (error) {
    console.error('[fetchDatabasesAndPermissions] 刷新数据源时出错:', error);
    ElMessage.error('刷新数据源失败：' + (error.message || '未知错误'));
  }
}

// 添加重试加载schemas的函数
async function retryLoadSchemas(maxRetries = 3) {
  console.log(`[retryLoadSchemas] 开始尝试加载schemas，最多尝试 ${maxRetries} 次`);
  let retryCount = 0;
  let success = false;
  let lastError = null;
  
  while (!success && retryCount < maxRetries) {
    try {
      retryCount++;
      console.log(`[retryLoadSchemas] 第 ${retryCount}/${maxRetries} 次尝试加载schemas`);
      await loadSchemasForSelectedDB();
      
      // 如果schemas加载成功，标记成功
      if (schemas.value.length > 0) {
        success = true;
        console.log(`[retryLoadSchemas] 成功加载schemas，共 ${schemas.value.length} 个`);
      } else {
        // 如果加载完成但没有schemas，可能是真的没有schema，也算成功
        console.log(`[retryLoadSchemas] 加载完成，但没有schemas，可能数据源为空`);
        success = true;
      }
    } catch (error) {
      lastError = error;
      console.error(`[retryLoadSchemas] 第 ${retryCount} 次加载schemas失败:`, error);
      
      if (retryCount < maxRetries) {
        // 等待一段时间后重试，时间随重试次数增加
        const waitTime = retryCount * 1000;
        console.log(`[retryLoadSchemas] 等待 ${waitTime}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }
  
  if (!success) {
    console.error(`[retryLoadSchemas] 在 ${maxRetries} 次尝试后仍然失败:`, lastError);
    throw new Error(`加载schemas失败，请检查网络连接或数据源配置: ${lastError?.message || '未知错误'}`);
  }
  
  return schemas.value;
}

async function loadSchemasForSelectedDB() {
  console.log('[loadSchemasForSelectedDB] 开始加载数据源的schemas, 数据源ID:', selectedDatabase.value);
  schemas.value = []; // 清空当前的schemas
  selectedSchema.value = ''; // 清空当前选择的schema
  
  // 如果没有选择数据源，直接返回
  if (!selectedDatabase.value) {
    console.log('[loadSchemasForSelectedDB] 没有选择数据源，中止加载');
    return;
  }
  
  try {
    const response = await getSchemaList(selectedDatabase.value);
    console.log('[loadSchemasForSelectedDB] 获取到的schemas数据:', response);
    
    let schemaNames = [];
    
    // 处理各种可能的返回格式
    if (Array.isArray(response)) {
      // 如果直接返回数组，直接使用
      schemaNames = response;
    } else if (response && typeof response === 'object') {
      // 如果返回对象
      if (response.schemas && Array.isArray(response.schemas)) {
        // 如果有 schemas 字段且是数组，使用它
        schemaNames = response.schemas;
      } else if (response.data && Array.isArray(response.data)) {
        // 如果有 data 字段且是数组，使用它
        schemaNames = response.data;
      } else if (response.data && response.data.schemas && Array.isArray(response.data.schemas)) {
        // 如果在 data.schemas 中是数组，使用它
        schemaNames = response.data.schemas;
      } else {
        // 否则尝试使用对象的所有键，但排除特定的非 schema 键
        const keys = Object.keys(response);
        schemaNames = keys.filter(key => 
          !['success', 'code', 'message', 'msg', 'status', 'error'].includes(key.toLowerCase())
        );
      }
    }
    
    console.log('[loadSchemasForSelectedDB] 处理后的 schema 名称列表:', schemaNames);
    schemas.value = schemaNames.map(name => ({ label: name, value: name }));
    
    // 如果加载了 schemas 并且列表不为空，自动选择第一个
    if (schemas.value.length > 0 && !selectedSchema.value) {
      selectedSchema.value = schemas.value[0].value;
      console.log(`[loadSchemasForSelectedDB] 自动选择第一个 schema: ${selectedSchema.value}`);
      
      // 如果有活动标签，更新它的 schema
      if (activeTab.value) {
        activeTab.value.schema = selectedSchema.value;
        selectedSchema.value = schemas.value[0].value;
      }
      
      // 设置好 schema 后，立即加载该 schema 的数据库对象
      if (selectedSchema.value) {
        // 使用 nextTick 确保 schema 设置已生效
        nextTick(async () => {
          console.log(`[loadSchemasForSelectedDB] 加载 schema ${selectedSchema.value} 的对象`);
          try {
            await loadDatabaseObjectsForSelectedSchema();
          } catch (error) {
            console.error('[loadSchemasForSelectedDB] 加载对象失败:', error);
          }
        });
      }
    }
    
    // 更新 allSchemas 以便其他组件使用
    allSchemas.value = [...schemas.value];
    
    return schemas.value;
  } catch (error) {
    console.error('[loadSchemasForSelectedDB] 加载 schemas 失败:', error);
    ElMessage.error(`加载 schemas 失败: ${error.message || '未知错误'}`);
    schemas.value = [];
    return [];
  }
}

async function loadDatabaseObjectsForSelectedSchema(force = false) {
  console.log(`[loadDatabaseObjectsForSelectedSchema] Called. DB: ${selectedDatabase.value}, Schema: ${selectedSchema.value}, Force: ${force}`);
  
  // 首先检查是否有选择数据源，如果没有则清空对象列表并返回
  if (!selectedDatabase.value) {
    console.log('[loadDatabaseObjectsForSelectedSchema] 未选择数据源，清空对象列表');
    databaseObjects.value = [];
    return;
  }
  
  if (selectedSchema.value === null || selectedSchema.value === undefined) {
    databaseObjects.value = [];
    console.log('[loadDatabaseObjectsForSelectedSchema] Exiting: No Schema selected.');
    return;
  }

  const schemaForAPI = selectedSchema.value === '(默认Schema)' ? '' : selectedSchema.value;
  const cacheKey = `database_objects_${selectedDatabase.value}_${schemaForAPI}`;

  // 1. Check localStorage if not forcing refresh
  if (!force) {
    const cachedData = localStorage.getItem(cacheKey);
    if (cachedData) {
      try {
        console.log(`[loadDatabaseObjectsForSelectedSchema] Loading objects from localStorage for ${cacheKey}`);
        const parsed = JSON.parse(cachedData);
        databaseObjects.value = parsed;
        return; // Exit if loaded from cache
      } catch (e) {
        console.error('[loadDatabaseObjectsForSelectedSchema] Failed to parse cached database objects', e);
        localStorage.removeItem(cacheKey); // Clear corrupted cache
      }
    }
  }

  treeLoading.value = true;
  try {
    let userPermittedTablesInCurrentSchema = new Set();
    if (!isAdmin.value) {
      console.log('[loadDatabaseObjectsForSelectedSchema] User is not admin, fetching permissions...');
      const permissions = await refreshTablePermissions(selectedDatabase.value, 0, false);
      if (permissions && Array.isArray(permissions)) {
        permissions.forEach(perm => {
          const permSchema = perm.schema_name || '';
          const currentSelectedSchemaVal = selectedSchema.value === '(默认Schema)' ? '' : selectedSchema.value;
          if (perm.permission_type !== 'denied' && permSchema === currentSelectedSchemaVal) {
            userPermittedTablesInCurrentSchema.add(perm.table_name.toLowerCase());
          }
        });
      }
      console.log('[loadDatabaseObjectsForSelectedSchema] Permitted tables in current schema:', userPermittedTablesInCurrentSchema);
    }

    console.log('[loadDatabaseObjectsForSelectedSchema] Fetching database schema for API with schema:', schemaForAPI);
    
    const params = force ? { no_cache: true } : {};
    console.log(`[loadDatabaseObjectsForSelectedSchema] Calling getDatabaseSchema with params:`, params);
    const response = await getDatabaseSchema(selectedDatabase.value, schemaForAPI, params);
    console.log('[loadDatabaseObjectsForSelectedSchema] getDatabaseSchema API响应:', response);

    let schemaData = null;
    if (response && response.success === true && response.schema) {
      schemaData = response.schema;
    } else if (response && (response.tables || response.views)) {
      schemaData = response;
    }

    if (schemaData) {
      const tables = (schemaData.tables || []).map(t => ({
        label: (t.name || 'unknown_table') + (t.comment ? ` (${t.comment})` : ''),
        value: t.name || 'unknown_table',
        type: 'table',
        children: t.columns ? t.columns.map(c => ({
          label: `${c.name || 'unknown_column'} (${c.type || ''})` + (c.comment ? ` (${c.comment})` : ''),
          value: c.name || 'unknown_column',
          type: 'column',
          dataType: c.type,
          comment: c.comment,
          icon: 'DataLine'
        })) : [],
        comment: t.comment,
        icon: 'Grid',
        id: `table_${selectedSchema.value}_${t.name || 'unknown_table'}`
      }));

      const views = (schemaData.views || []).map(v => ({
        label: (v.name || 'unknown_view') + (v.comment ? ` (${v.comment})` : ''),
        value: v.name || 'unknown_view',
        type: 'view',
        children: v.columns ? v.columns.map(c => ({
          label: `${c.name || 'unknown_column'} (${c.type || ''})` + (c.comment ? ` (${c.comment})` : ''),
          value: c.name || 'unknown_column',
          type: 'column',
          dataType: c.type,
          comment: c.comment,
          icon: 'DataLine'
        })) : [],
        comment: v.comment,
        icon: 'View',
        id: `view_${selectedSchema.value}_${v.name || 'unknown_view'}`
      }));

      let combinedObjects = [...tables, ...views].sort((a, b) => a.label.localeCompare(b.label));

      if (!isAdmin.value) {
        combinedObjects = combinedObjects.filter(obj => userPermittedTablesInCurrentSchema.has(obj.value.toLowerCase()));
      }

      databaseObjects.value = combinedObjects;

      try {
        localStorage.setItem(cacheKey, JSON.stringify(combinedObjects));
        console.log(`[loadDatabaseObjectsForSelectedSchema] Saved ${combinedObjects.length} objects to localStorage cache for ${cacheKey}`);
      } catch (e) {
        console.error('[loadDatabaseObjectsForSelectedSchema] Failed to save database objects to cache', e);
      }

      if (combinedObjects.length === 0) {
        // ... (existing empty message logic)
      }
    } else {
      databaseObjects.value = [];
      if (response && response.success === false && response.message) {
         ElMessage.error(`获取对象失败: ${response.message}`);
      }
    }
  } catch (error) {
    console.error("[loadDatabaseObjectsForSelectedSchema] Error loading database objects:", error);
    ElMessage.error(`加载数据库对象失败: ${error.message || '未知错误'}`);
    databaseObjects.value = [];
  } finally {
    // 确保在所有情况下都重置加载状态
    treeLoading.value = false;
  }
}


// 修改 handleActiveTabContextChanged 中的相关部分
async function handleActiveTabContextChanged(context) {
  console.log(`[handleActiveTabContextChanged] 接收到的上下文:`, context);
  
  if (!context) {
    console.log('[handleActiveTabContextChanged] 接收到空上下文');
    activeTab.value = null;
    return;
  }

  activeTab.value = context;
  const forceRefresh = context._forceSchemaRefresh;
  
  // 如果数据源发生变化或强制刷新
  if (selectedDatabase.value !== context.datasourceId || forceRefresh) {
    console.log('[handleActiveTabContextChanged] 数据源变更或强制刷新:', 
      selectedDatabase.value, '->', context.datasourceId, 
      'forceRefresh:', forceRefresh);
    
    // 先更新选中的数据源
    selectedDatabase.value = context.datasourceId;
    
    // 清空当前的schemas和对象
    schemas.value = [];
    databaseObjects.value = [];
    
    if (context.datasourceId) {
      try {
        // 使用重试机制加载schemas
        console.log('[handleActiveTabContextChanged] 开始加载schemas（带重试机制）');
        await retryLoadSchemas();
        
        // 如果加载成功且有schemas
        if (schemas.value.length > 0) {
          // 更新全局schemas
          allSchemas.value = [...schemas.value];
          
          // 如果标签页没有选择schema，自动选择第一个
          if (!context.schema && schemas.value.length > 0) {
            console.log('[handleActiveTabContextChanged] 自动选择第一个schema:', schemas.value[0].value);
            if (queryTabsManagerRef.value) {
              const activeTab = queryTabsManagerRef.value.activeTab;
              if (activeTab) {
                activeTab.schema = schemas.value[0].value;
                selectedSchema.value = schemas.value[0].value;
              }
            }
          } else if (context.schema) {
            console.log('[handleActiveTabContextChanged] 使用标签页的schema:', context.schema);
            selectedSchema.value = context.schema;
          }
        }
      } catch (error) {
        console.error('[handleActiveTabContextChanged] 多次尝试加载schemas都失败:', error);
        ElMessage.error('多次尝试加载Schema列表失败，请刷新页面重试');
      }
    }
  } else if (selectedSchema.value !== context.schema) {
    // 数据源相同但schema变更
    console.log('[handleActiveTabContextChanged] Schema变更:', selectedSchema.value, '->', context.schema);
    selectedSchema.value = context.schema || '';
  }
}

async function handleExecuteQuery(tab) {
  console.log('[index.vue handleExecuteQuery] 接收到执行查询请求:', tab);
  
  if (!tab) {
    console.error("[index.vue handleExecuteQuery] Invalid tabData for query execution: tabQueryData is null or undefined");
    return;
  }
  
  if (!tab.tabId) {
    // 尝试使用id字段作为tabId
    if (tab.id) {
      tab.tabId = tab.id;
      console.log("[index.vue handleExecuteQuery] Using tab.id as tabId:", tab.id);
    } else {
      console.error("[index.vue handleExecuteQuery] Invalid tabData for query execution: missing tabId");
      return;
    }
  }
            
  // 从tab对象中提取所需参数，支持新旧字段名
  const tabId = tab.tabId;
  const sql = tab.sql || tab.sql_content;
  const datasourceId = tab.datasource_id || tab.datasourceId;
  const schema = tab.schema;
  const page = tab.page || 1;
  const pageSize = tab.page_size || tab.pageSize || 100;
  
  console.log(`[handleExecuteQuery] 执行查询请求: tabId=${tabId}, datasourceId=${datasourceId}, schema=${schema}, page=${page}, pageSize=${pageSize}`);

  if (!datasourceId) {
    ElMessage.error('请先为当前查询标签页选择一个数据源');
    queryTabsManagerRef.value?.updateTabQueryState(tabId, {
      loading: false,
      error: '未选择数据源',
    });
    return;
  }
  
  if (!sql?.trim()) {
    ElMessage.error('请输入SQL查询语句');
    queryTabsManagerRef.value?.updateTabQueryState(tabId, {
      loading: false,
      error: 'SQL语句为空',
    });
    return;
  }
            
  // 初始化标签页状态，设置为加载中
  queryTabsManagerRef.value?.updateTabQueryState(tabId, {
    loading: true,
    error: null,
    results: [],
    columns: [],
    executed: false,
    isExecuting: true, // 添加执行中状态标记
    messages: null // 清空之前的消息
  });

  try {
    // 确保用户已登录
    if (!currentUser.value || currentUser.value.id === null) {
        await fetchCurrentUser();
        if(!currentUser.value || currentUser.value.id === null) {
            throw new Error("无法获取用户信息，请重新登录后再试。");
        }
    }
    
    // 处理SQL语句，确保分号不会导致问题
    let sqlToExecute = sql;
    
    // 构建API请求参数，确保字段名与后端API一致
    const apiRequestData = {
      datasource_id: datasourceId,
      sql_content: sqlToExecute,
      schema: schema,
      page: page,
      page_size: pageSize,
    };

    console.log(`[handleExecuteQuery] 发送API请求: datasource_id=${datasourceId}, schema=${schema}, page=${page}, page_size=${pageSize}, sql_content=${sqlToExecute.substring(0, 50)}...`);
    
    // 确保SQL语句末尾的分号不会导致问题
    if (apiRequestData.sql_content && typeof apiRequestData.sql_content === 'string') {
      // 不需要删除SQL末尾的分号，后端会处理
      console.log(`[handleExecuteQuery] SQL语句长度: ${apiRequestData.sql_content.length}`);
    }
    
    const response = await executeQueryAPI(apiRequestData);
    
    if (response.success === false) {
      console.log(`[handleExecuteQuery] 查询执行失败: ${response.error_message || response.error || '未知错误'}`);
      
      const errorMessage = response.error_message || response.error || '查询执行失败，请检查SQL或联系管理员';
      
      // 发送错误结果事件
      document.dispatchEvent(new CustomEvent('query-result', { 
        detail: {
          tabId,
          error: errorMessage,
        }
      }));
      
      // 更新标签页状态
      queryTabsManagerRef.value?.updateTabQueryState(tabId, {
        loading: false,
        error: errorMessage,
        results: [],
        columns: [],
        executed: true,
        isExecuting: false // 清除执行中状态
      });
    } else {
      // 新增：统一处理成功响应，分发所有字段
      const queryResultData = response.data || response;
      // 检查是否是选中SQL执行（使用传递过来的 selectedSql 字段）
      const isSelectedSqlExecution = tab.selectedSql || false;
      
      document.dispatchEvent(new CustomEvent('query-result', { 
        detail: {
          tabId,
          results: queryResultData.rows || [],
          columns: queryResultData.columns || [],
          rowCount: queryResultData.row_count !== undefined ? queryResultData.row_count : (queryResultData.rows?.length || 0),
          dbExecutionTime: queryResultData.db_execution_time,
          totalTime: queryResultData.total_time,
          messages: queryResultData.message || '',
          error: null,
          // 新增多结果集字段
          is_multi_statement: response.is_multi_statement,
          all_results: response.all_results,
          // 新增：选中SQL执行标记和实际执行的SQL
          selectedSql: isSelectedSqlExecution,
          sql: sqlToExecute
        }
      }));
      
      // 检查是否为DDL或DML语句（如CREATE TABLE, ALTER TABLE, UPDATE, INSERT, DELETE等）
      const sqlLower = sql.trim().toLowerCase();
      const isDDL = sqlLower.startsWith('create ') ||
                   sqlLower.startsWith('alter ') ||
                   sqlLower.startsWith('drop ') ||
                   sqlLower.startsWith('truncate ') ||
                   sqlLower.startsWith('rename ');

      // 检查是否为DML语句（UPDATE, INSERT, DELETE）
      const isDML = sqlLower.startsWith('update ') ||
                   sqlLower.startsWith('insert ') ||
                   sqlLower.startsWith('delete ');
      
      // 如果是DDL语句，总是显示成功消息，无论是否有返回结果
      if (isDDL) {
        ElMessage.success('SQL执行成功！');
        
        // 刷新对象浏览器，以便显示新创建/修改的对象
        if (sqlLower.includes('table') || sqlLower.includes('view')) {
          nextTick(async () => {
            isProgrammaticRefresh.value = true;
            
            const currentTab = queryTabsManagerRef.value?.activeTab;
            const ddlSchema = currentTab?.schema;

            // If the DDL was run in a schema different from the currently displayed one
            if (ddlSchema && ddlSchema !== selectedSchema.value) {
              // This assignment would normally trigger the watcher, but the flag will prevent it.
              selectedSchema.value = ddlSchema;
            }
            
            // Now, force a refresh for the (now correct) schema.
            await forceRefreshObjectBrowser();
            
            // Allow the watcher to work normally again after the refresh is complete.
            isProgrammaticRefresh.value = false;
          });
        }
      }
      
      // 直接使用后端返回的 rows 和 columns，不再做额外处理
      const resultData = {
        tabId,
        results: queryResultData.rows || [],
        columns: queryResultData.columns || [],
        rowCount: queryResultData.row_count !== undefined ? queryResultData.row_count : (queryResultData.rows?.length || 0),
        dbExecutionTime: queryResultData.db_execution_time,
        totalTime: queryResultData.total_time,
        messages: queryResultData.message || (isDDL || isDML ? '执行成功' : ''),
        error: null
      };
      
      console.log(`[handleExecuteQuery] 准备更新UI，列:`, resultData.columns, `行数:`, resultData.results.length);
      if (resultData.results.length > 0) {
        console.log(`[handleExecuteQuery] 第一行数据:`, resultData.results[0]);
      }
      
      // 更新标签页状态
      queryTabsManagerRef.value?.updateTabQueryState(tabId, {
        ...resultData,
        executed: true,
        loading: false,
        isExecuting: false // 清除执行中状态
      });
    }

  } catch (error) {
    console.error("[handleExecuteQuery] 执行查询时出错:", error);
    const errorMessage = error.message || '执行查询时发生未知网络或客户端错误';
    
    // 发送错误结果事件
    document.dispatchEvent(new CustomEvent('query-result', { 
      detail: {
        tabId,
        error: errorMessage,
      }
    }));
    
    // 更新标签页状态
    queryTabsManagerRef.value?.updateTabQueryState(tabId, {
      loading: false,
      error: errorMessage,
      results: [],
      columns: [],
      executed: true,
      isExecuting: false // 清除执行中状态
    });
    
    ElMessage.error(errorMessage);
  }
}


// Placeholder for functions to be implemented
async function fetchDatabasesAndCurrentUser() {}
// async function loadSchemasForSelectedDB() {}
// async function loadDatabaseObjectsForSelectedSchema() {}
function handleSaveQueryRequest(queryDetailsToSave) {
  console.log('[handleSaveQueryRequest] 保存查询请求:', queryDetailsToSave);
  
  if (!queryDetailsToSave) {
    ElMessage.warning('无效的查询数据');
    return;
  }

  // 设置保存对话框数据
  queryDataForSaveDialog.value = {
    sql: queryDetailsToSave.sql,
    datasourceId: queryDetailsToSave.datasourceId,
    schema: queryDetailsToSave.schema
  };
  
  // 显示保存对话框
  saveQueryDialogVisible.value = true;
}

// ... handleOpenSaveQueryDialog, handleConfirmSaveQuery, etc. as previously defined ...
// ... handleShowSavedQueriesList, handleImportSavedQuery, handleDeleteSavedQuery ...
// ... handleNodeClickInObjectBrowser, handleNodeRightClickInObjectBrowser, showContextMenuForObjectBrowser ...
// ... viewTableStructure, viewTableIndexes, viewTableDDL ...
// ... handleShowCellContent, handleTableInfoSubDialogClose ...

// 不再使用这个函数处理双击事件，避免重复处理
function handleCellDblClick(payload) {
  // 仅记录事件，但不再处理
  console.log('Cell double-click event received, but skipped to avoid duplication:', payload);
  // 不再调用 handleShowCellContent(payload);
}

// Add dummy handlers for events from QueryToolbar and QueryTabsManager for now
// if they are not yet fully implemented, to prevent Vue warnings.
// Actual implementations are above or in previous steps.

// 添加一个强制刷新对象浏览器的函数
async function forceRefreshObjectBrowser() {
  if (selectedDatabase.value) {
    // Call the unified loading function with force=true
    await loadDatabaseObjectsForSelectedSchema(true);
  } else {
    ElMessage.warning('请先选择一个数据源进行刷新。');
  }
}

// 添加一个强制刷新对象浏览器的函数
async function refreshObjectBrowser() {
  console.log('[refreshObjectBrowser] 强制刷新对象浏览器...');
  databaseObjects.value = [];
  treeLoading.value = true;
  
  try {
    // 如果没有选择数据源或模式，直接返回
    if (!selectedDatabase.value) {
      console.log('[refreshObjectBrowser] 没有选择数据源，不刷新');
      treeLoading.value = false;
      return;
    }
    
    // 先获取模式列表
    if (schemas.value.length === 0) {
      console.log('[refreshObjectBrowser] 加载模式列表...');
      await loadSchemasForSelectedDB();
    }
    
    // 如果有选择模式，加载对象
    if (selectedSchema.value) {
      console.log('[refreshObjectBrowser] 加载数据库对象...');
      await loadDatabaseObjectsForSelectedSchema();
    } else if (schemas.value.length > 0) {
      // 如果没有选择模式但有模式列表，选择第一个模式
      console.log('[refreshObjectBrowser] 自动选择第一个模式:', schemas.value[0].value);
      selectedSchema.value = schemas.value[0].value;
      // 会自动触发 watch(selectedSchema) 加载对象
    }
  } catch (error) {
    console.error('[refreshObjectBrowser] 刷新对象浏览器时出错:', error);
    ElMessage.error('刷新对象浏览器失败：' + (error.message || '未知错误'));
  } finally {
    treeLoading.value = false;
    console.log('[refreshObjectBrowser] 刷新完成');
  }
}

// 添加一个确保activeTabDatasourceId与selectedDatabase同步的watcher
watch(selectedDatabase, (newDbId) => {
  console.log(`[watch selectedDatabase 同步] 数据源变更: ${newDbId}`);
  if (activeTab.value) {
    activeTab.value.datasourceId = newDbId;
  }
});

// 修改handleTabDatabaseChanged函数，在数据库变更时强制刷新对象浏览器
/*
function handleTabDatabaseChanged(payload) { 
  console.log(`[handleTabDatabaseChanged IGNORED] 原始 payload:`, payload);
  // This logic is now handled by QueryTabsManager internally, 
  // and updates flow via 'active-tab-changed' event.
}

function handleTabSchemaChanged(tabData, unusedParam) {
  console.log(`[handleTabSchemaChanged IGNORED] 原始参数 - tabData类型:${typeof tabData}, 值:`, tabData, `unusedParam类型:${typeof unusedParam}, 值:`, unusedParam);
  // This logic is now handled by QueryTabsManager internally, 
  // and updates flow via 'active-tab-changed' event.
}
*/

// 添加一个强制重新加载当前Schema对象的函数
async function forceReloadCurrentSchemaObjects() {
  console.log('[forceReloadCurrentSchemaObjects] 强制重新加载当前Schema对象');
  if (!selectedDatabase.value || !selectedSchema.value) {
    console.log('[forceReloadCurrentSchemaObjects] 没有选择数据源或Schema，不执行加载');
    return;
  }
  
  databaseObjects.value = []; // 清空当前对象列表
  treeLoading.value = true; // 设置加载状态
  
  try {
    console.log(`[forceReloadCurrentSchemaObjects] 重新加载数据源 ${selectedDatabase.value} Schema ${selectedSchema.value} 的对象`);
    await loadDatabaseObjectsForSelectedSchema();
    console.log('[forceReloadCurrentSchemaObjects] 重新加载完成');
  } catch (error) {
    console.error('[forceReloadCurrentSchemaObjects] 重新加载对象失败:', error);
    ElMessage.error('重新加载对象失败：' + (error.message || '未知错误'));
  } finally {
    treeLoading.value = false;
  }
}

// 在handleTabSchemaChanged函数中，替换refreshObjectBrowser调用为forceReloadCurrentSchemaObjects
/*
function handleTabSchemaChanged(tabData, unusedParam) {
  console.log(`[handleTabSchemaChanged IGNORED] 原始参数 - tabData类型:${typeof tabData}, 值:`, tabData, `unusedParam类型:${typeof unusedParam}, 值:`, unusedParam);
  // This logic is now handled by QueryTabsManager internally, 
  // and updates flow via 'active-tab-changed' event.
}
*/


function handleDeleteRow(payload) {
  const { datasourceId, database, tableName, row, index } = payload;
  console.log('删除行请求:', payload);

  // 没有正确的条件不允许删除
  if (!row || Object.keys(row).length === 0) {
    ElMessage.error('删除行失败: 没有指定行条件');
    return;
  }

  // 显示确认对话框
  ElMessageBox.confirm(
    `确定要删除这行数据吗？此操作将无法恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    // 用户确认删除，显示加载状态
    const loading = ElLoading.service({
      lock: true,
      text: '正在删除...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    try {
      // 导入deleteTableRow函数
      const { deleteTableRow } = await import('@/api/database');
      
      // 调用删除API
      await deleteTableRow({
        datasourceId,
        tableName,
        database,
        row
      });
      
      // 删除成功，刷新活动标签页中的查询结果
      if (queryTabsManagerRef.value) {
        const activeTab = queryTabsManagerRef.value.activeTab;
        if (activeTab && activeTab.type === 'query') {
          // 让活动标签页重新执行查询以刷新数据
          await activeTab.executeQuery();
        }
      }
      
      ElMessage.success('数据已成功删除');
    } catch (error) {
      console.error('删除数据行失败:', error);
      ElMessage.error(error.message || '删除失败，请重试');
    } finally {
      loading.close();
    }
  }).catch(() => {
    // 用户取消删除
    ElMessage.info('已取消删除');
  });
}

// ... existing function definitions for handleOpenSaveQueryDialog, handleConfirmSaveQuery ...
// ... handleShowSavedQueriesList, handleImportSavedQuery, handleDeleteSavedQuery ...
// ... handleNodeClickInObjectBrowser, handleNodeRightClickInObjectBrowser, showContextMenuForObjectBrowser ...
// ... viewTableStructure, viewTableIndexes, viewTableDDL ...
// ... handleShowCellContent, handleTableInfoSubDialogClose ...

// Watchers for Object Browser context changes
watch(selectedDatabase, async (newDbId, oldDbId) => {
  console.log(`[watch selectedDatabase] 数据源变更: ${oldDbId} -> ${newDbId}`);
  
  // 先清空 schema 相关数据，确保在数据库切换时刷新
  schemas.value = [];
  databaseObjects.value = [];
  selectedSchema.value = ''; // 重要：确保先清空 selectedSchema
  
  if (newDbId) {
    console.log(`[watch selectedDatabase] 为数据源 ${newDbId} 加载 schemas`);
    treeLoading.value = true;
    
    try {
      await retryLoadSchemas();
      console.log(`[watch selectedDatabase] 数据源 ${newDbId} 的 schemas 加载完成，共加载 ${schemas.value.length} 个 schemas`);
      
      // 重要：同步更新 allSchemas，确保查询标签页能获取到最新的 schemas
      allSchemas.value = [...schemas.value];
      console.log(`[watch selectedDatabase] 更新 allSchemas，共 ${allSchemas.value.length} 个`);
      
      // 如果 schemas 加载完成后没有自动设置 selectedSchema，且 schemas 列表非空，手动设置第一个
      if (!selectedSchema.value && schemas.value.length > 0) {
        console.log(`[watch selectedDatabase] 手动设置 selectedSchema 为第一个: ${schemas.value[0].value}`);
        selectedSchema.value = schemas.value[0].value;
      }
    } catch (err) {
      console.error(`[watch selectedDatabase] 加载 schemas 时出错:`, err);
      ElMessage.error(`为数据源 ${newDbId} 加载 schemas 失败`);
    } finally {
      treeLoading.value = false;
    }
  } else {
    // 如果数据源被设置为null或undefined，确保清空对象列表
    console.log('[watch selectedDatabase] 数据源被清空，清空对象列表');
    databaseObjects.value = [];
    schemas.value = [];
    selectedSchema.value = '';
  }
});

watch(selectedSchema, async (newSchema, oldSchema) => {
  // If the schema is being changed programmatically as part of a DDL refresh,
  // do not trigger a standard (cached) load. The DDL handler will trigger a forced refresh.
  if (isProgrammaticRefresh.value) {
    console.log('[watch selectedSchema] Skipped loading because a programmatic refresh is in progress.');
    return;
  }

  console.log(`[watch selectedSchema] Schema变更: ${oldSchema} -> ${newSchema}`);
  if (selectedDatabase.value && newSchema !== undefined) {
    treeLoading.value = true;
    try {
      await loadDatabaseObjectsForSelectedSchema(false);
    } catch (error) {
      console.error('[watch selectedSchema] 加载对象失败:', error);
      ElMessage.error('加载数据库对象失败，请尝试刷新页面');
    } finally {
      treeLoading.value = false;
    }
  }
});

// 定义beforeunload事件处理函数，以便可以在多处引用
const handleBeforeUnload = (event) => {
  console.log('[beforeunload] 页面即将刷新或关闭，执行资源清理');
  try {
    cleanupBeforeNavigation();
    // 如果需要让用户确认离开，可以取消下面的注释
    // event.preventDefault();
    // event.returnValue = '';
  } catch (e) {
    console.error('[beforeunload] 清理资源时出错:', e);
  }
};

onMounted(async () => {
  mainLoading.value = true;
  
  // 确保初始化时清空数据库对象
  databaseObjects.value = [];
  
  try {
    // 首先获取当前用户
    await fetchCurrentUser(); 
    
    // 获取所有可用数据源
    await fetchDatabases();
    
    // 等待QueryTabsManager组件加载完毕（可能会从localStorage恢复标签页）
    await nextTick();
    
    // 如果有活动标签，确保对象浏览器与其同步
    if (queryTabsManagerRef.value?.activeTab) {
      const activeTab = queryTabsManagerRef.value.activeTab;
      console.log('[onMounted] 检测到活动标签:', activeTab.id);
      
      // 手动触发一次活动标签变更处理
      handleActiveTabChange(activeTab);
    }
    
    // 只注册全局刷新方法，不注册调试函数
    window.forceRefreshObjectBrowser = forceRefreshObjectBrowser;
    
    // 添加 beforeunload 事件监听器，确保在页面刷新或关闭时清理资源
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    // 如果页面加载后对象浏览器仍然为空，但有selectedDatabase和selectedSchema，强制刷新
    setTimeout(async () => {
      if (databaseObjects.value.length === 0 && selectedDatabase.value && selectedSchema.value) {
        console.log('[onMounted] 页面加载后对象浏览器为空，强制刷新...');
        
        // 添加重试逻辑
        let retryCount = 0;
        const maxRetries = 2;
        
        const attemptRefresh = async () => {
          try {
            await forceRefreshObjectBrowser();
            if (databaseObjects.value.length > 0) {
              console.log('[onMounted] 强制刷新成功，对象浏览器已加载数据');
            } else if (retryCount < maxRetries) {
              retryCount++;
              console.log(`[onMounted] 强制刷新后仍然没有数据，尝试再次刷新 (${retryCount}/${maxRetries})...`);
              setTimeout(attemptRefresh, 1000 * retryCount); // 增加延迟
            } else {
              console.log('[onMounted] 达到最大重试次数，仍然没有加载到对象数据');
              ElMessage.warning('无法加载数据库对象，请尝试手动刷新或检查Schema是否为空');
            }
          } catch (refreshError) {
            console.error('[onMounted] 强制刷新时出错:', refreshError);
            if (retryCount < maxRetries) {
              retryCount++;
              console.log(`[onMounted] 刷新失败，尝试再次刷新 (${retryCount}/${maxRetries})...`);
              setTimeout(attemptRefresh, 1000 * retryCount);
            } else {
              ElMessage.error('多次尝试加载对象浏览器失败，请刷新页面或联系管理员');
            }
          }
        };
        
        attemptRefresh();
      }
    }, 1000); // 延迟1秒执行，确保页面其他部分已加载完成
  } catch (error) {
    console.error('[onMounted] 初始化时出错:', error);
    
    // 特别处理循环引用JSON错误
    if (error instanceof TypeError && error.message && 
        (error.message.includes('circular structure to JSON') || 
         error.message.includes('Converting circular'))) {
      console.error('[onMounted] 检测到循环引用JSON错误。这通常是由于尝试序列化包含自引用的对象导致。');
      ElMessage.error('初始化失败：检测到对象循环引用问题，请尝试刷新页面。');
    } else {
      ElMessage.error('初始化失败：' + (error.message || '未知错误'));
    }
  } finally {
    mainLoading.value = false;
  }

  // 添加路由变化监听器
  console.log('[onMounted] 添加路由变化监听器');
  router.beforeEach((to, from, next) => {
    // 如果当前路由是SQL查询页面，且将要离开
    if (from.path.includes('/query') && !to.path.includes('/query')) {
      console.log('[router.beforeEach] 即将离开SQL查询页面，执行清理...');
      try {
        cleanupBeforeNavigation();
        console.log('[router.beforeEach] 离开SQL查询页面前清理完成');
      } catch (e) {
        console.error('[router.beforeEach] 离开SQL查询页面前清理失败:', e);
      }
    }
    next();
  });
});

// 全局清理函数
function cleanupBeforeNavigation() {
  console.log('[QueryIndex] 执行导航前清理');
  
  try {
    // 1. 只清除与SQL查询页面相关的事件监听器
    try {
      console.log('[QueryIndex] 清理SQL查询相关事件监听器');
      
      // 只移除确定是本页面添加的事件监听器
      if (typeof closeContextMenu === 'function') {
        document.removeEventListener('click', closeContextMenu);
        console.log(`[QueryIndex] 已移除SQL查询页面的click事件监听器`);
      }
      
      // 不再移除全局window事件监听器
      // window.removeEventListener('resize', null);
      // window.removeEventListener('beforeunload', null);
    } catch (listenerError) {
      console.error('[QueryIndex] 清理事件监听器时出错:', listenerError);
    }
    
    // 2. 移除本页面添加的DOM元素
    try {
      console.log('[QueryIndex] 清理SQL查询页面DOM元素');
      const existingMenu = document.getElementById('custom-obj-browser-ctx-menu');
      if (existingMenu) {
        existingMenu.remove();
        console.log('[QueryIndex] 已移除上下文菜单DOM元素');
      }
    } catch (domError) {
      console.error('[QueryIndex] 清理DOM元素时出错:', domError);
    }
    
    // 3. 重置本组件的状态变量
    try {
      console.log('[QueryIndex] 重置SQL查询页面组件状态');
      
      // 关闭所有打开的对话框
      saveQueryDialogVisible.value = false;
      savedQueriesListDialogVisible.value = false;
      tableStructureDialogVisible.value = false;
      tableIndexesDialogVisible.value = false;
      tableDDLDialogVisible.value = false;
      editTableStructureDialogVisible.value = false;
      cellOperationsDialogVisible.value = false;
      if (contextMenu) {
        contextMenu.visible = false;
        contextMenu.nodeData = null;
      }
    } catch (stateError) {
      console.error('[QueryIndex] 重置组件状态时出错:', stateError);
    }
    
    // 4. 不再清理全局缓存和引用，以免影响其他页面
    /*
    try {
      console.log('[QueryIndex] 清理全局缓存和引用');
      
      // 清除对象浏览器的全局引用
      if (window.forceRefreshObjectBrowser) {
        window.forceRefreshObjectBrowser = undefined;
        console.log('[QueryIndex] 已清除 forceRefreshObjectBrowser 全局引用');
      }
      
      // 清理任何临时存储
      localStorage.removeItem('temp_sql_query_state');
    } catch (cacheError) {
      console.error('[QueryIndex] 清理全局缓存时出错:', cacheError);
    }
    */
    
    console.log('[QueryIndex] 导航前清理完成');
    return true;
  } catch (error) {
    console.error('[QueryIndex] 导航前清理出错:', error);
    // 即使出错也继续执行，尽量清理
    return false;
  }
}

onBeforeUnmount(() => {
  console.log('[QueryIndex] 组件即将卸载，执行清理操作');
  
  try {
    // 移除全局事件监听器
    window.removeEventListener('beforeunload', handleBeforeUnload);
    console.log('[QueryIndex] 已移除 beforeunload 事件监听器');
    
    // 调用全局清理函数
    cleanupBeforeNavigation();
    
    // 额外的清理操作 - 重置所有状态变量
    console.log('[QueryIndex] 重置所有状态变量');
    selectedDatabase.value = null;
    selectedSchema.value = '';
    databaseObjects.value = [];
    schemas.value = [];
    activeTab.value = null;
    allSchemas.value = [];
    extensions.value = [];
    userInfo.value = {};
    savedQueries.value = [];
    
    // 清理组件引用
    console.log('[QueryIndex] 清理组件引用');
    if (queryTabsManagerRef.value) {
      // 确保先调用组件内部的清理方法
      if (typeof queryTabsManagerRef.value.clearAllTabs === 'function') {
        try {
          queryTabsManagerRef.value.clearAllTabs();
          console.log('[QueryIndex] 已调用 queryTabsManagerRef.clearAllTabs()');
        } catch (e) {
          console.warn('[QueryIndex] 调用 clearAllTabs 失败:', e);
        }
      }
      queryTabsManagerRef.value = null;
    }
    
    console.log('[QueryIndex] 清理操作完成');
  } catch (error) {
    console.error('[QueryIndex] 清理操作出错:', error);
    // 失败后尝试进行最基本的清理
    try {
      console.log('[QueryIndex] 尝试最基本的清理');
      document.removeEventListener('click', closeContextMenu);
      contextMenu.visible = false;
      contextMenu.nodeData = null;
    } catch (e) {
      console.error('[QueryIndex] 最基本清理也失败:', e);
    }
  }
});

// 添加这个函数来处理活动标签变化事件
function handleActiveTabChange(payload) {
  console.log('[QueryIndex] Active tab changed:', payload);
  if (payload) {
    // 确保我们存储的是一个干净的、非响应式的对象
    activeTab.value = JSON.parse(JSON.stringify(payload));
  } else {
    activeTab.value = null;
  }
}

// 修改 handleOpenSaveQueryDialog 方法
function handleOpenSaveQueryDialog(queryData) {
  queryDataForSaveDialog.value = queryData;
  saveQueryDialogVisible.value = true;
}

// 修改 handleShowSavedQueriesList 方法
async function handleShowSavedQueriesList() {
  savedQueriesListDialogVisible.value = true;
  savedQueriesLoading.value = true;
  try {
    const response = await getSavedQueries();
    console.log('获取已保存查询列表响应:', response);
    
    // 检查响应格式，确保正确处理
    if (Array.isArray(response)) {
      // 如果直接返回数组，直接使用
      savedQueries.value = response;
    } else if (response.data) {
      // 如果返回对象中包含data字段，使用data字段
      savedQueries.value = response.data;
    } else {
      // 如果都不是，可能是空数据或其他格式
      console.warn('获取已保存查询列表返回格式异常:', response);
      savedQueries.value = [];
    }
  } catch (error) {
    console.error('获取已保存查询列表失败:', error);
    ElMessage.error('获取已保存查询列表失败：' + (error.message || '未知错误'));
    savedQueries.value = []; // 确保在出错时设置为空数组
  } finally {
    savedQueriesLoading.value = false;
  }
}

function handleNodeRightClickInObjectBrowser({ node, event }) {
  // 仅为表和视图显示上下文菜单
  if (node.data.type === 'table' || node.data.type === 'view') {
    event.preventDefault(); // 阻止默认的浏览器右键菜单
    contextMenu.visible = true;
    contextMenu.top = event.clientY;
    contextMenu.left = event.clientX;
    contextMenu.nodeData = node.data;

    // 添加点击其他地方关闭菜单的监听器
    document.addEventListener('click', closeContextMenu, { once: true });
  }
}

function closeContextMenu() {
  contextMenu.visible = false;
  contextMenu.nodeData = null;
}

async function viewTableStructure() {
  if (!contextMenu.nodeData) return;
  const { value: tableName } = contextMenu.nodeData;
  console.log(`[viewTableStructure] for table: ${tableName}`);

  currentTableForInfo.value = {
    name: tableName,
    schema: selectedSchema.value,
    datasourceId: selectedDatabase.value,
  };

  tableInfoLoading.value = true;
  tableStructureDialogVisible.value = true;
  try {
    // 获取当前数据源信息，以确定数据源类型
    const datasource = databases.value.find(db => db.id === selectedDatabase.value);
    const datasourceType = datasource?.type?.toLowerCase() || '';
    console.log(`[viewTableStructure] 数据源类型: ${datasourceType}`);
    
    const response = await getTableStructureAPI(
      selectedDatabase.value,
      tableName,
      { 
        schema: selectedSchema.value,
        datasourceType: datasourceType // 添加数据源类型参数
      }
    );
    tableStructureData.value = response;
  } catch (error) {
    console.error('获取表结构失败:', error);
    ElMessage.error(`获取表结构失败: ${error.message}`);
    tableStructureDialogVisible.value = false; // 出错时关闭对话框
  } finally {
    tableInfoLoading.value = false;
  }
}

async function viewTableIndexes() {
  if (!contextMenu.nodeData) return;
  const { value: tableName } = contextMenu.nodeData;
  console.log(`[viewTableIndexes] for table: ${tableName}`);
  
  currentTableForInfo.value = {
    name: tableName,
    schema: selectedSchema.value,
    datasourceId: selectedDatabase.value,
  };

  tableInfoLoading.value = true;
  tableIndexesDialogVisible.value = true;
  try {
    // 获取当前数据源信息，以确定数据源类型
    const datasource = databases.value.find(db => db.id === selectedDatabase.value);
    const datasourceType = datasource?.type?.toLowerCase() || '';
    console.log(`[viewTableIndexes] 数据源类型: ${datasourceType}`);
    
    const response = await getTableIndexesAPI(
      selectedDatabase.value,
      tableName,
      { 
        schema: selectedSchema.value,
        datasourceType: datasourceType // 添加数据源类型参数
      }
    );
    tableIndexesData.value = response;
  } catch (error) {
    console.error('获取表索引失败:', error);
    ElMessage.error(`获取表索引失败: ${error.message}`);
    tableIndexesDialogVisible.value = false; // 出错时关闭对话框
  } finally {
    tableInfoLoading.value = false;
  }
}

async function viewTableDDL() {
  if (!contextMenu.nodeData) return;
  const { value: tableName } = contextMenu.nodeData;
  console.log(`[viewTableDDL] for table: ${tableName}`);

  currentTableForInfo.value = {
    name: tableName,
    schema: selectedSchema.value,
    datasourceId: selectedDatabase.value,
  };

  tableInfoLoading.value = true;
  tableDDLDialogVisible.value = true;
  try {
    // 获取当前数据源信息，以确定数据源类型
    const datasource = databases.value.find(db => db.id === selectedDatabase.value);
    const datasourceType = datasource?.type?.toLowerCase() || '';
    console.log(`[viewTableDDL] 数据源类型: ${datasourceType}`);
    
    // 假设 getTableDDLAPI 存在
    const response = await getTableDDLAPI(
      selectedDatabase.value,
      tableName,
      { 
        schema: selectedSchema.value,
        datasourceType: datasourceType // 添加数据源类型参数
      }
    );
    tableDDLData.value = response.ddl || response;
  } catch (error) {
    console.error('获取建表语句失败:', error);
    ElMessage.error(`获取建表语句失败: ${error.message}. (注意: API可能尚未实现)`);
    tableDDLDialogVisible.value = false; // 出错时关闭对话框
  } finally {
    tableInfoLoading.value = false;
  }
}

async function editTableStructure() {
  if (!contextMenu.nodeData) return;
  const { value: tableName } = contextMenu.nodeData;
  
  currentTableForInfo.value = {
    name: tableName,
    schema: selectedSchema.value,
    datasourceId: selectedDatabase.value,
  };
  
  tableInfoLoading.value = true;
  editTableStructureDialogVisible.value = true; // Show dialog with loading state
  try {
    // 获取当前数据源信息，以确定数据源类型
    const datasource = databases.value.find(db => db.id === selectedDatabase.value);
    const datasourceType = datasource?.type?.toLowerCase() || '';
    console.log(`[editTableStructure] 数据源类型: ${datasourceType}`);
    
    const response = await getTableStructureAPI(
      selectedDatabase.value,
      tableName,
      { 
        schema: selectedSchema.value,
        datasourceType: datasourceType // 添加数据源类型参数
      }
    );
    tableStructureData.value = response;
  } catch (error) {
    console.error('获取表结构失败 (for editing):', error);
    ElMessage.error(`获取表结构以进行编辑失败: ${error.message}`);
    editTableStructureDialogVisible.value = false; // Close dialog on error
  } finally {
    tableInfoLoading.value = false;
  }
}

async function handleApplyAlterSql(sql) {
  editTableStructureDialogVisible.value = false;
  
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在应用更改...',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  try {
    await executeDdlAPI({
      datasource_id: currentTableForInfo.value.datasourceId,
      schema: currentTableForInfo.value.schema,
      sql_content: sql,
    });
    
    ElMessage.success('表结构更新成功！');
    
    // Refresh the object browser to reflect the changes
    await forceReloadCurrentSchemaObjects();
    
  } catch (error) {
    console.error('应用表结构更改失败:', error);
    ElMessage.error(`应用更改失败: ${error.response?.data?.error || error.message || '未知错误'}`);
  } finally {
    loadingInstance.close();
  }
}

function handleShowCellContent(payload) {
  cellContentToShow.value = payload.content;
  cellContentTitle.value = payload.title;
  cellOperationsDialogVisible.value = true;
}

function handleTableInfoSubDialogClose(dialogType) {
  console.log(`[handleTableInfoSubDialogClose] Closing dialog of type: ${dialogType}`);
  if (dialogType === 'structure') {
    tableStructureDialogVisible.value = false;
  } else if (dialogType === 'indexes') {
    tableIndexesDialogVisible.value = false;
  } else if (dialogType === 'ddl') {
    tableDDLDialogVisible.value = false;
  }
}

// 处理取消查询的函数
async function handleCancelQuery(params) {
  try {
    console.log('[handleCancelQuery] 取消查询请求:', params);
    
    // 获取查询参数
    const { tabId, killInDatabase, datasourceId, schema, sqlContent } = params;
    
    // 重置标签页状态
    queryTabsManagerRef.value?.updateTabQueryState(tabId, {
      loading: false,
      isExecuting: false,
      error: '用户取消了查询'
    });
    
    // 如果需要终止数据库中的查询进程
    if (killInDatabase && datasourceId) {
      try {
        // 调用API终止数据库中的查询进程
        const response = await executeQueryAPI({
          datasource_id: datasourceId,
          sql_content: 'SELECT 1; /* KILL_QUERY_FLAG */', // 发送有效的SQL语句，同时添加特殊注释标记
          schema: schema,
          kill_query: true, // 添加标志，表示这是终止查询的请求
          tab_id: tabId,
          query_hash: generateQueryHash(sqlContent || '') // 添加查询哈希值，用于标识特定查询
        });
        
        console.log('[handleCancelQuery] 终止数据库查询进程响应:', response);
        
        if (response.success) {
          ElMessage.success('已成功终止数据库中的查询进程');
        } else {
          ElMessage.warning('终止数据库查询进程可能失败，但已取消本地查询');
        }
      } catch (error) {
        console.error('[handleCancelQuery] 终止数据库查询进程出错:', error);
        ElMessage.warning('终止数据库查询进程失败，但已取消本地查询');
      }
    }
  } catch (error) {
    console.error('[handleCancelQuery] 取消查询出错:', error);
    ElMessage.error('取消查询时发生错误');
  }
}

// 生成查询哈希值，用于标识特定查询
function generateQueryHash(sql) {
  // 简单的哈希函数，用于生成SQL查询的唯一标识
  let hash = 0;
  if (sql.length === 0) return hash.toString();
  
  for (let i = 0; i < sql.length; i++) {
    const char = sql.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  
  return hash.toString();
}

// 添加一个新的函数来处理标签页数据源或schema变更
function handleTabDatabaseChange(payload) {
  console.log('[handleTabDatabaseChange] 收到标签页数据源/schema变更事件:', payload);
  
  // 确保有有效的数据
  if (!payload || (!payload.datasourceId && !payload.schema)) {
    console.warn('[handleTabDatabaseChange] 无效的payload:', payload);
    return;
  }
  
  // 更新全局选择的数据源和schema
  if (payload.datasourceId && payload.datasourceId !== selectedDatabase.value) {
    console.log(`[handleTabDatabaseChange] 更新选择的数据源: ${selectedDatabase.value} -> ${payload.datasourceId}`);
    selectedDatabase.value = payload.datasourceId;
    // 数据源变更会触发 watch(selectedDatabase)，自动加载schemas
  }
  
  // 检查schema变更
  if (payload.schema) {
    // 即使数据源没有变化，也需要确保schema正确设置和刷新
    console.log(`[handleTabDatabaseChange] 检查schema变更: 当前=${selectedSchema.value}, 新=${payload.schema}`);
    
    if (payload.schema !== selectedSchema.value) {
      // 如果schema变更了，更新selectedSchema并触发刷新
      console.log(`[handleTabDatabaseChange] 更新选择的schema: ${selectedSchema.value} -> ${payload.schema}`);
      selectedSchema.value = payload.schema;
      // schema变更会触发 watch(selectedSchema)，自动加载对象
    } else {
      // 即使schema没变，也强制刷新一次对象浏览器
      console.log(`[handleTabDatabaseChange] Schema没有变化，但仍然强制刷新对象浏览器`);
      nextTick(() => {
        forceReloadCurrentSchemaObjects();
      });
    }
  }
}

// 添加专门用于导航变化的清理函数
function cleanupForNavigation() {
  console.log('[cleanupForNavigation] 开始执行导航变化清理');
  
  try {
    // 执行更彻底的清理
    cleanupBeforeNavigation();
    
    // 移除路由监听器
    const removeRouteGuard = router.beforeEach(() => {});
    if (typeof removeRouteGuard === 'function') {
      removeRouteGuard();
      console.log('[cleanupForNavigation] 已移除路由守卫');
    }
    
    // 重置所有组件状态
    selectedDatabase.value = null;
    selectedSchema.value = '';
    databaseObjects.value = [];
    schemas.value = [];
    activeTab.value = null;
    
    // 强制进行一次垃圾回收（不保证会立即执行）
    if (window.gc) {
      try {
        window.gc();
        console.log('[cleanupForNavigation] 已请求垃圾回收');
      } catch (e) {
        console.warn('[cleanupForNavigation] 请求垃圾回收失败:', e);
      }
    }
    
    console.log('[cleanupForNavigation] 导航变化清理完成');
    return true;
  } catch (error) {
    console.error('[cleanupForNavigation] 导航变化清理出错:', error);
    return false;
  }
}

// 创建基本的自动补全缓存(当无法获取完整元数据时使用)
async function createBasicAutocompleteCache(datasourceId) {
  console.log('[createBasicAutocompleteCache] 尝试创建基本的自动补全缓存');
  
  try {
    // 获取数据源的schema列表
    const schemaResponse = await getSchemaList(datasourceId);
    let schemaList = [];
    
    if (Array.isArray(schemaResponse)) {
      schemaList = schemaResponse;
    } else if (schemaResponse && typeof schemaResponse === 'object') {
      if (schemaResponse.schemas && Array.isArray(schemaResponse.schemas)) {
        schemaList = schemaResponse.schemas;
      } else if (schemaResponse.data && Array.isArray(schemaResponse.data)) {
        schemaList = schemaResponse.data;
      }
    }
    
    if (schemaList.length === 0) {
      console.warn('[createBasicAutocompleteCache] 没有找到任何schema');
      return;
    }
    
    console.log(`[createBasicAutocompleteCache] 找到 ${schemaList.length} 个schema`);
    
    // 为每个schema创建一个空的表结构
    const basicCache = {};
    schemaList.forEach(schema => {
      if (typeof schema === 'string') {
        basicCache[schema] = {}; // 空表结构
      } else if (schema && schema.value) {
        basicCache[schema.value] = {}; // 如果schema是对象，使用value属性
      }
    });
    
    // 保存基本缓存
    const cacheObject = {
      timestamp: Date.now(),
      data: basicCache
    };
    
    localStorage.setItem(`autocomplete_cache_${datasourceId}`, JSON.stringify(cacheObject));
    autocompleteCache.value = basicCache;
    
    console.log('[createBasicAutocompleteCache] 成功创建基本缓存');
    ElMessage.info(`已创建基本的自动补全缓存，包含 ${schemaList.length} 个schema。表结构信息暂不可用。`);
    
    return basicCache;
  } catch (error) {
    console.error('[createBasicAutocompleteCache] 创建基本缓存失败:', error);
    throw error;
  }
}

// 监听tab管理器触发的缓存刷新事件
const handleRefreshAutocompleteCache = async () => {
  const activeTab = queryTabsManagerRef.value?.activeTab;
  if (!activeTab) {
    ElMessage.warning('请先打开一个查询标签页');
    return;
  }

  if (!activeTab.datasourceId) {
    ElMessage.warning('请在当前标签页中选择一个数据源');
    return;
  }

  const { datasourceId } = activeTab;
  const elLoading = ElLoading.service({
    lock: true,
    text: '正在刷新自动补全缓存，这可能需要一点时间...',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  try {
    console.log(`开始刷新数据源 ${datasourceId} 的自动补全缓存`);
    
    // 检查localStorage中是否已有缓存，并检查缓存时间
    const cachedData = localStorage.getItem(`autocomplete_cache_${datasourceId}`);
    let cachedMetadata = null;
    let cacheTimestamp = 0;

    if (cachedData) {
      try {
        const parsed = JSON.parse(cachedData);
        if (parsed && typeof parsed === 'object') {
          if (parsed.timestamp) {
            cacheTimestamp = parsed.timestamp;
            cachedMetadata = parsed.data;
          } else {
            // 旧格式缓存
            cachedMetadata = parsed;
          }
        }
      } catch (e) {
        console.error('解析缓存数据失败:', e);
      }
    }
    
    // 检查缓存是否过期（超过30分钟）
    const now = Date.now();
    const cacheAge = now - cacheTimestamp;
    const cacheValid = cacheTimestamp > 0 && cacheAge < 1 * 60 * 1000;
    
    // 如果缓存有效，直接使用缓存
    if (cacheValid && cachedMetadata) {
      console.log(`使用本地缓存，缓存时间: ${new Date(cacheTimestamp).toLocaleString()}`);
      autocompleteCache.value = cachedMetadata;
      
      // 使用更友好的日期格式
      const cacheDate = new Date(cacheTimestamp);
      const formattedDate = `${cacheDate.getFullYear()}/${cacheDate.getMonth()+1}/${cacheDate.getDate()} ${cacheDate.getHours()}:${String(cacheDate.getMinutes()).padStart(2, '0')}:${String(cacheDate.getSeconds()).padStart(2, '0')}`;
      ElMessage.success(`使用缓存数据！上次更新: ${formattedDate}`);
      
      elLoading.close();
      return;
    }
    
    // 缓存无效或不存在，调用API获取新数据
    console.log('缓存无效或不存在，从服务器获取数据');
    
    // 调用接口获取数据
    let fullMetadata;
    try {
      // 调用新的高效接口
      fullMetadata = await getDatabaseFullMetadata(datasourceId);
    } catch (apiError) {
      console.error('获取元数据失败，尝试使用本地缓存:', apiError);
      // 如果API失败但有缓存，使用缓存并显示警告
      if (cachedMetadata) {
        autocompleteCache.value = cachedMetadata;
        // 同样使用更友好的日期格式显示
        const cacheDate = new Date(cacheTimestamp);
        const formattedDate = `${cacheDate.getFullYear()}/${cacheDate.getMonth()+1}/${cacheDate.getDate()} ${cacheDate.getHours()}:${String(cacheDate.getMinutes()).padStart(2, '0')}:${String(cacheDate.getSeconds()).padStart(2, '0')}`;
        ElMessage.warning(`服务器数据获取失败，使用本地缓存数据 (${formattedDate})`);
        elLoading.close();
        return;
      }
      // 如果API失败且没有缓存，抛出错误
      throw apiError;
    }
    
    // 统计缓存数据信息
    let schemaCount = 0;
    let tableCount = 0;
    
    if (fullMetadata) {
      // 确保数据格式正确
      if (typeof fullMetadata === 'object') {
        schemaCount = Object.keys(fullMetadata).length;
        for (const schema in fullMetadata) {
          tableCount += Object.keys(fullMetadata[schema] || {}).length;
        }

        // 保存到缓存，包含时间戳
        const cacheObject = {
          timestamp: Date.now(),
          data: fullMetadata
        };
        localStorage.setItem(`autocomplete_cache_${datasourceId}`, JSON.stringify(cacheObject));
        autocompleteCache.value = fullMetadata;
        
        ElMessage.success(`缓存刷新成功！共加载 ${schemaCount} 个Schema，${tableCount} 个表。`);
      } else {
        throw new Error('服务器返回的数据格式不正确');
      }
    } else {
      throw new Error('服务器未返回数据');
    }
    
  } catch (error) {
    console.error('刷新补全缓存失败:', error);
    ElMessage.error(`刷新补全缓存失败: ${error.message || '未知错误'}`);
    
    // 尝试从数据源列表和schema信息构建基本缓存
    try {
      await createBasicAutocompleteCache(datasourceId);
    } catch (e) {
      console.error('创建基本缓存也失败:', e);
    }
  } finally {
    elLoading.close();
  }
};

// 添加处理单元格值更新的方法
async function handleUpdateCellValue(payload) {
  console.log('[QueryView] 处理单元格值更新 - 原始参数:', payload);
  
  // 检查是否有权限
  if (!isAdmin.value && !(userInfo.value?.permissions?.includes('DATA_EDIT'))) {
    ElMessage.warning('很抱歉，您暂时没有数据编辑权限，请联系系统管理员申请');
    return;
  }

  // 确保columnName字段存在
  if (!payload.columnName) {
    if (payload.columnKey) {
      console.log('[QueryView] 使用columnKey作为columnName:', payload.columnKey);
      payload.columnName = payload.columnKey;
    } else if (payload.column) {
      console.log('[QueryView] 使用column作为columnName:', payload.column);
      payload.columnName = payload.column;
    }
  }

  if (!payload.columnName) {
    console.error('[QueryView] 缺少columnName字段，无法确定要更新的字段');
    ElMessage.error('无法确定要更新的字段');
    return;
  }

  console.log('[QueryView] 处理单元格值更新 - 处理后的参数:', { 
    columnName: payload.columnName, 
    newValue: payload.newValue,
    tableName: payload.tableName
  });
  
  // 显示loading
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在更新数据...',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  
  try {
    // 调用API更新单元格值
    const response = await updateCellValueAPI({
      datasourceId: payload.datasourceId,
      schema: payload.schema,
      tableName: payload.tableName,
      columnName: payload.columnName,
      oldValue: payload.oldValue,
      newValue: payload.newValue,
      rowData: payload.rowData, // 用于定位记录的完整行数据
      sql: payload.sql // 可选，用于生成更新语句
    });
    
    if (response && (response.success || response.code === 200)) {
      ElMessage.success('数据更新成功');
      
      // 重新执行查询，刷新结果
      if (payload.tabId) {
        const queryTabsManager = queryTabsManagerRef.value;
        if (queryTabsManager) {
          await queryTabsManager.refreshTabResults(payload.tabId);
        }
      }
    } else {
      ElMessage.error(`更新失败: ${response?.message || '未知错误'}`);
    }
  } catch (error) {
    console.error('更新单元格值出错:', error);
    ElMessage.error(`更新失败: ${error.message || '未知错误'}`);
  } finally {
    loadingInstance.close();
  }
}

// 添加处理请求编辑权限的方法
async function handleRequestEditPermission(payload) {
  console.log('[QueryView] 处理请求编辑权限:', payload);
  
  try {
    // 这里可以调用API将请求记录到系统中，或直接发送通知给管理员
    // 示例：发送通知
    ElNotification({
      title: '收到编辑权限申请',
      message: `用户 ${payload.userName || '未知用户'} 申请数据编辑权限。原因: ${payload.reason || '无'}`,
      type: 'info',
      duration: 10000, // 10秒
      position: 'top-right'
    });
    
    // 如果系统中有管理员，可以发送系统消息给管理员
    if (isAdmin.value) {
      // 记录到本地，实际项目中应该保存到服务器
      const pendingRequests = JSON.parse(localStorage.getItem('pendingEditRequests') || '[]');
      pendingRequests.push({
        id: Date.now().toString(),
        userId: payload.userId,
        userName: payload.userName,
        datasourceId: payload.datasourceId,
        schema: payload.schema,
        reason: payload.reason,
        requestTime: new Date().toISOString(),
        status: 'pending'
      });
      localStorage.setItem('pendingEditRequests', JSON.stringify(pendingRequests));
    }
    
    ElMessage.success('您的申请已提交，请等待审核');
  } catch (error) {
    console.error('提交权限请求出错:', error);
    ElMessage.error(`提交失败: ${error.message || '未知错误'}`);
  }
}

// 为QueryTabsManager添加refreshTabResults方法
// 需要修改QueryTabsManager.vue，添加这个方法的实现

// 添加处理批量提交更改的函数
async function handleSubmitChanges(changes) {
  console.log('[index.vue] 接收到批量提交更改请求:', changes);
  
  if (!changes || !changes.changes || !Array.isArray(changes.changes) || !changes.changes.length) {
    const error = '没有有效的更改需要提交: ' + JSON.stringify(changes);
    console.error(error);
    ElMessage.error(error);
    return;
  }
  
  // 显示加载状态
  const loading = ElLoading.service({
    lock: true,
    text: '正在提交修改...',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  
  try {
    // 提取必要参数
    const { datasourceId, database, schema, tableName, changes: changesList } = changes;
    
    console.log('[index.vue] 变更提交参数:', {
      datasourceId, database, schema, tableName,
      changeCount: changesList.length,
      firstChange: changesList[0]
    });
    
    if (!datasourceId) {
      throw new Error('缺少数据源ID');
    }
    
    if (!database && !schema) {
      throw new Error('缺少数据库/模式信息');
    }
    
    if (!tableName) {
      throw new Error('无法确定要操作的表名');
    }
    
    // 执行强制转换，确保数据结构完整
    const processedChanges = changesList.map(change => {
      // 确保type字段存在
      if (!change.type) {
        console.warn('变更缺少type字段，默认设置为update:', change);
        change.type = 'update';
      }
      
      // 确保row字段存在
      if (!change.row) {
        console.error('变更缺少row字段，无法确定更新条件:', change);
        throw new Error('变更缺少row字段，无法确定更新条件');
      }
      
      // 确保columnName存在
      if (change.type === 'update' && !change.columnName && !change.column) {
        // 尝试从row对象中获取一个键作为列名
        change.columnName = Object.keys(change.row)[0];
        console.warn(`修正缺少的columnName字段为: ${change.columnName}`);
      }
      
      return change;
    });
    
    console.log('[index.vue] 准备调用submitBatchChanges:', {
      datasourceId, 
      database: database || schema, // 使用database或schema
      schema: schema || database, // 使用schema或database
      tableName,
      changes: processedChanges
    });
    
    // 直接进行批量更新处理
    
    // 调用批量更新API
    const result = await submitBatchChanges({
      datasourceId,
      database: database || schema, // 使用database或schema
      schema: schema || database, // 使用schema或database
      tableName,
      changes: processedChanges
    });
    
    console.log('[index.vue] 批量提交结果:', result);

    // 获取当前活动标签页
    const currentTab = queryTabsManagerRef.value?.activeTab;
    if (currentTab && result && result.results) {
      console.log('[index.vue] 设置批量变更结果到当前标签页');

      // 将批量提交结果转换为 multiDmlResults 格式
      const multiDmlResults = result.results.map((item, index) => {
        const change = processedChanges[index];
        let sqlText = '';

        // 根据操作类型生成SQL描述
        if (change.type === 'update') {
          const columnName = change.columnName || change.column || Object.keys(change.fields || {})[0];
          sqlText = `UPDATE ${tableName} SET ${columnName} = ... WHERE ...`;
        } else if (change.type === 'add' || change.type === 'insert') {
          sqlText = `INSERT INTO ${tableName} VALUES (...)`;
        } else if (change.type === 'delete') {
          sqlText = `DELETE FROM ${tableName} WHERE ...`;
        } else {
          sqlText = `${change.type?.toUpperCase() || 'UNKNOWN'} ${tableName}`;
        }

        return {
          sql: sqlText,
          success: !item.error,
          affectedRows: item.affected_rows || (item.success ? 1 : 0),
          error: item.error || null
        };
      });

      // 创建新的结果标签页来显示操作执行结果
      const targetTabId = changes.tabId || currentTab.id;
      console.log('[index.vue] 为手动编辑提交创建新的结果标签页');

      // 确保resultTabs已初始化
      if (!currentTab.resultTabs) {
        currentTab.resultTabs = [];
      }

      // 限制结果标签页数量
      if (currentTab.resultTabs.length >= 5) {
        currentTab.resultTabs.shift();
        console.log('[index.vue] 已达到结果标签页上限(5)，移除最早的标签页');
      }

      // 创建新的结果标签页用于显示操作执行结果
      const newResultTab = {
        id: `result-tab-${Date.now()}`,
        results: [], // 操作执行结果不需要表格数据
        columns: [],
        timestamp: Date.now(),
        sql: `手动编辑提交 - ${tableName}`,
        isSelect: false, // 明确标记这不是查询结果，不应显示编辑按钮
        isEditSubmission: true, // 标记这是手动编辑提交的结果
        multiDmlResults: multiDmlResults // 将操作结果放在结果标签页中
      };

      currentTab.resultTabs.push(newResultTab);
      console.log(`[index.vue] 已添加手动编辑提交的结果标签页，当前共 ${currentTab.resultTabs.length} 个标签页`);

      // 不自动切换到结果标签页，让用户保持在原来的数据标签页上
      // 用户可以手动点击结果标签页查看执行结果
      console.log('[index.vue] 已创建结果标签页，但不自动切换，保持在原数据标签页');

      // 只设置主标签页的执行状态，不设置 multiDmlResults（避免清除 resultTabs）
      queryTabsManagerRef.value?.updateTabQueryState(targetTabId, {
        executed: true,
        results: [],
        columns: [],
        messages: `批量提交完成：成功 ${result.successful}/${result.total} 项`,
        error: null
      });

      console.log('[index.vue] 已调用 updateTabQueryState 并创建结果标签页');

      // 表格组件已经在用户确认时立即退出编辑模式，这里不需要再次触发
      console.log('[index.vue] 表格组件已处理编辑模式退出，无需额外操作');
    }

    if (result && result.success) {
      ElMessage.success(`所有修改已成功提交 (${result.successful}/${result.total}项)`);
    } else if (result && result.failed > 0) {
      ElMessage.warning(`部分修改提交失败 (成功: ${result.successful}/${result.total}项)`);
    } else {
      ElMessage.success('提交成功，数据已保存');
    }

    // 不再自动刷新查询结果，显示成功提示即可
    console.log('[index.vue] 修改已成功提交，不自动刷新');

    // 添加一个延迟，让用户看到成功消息
    await new Promise(resolve => setTimeout(resolve, 1000));
  } catch (error) {
    console.error('[index.vue] 批量提交失败:', error);
    ElMessage.error('提交修改失败: ' + (error.message || '未知错误'));

    // 触发自动退出编辑模式事件（失败时不保存修改）
    const currentTab = queryTabsManagerRef.value?.activeTab;
    document.dispatchEvent(new CustomEvent('auto-exit-edit-mode', {
      detail: {
        tabId: changes.tabId || currentTab?.id,
        saveChanges: false, // 失败时不保存修改
        reason: 'submit-failed'
      }
    }));
  } finally {
    // 关闭加载状态
    if (loading) {
      loading.close();
    }
  }
}

async function handleFormatSql(payload) {
  console.log("[index.vue handleFormatSql] 接收到格式化SQL请求:", payload);
  
  if (!payload || !payload.tabId) {
    console.error("[index.vue handleFormatSql] Invalid payload for format SQL", payload);
    return;
  }
  
  try {
    // 获取当前标签页
    const tabId = payload.tabId;
    const sql = payload.sql;
    
    console.log(`[index.vue handleFormatSql] 格式化SQL: tabId=${tabId}, sql长度=${sql?.length || 0}`);
    
    if (!sql || !sql.trim()) {
      console.warn("[index.vue handleFormatSql] 没有可格式化的SQL");
      ElMessage.warning('没有可格式化的SQL');
      return;
    }
    
    // 简单的SQL格式化逻辑
    const formatted = sql
      .replace(/\s+/g, ' ')
      .replace(/\s*,\s*/g, ', ')
      .replace(/\s*;\s*/g, ';\n')
      .replace(/\b(SELECT|FROM|WHERE|GROUP BY|HAVING|ORDER BY|LIMIT|JOIN|LEFT JOIN|RIGHT JOIN|INNER JOIN|ON|AND|OR)\b/gi, '\n$1')
      .replace(/^\n/, '')
      .split('\n')
      .map(line => line.trim().startsWith('ON') || line.trim().startsWith('AND') || line.trim().startsWith('OR') ? '  ' + line.trim() : line.trim())
      .join('\n');
    
    console.log(`[index.vue handleFormatSql] 格式化完成，更新标签页 ${tabId} 的SQL`);
    
    // 更新标签页SQL
    queryTabsManagerRef.value?.updateTabSql(tabId, formatted);
    ElMessage.success('SQL已格式化');
  } catch (error) {
    console.error("[index.vue handleFormatSql] 格式化SQL时出错:", error);
    ElMessage.error(`SQL格式化失败: ${error.message}`);
  }
}
</script>

<style scoped>
.sql-query-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.sql-query-main {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
}

.left-panel {
  width: 280px; /* 增加宽度 */
  border-right: 1px solid #dcdfe6;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 30; /* 增加 z-index 确保左侧面板在最上层 */
}

.query-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 1;
  margin-left: 5px; /* 增加左边距 */
}

.custom-context-menu {
  position: fixed;
  background: #fff;
  border: 1px solid #dcdfe6;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  border-radius: 4px;
  padding: 10px 0;
  z-index: 9999;
  min-width: 180px;
}

.custom-context-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.custom-context-menu li {
  display: flex;
  align-items: center;
  padding: 0 20px;
  height: 34px;
  line-height: 34px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
}

.custom-context-menu li:hover {
  background-color: #ecf5ff;
  color: #409eff;
}

.custom-context-menu .el-icon {
  margin-right: 8px;
  font-size: 16px;
}
</style>