/* 主容器样式 */
.sql-query-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f5f7fa;
}

/* 顶部工具栏样式 */
.sql-query-toolbar {
  padding: 12px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.connection-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.db-select {
  width: 220px;
}

.schema-select {
  width: 180px;
}

.db-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 主体内容区样式 */
.sql-query-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

/* 标签页样式 */
.query-tabs {
  width: 100%;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0 5px;
}

.query-tabs :deep(.el-tabs__header) {
  margin-bottom: 0;
}

.query-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0;
}

.query-tabs :deep(.el-tabs__item) {
  height: 36px;
  line-height: 36px;
}

/* 标签页下方的内容区域 */
.query-content-area {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 对象浏览器样式 */
.object-browser {
  width: 300px;
  border-right: 1px solid #e4e7ed;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.browser-header {
  padding: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.browser-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.browser-actions {
  display: flex;
  gap: 5px;
}

.browser-header h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 0; /* 覆盖原来的下边距 */
}

.browser-body {
  flex: 1;
  overflow: auto;
  padding: 8px;
}

/* 查询编辑器区域样式 */
.query-editor-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 单个查询编辑器容器 */
.query-editor-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 查询标签页容器 */
.query-tabs-container {
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0 5px;
  flex-shrink: 0; /* 防止标签容器被压缩 */
}

/* 查询标签样式 */
.query-tag {
  margin-right: 6px;
  margin-bottom: 6px;
}

/* 标签输入框样式 */
.tag-input {
  width: 100px;
}

/* 树节点样式 */
.tree-node {
  display: flex;
  align-items: center;
  gap: 5px;
}

.row-count {
  font-size: 12px;
  color: #909399;
  margin-left: 5px;
}

/* 已保存查询对话框样式 */
.saved-queries-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.saved-queries-dialog :deep(.el-dialog__body) {
  padding: 16px;
}

/* SQL内容查看对话框样式 */
.sql-content-dialog {
  width: 800px;
  max-height: 60vh;
  overflow-y: auto;
}

.sql-code-container {
  position: relative;
}

.sql-code-container pre {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
  margin: 0;
}

.sql-code-container button {
  position: absolute;
  top: 10px;
  right: 10px;
  margin: 0;
}

/* 长内容显示对话框样式 */
.long-content-dialog {
  width: 800px;
  max-height: 60vh;
  overflow-y: auto;
}

.long-content-container pre {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
  margin: 0;
  max-height: 60vh;
  overflow-y: auto;
}

.long-content-container button {
  position: absolute;
  top: 10px;
  right: 10px;
  margin: 0;
}

/* 新增：刷新图标旋转动画 */
.is-loading svg {
  animation: rotating 1.5s linear infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 优化表格区域与工具栏的连接 */
.query-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 标签栏中的编辑按钮样式 */
.results-tabs .edit-mode-button {
  margin-right: 0;
  margin-left: 5px;
  height: 28px;
  padding: 0 10px;
  font-size: 12px;
  position: relative;
  z-index: 10;
}

/* 调整标签栏布局 */
.results-tabs {
  display: flex;
  align-items: center;
  padding-left: 5px;
}

/* 标签栏左侧区域 */
.tabs-left-section {
  display: flex;
  align-items: center;
  min-width: 100px;
}

.editor-container {
  border-bottom: none;
}

.resizer {
  height: 4px;
  background-color: #f5f7fa;
  cursor: row-resize;
  border-top: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
}

.toolbar {
  border-top: none;
}

/* 优化结果区域 */
.results-container {
  margin: 0;
  padding: 0;
  border: none;
}

/* 优化表格样式 */
.el-table-v2 {
  border: none !important;
}

/* 优化表格头部 */
.el-table-v2__header-row {
  background-color: #f5f7fa !important;
}

/* 优化表格单元格 */
.el-table-v2__cell {
  padding: 6px 8px !important;
}

/* 优化空白区域 */
.empty-result-content {
  margin: 0;
  padding: 20px;
} 