<template>
  <div class="database-container">
    <div class="top-panel">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-input
            v-model="searchText"
            placeholder="搜索数据库名称、主机地址或标签"
            clearable
            prefix-icon="Search"
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="8" class="action-col">
          <el-button v-if="isAdmin" type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>新建连接
          </el-button>
          <el-button @click="handleRefresh">
            <el-icon><Refresh /></el-icon>刷新
          </el-button>
        </el-col>
      </el-row>
    </div>

    <el-card class="database-list">
      <template #header>
        <div class="list-header">
          <span>数据库连接列表</span>
          <div class="filter-tags">
            <el-select v-model="typeFilter" clearable placeholder="数据库类型" @change="handleFilter">
              <el-option v-for="type in dbTypes" :key="type.value" :label="type.label" :value="type.value" />
            </el-select>
            <el-select v-model="statusFilter" clearable placeholder="连接状态" @change="handleFilter">
              <el-option v-for="status in statusTypes" :key="status.value" :label="status.label" :value="status.value" />
            </el-select>
          </div>
        </div>
      </template>

      <el-table :data="filteredDatabases" border style="width: 100%" v-loading="loading">
        <el-table-column prop="name" label="数据库名称" min-width="150">
          <template #default="scope">
            <div class="db-name-cell">
              <el-icon :class="getDbIconClass(scope.row.type)"><Connection /></el-icon>
              <span>{{ scope.row.name }}</span>
              <el-tag v-if="scope.row.isProduction" type="danger" size="small">生产</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="数据库类型" width="120">
          <template #default="scope">
            <el-tag :type="getDbTypeTag(scope.row.type)">{{ scope.row.type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="host" label="主机地址" min-width="180" />
        <el-table-column prop="port" label="端口" width="100" />
        <el-table-column prop="status" label="状态" width="130">
          <template #default="scope">
            <div class="status-container">
              <el-tag v-if="scope.row.status === 'online' || scope.row.status === 'ok'" type="success">在线</el-tag>
              <el-tag v-else-if="scope.row.status === 'connecting'" type="warning" class="connecting">连接中</el-tag>
              <el-tag v-else-if="scope.row.status === 'loading'" type="info" class="connecting">检测中</el-tag>
              <el-tag v-else type="danger">离线</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="标签" width="200">
          <template #default="scope">
            <el-tag 
              v-for="tag in scope.row.tags" 
              :key="tag" 
              class="db-tag"
              size="small"
            >
              {{ tag }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button v-if="isAdmin" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button v-if="isAdmin" size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
            <span v-if="!isAdmin">-</span>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalDatabases"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑数据库连接对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEditing ? '编辑数据库连接' : '新建数据库连接'"
      width="600px"
    >
      <el-form :model="databaseForm" :rules="formRules" ref="databaseFormRef" label-width="120px">
        <el-form-item label="连接名称" prop="name">
          <el-input v-model="databaseForm.name" placeholder="请输入连接名称" />
        </el-form-item>
        <el-form-item label="数据库类型" prop="type">
          <el-select v-model="databaseForm.type" placeholder="请选择数据库类型" style="width: 100%">
            <el-option v-for="type in dbTypes" :key="type.value" :label="type.label" :value="type.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="主机地址" prop="host">
          <el-input v-model="databaseForm.host" placeholder="请输入主机地址" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="databaseForm.port" :min="1" :max="65535" style="width: 100%" />
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="databaseForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="databaseForm.password" type="password" placeholder="请输入密码" show-password />
        </el-form-item>
        <el-form-item label="数据库名" prop="dbName">
          <el-input v-model="databaseForm.dbName" placeholder="请输入默认连接的数据库名称（可选）" />
        </el-form-item>
        <el-form-item label="标签">
          <el-tag
            v-for="tag in databaseForm.tags"
            :key="tag"
            closable
            @close="handleRemoveTag(tag)"
            class="db-tag"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="tagInputVisible"
            ref="tagInputRef"
            v-model="tagInputValue"
            class="tag-input"
            size="small"
            @keyup.enter="handleTagConfirm"
            @blur="handleTagConfirm"
          />
          <el-button v-else size="small" @click="showTagInput">+ 新标签</el-button>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="databaseForm.isProduction" label="这是生产环境数据库" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitForm">确认</el-button>
          <el-button v-if="!isEditing" type="info" @click="handleTestConnection">测试连接</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Refresh, Connection } from '@element-plus/icons-vue'
import { 
  getDatasources, 
  createDatasource, 
  updateDatasource, 
  deleteDatasource, 
  testConnection,
  testTempConnection,
  getDatabaseTypes,
  getDatasourceStatus
} from '../../api/datasource'

// 判断当前用户是否为管理员
const isAdmin = computed(() => {
  const userInfoStr = localStorage.getItem('userInfo')
  if (!userInfoStr) return false
  
  try {
    const userInfo = JSON.parse(userInfoStr)
    return userInfo.is_superuser || userInfo.is_staff || false
  } catch (e) {
    console.error('解析用户信息失败:', e)
    return false
  }
})

// 数据库类型列表
const dbTypes = ref([])

// 状态类型
const statusTypes = [
  { label: '在线', value: 'online' },
  { label: '连接中', value: 'connecting' },
  { label: '检测中', value: 'loading' },
  { label: '离线', value: 'offline' }
]

// 数据库连接列表（将从API获取）
const databases = ref([])

// 状态
const loading = ref(false)
const searchText = ref('')
const typeFilter = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const totalDatabases = ref(4)
const dialogVisible = ref(false)
const isEditing = ref(false)
const tagInputVisible = ref(false)
const tagInputValue = ref('')
const tagInputRef = ref(null)
const databaseFormRef = ref(null)

// 表单数据
const databaseForm = reactive({
  id: null,
  name: '',
  type: '',
  host: '',
  port: 3306,
  username: '',
  password: '',
  dbName: '',
  tags: [],
  isProduction: false
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入连接名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择数据库类型', trigger: 'change' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口', trigger: 'blur' },
    { type: 'number', message: '端口必须为数字', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

// 筛选后的数据库列表
const filteredDatabases = computed(() => {
  let result = [...databases.value]
  
  // 搜索过滤
  if (searchText.value) {
    const searchLower = searchText.value.toLowerCase()
    result = result.filter(db => 
      db.name.toLowerCase().includes(searchLower) || 
      db.host.toLowerCase().includes(searchLower) ||
      db.tags.some(tag => tag.toLowerCase().includes(searchLower))
    )
  }
  
  // 数据库类型过滤
  if (typeFilter.value) {
    result = result.filter(db => db.type === typeFilter.value)
  }
  
  // 状态过滤
  if (statusFilter.value) {
    result = result.filter(db => db.status === statusFilter.value)
  }
  
  return result
})

// 根据数据库类型获取图标类名
const getDbIconClass = (type) => {
  const typeMap = {
    'MySQL': 'mysql-icon',
    'PostgreSQL': 'postgres-icon',
    'Oracle': 'oracle-icon',
    'SQL Server': 'sqlserver-icon',
    'MongoDB': 'mongodb-icon',
    'Redis': 'redis-icon',
    'SQLite': 'sqlite-icon'
  }
  return typeMap[type] || 'db-icon'
}

// 根据数据库类型获取标签类型
const getDbTypeTag = (type) => {
  const typeMap = {
    'MySQL': '',
    'PostgreSQL': 'success',
    'Oracle': 'warning',
    'SQL Server': 'danger',
    'MongoDB': 'info',
    'Redis': 'danger',
    'SQLite': ''
  }
  return typeMap[type] || ''
}

// 处理搜索
const handleSearch = () => {
  // 可以在这里添加额外的搜索逻辑
}

// 处理筛选
const handleFilter = () => {
  currentPage.value = 1 // 重置页码
}

// 获取数据库列表
const fetchDatabases = async () => {
  loading.value = true
  try {
    // 调用API获取数据
    const res = await getDatasources()
    console.log('获取数据库列表原始响应:', res)
    
    // 处理不同格式的响应数据
    let dataList = null
    
    if (Array.isArray(res)) {
      // 直接返回数组的情况
      dataList = res
    } else if (res && typeof res === 'object') {
      // 可能在不同属性中包含数据
      if (res.data && Array.isArray(res.data)) {
        dataList = res.data
      } else if (res.results && Array.isArray(res.results)) {
        dataList = res.results
      } else if (res.items && Array.isArray(res.items)) {
        dataList = res.items
      } else if (res.datasources && Array.isArray(res.datasources)) {
        dataList = res.datasources
      } else if (res.list && Array.isArray(res.list)) {
        dataList = res.list
      } else {
        // 尝试查找任何数组类型的属性
        const arrayProps = Object.keys(res).filter(key => Array.isArray(res[key]))
        if (arrayProps.length > 0) {
          dataList = res[arrayProps[0]]
        }
      }
    }
    
    if (dataList && dataList.length >= 0) {
      console.log('提取到的数据库列表:', dataList)
      
      // 保存当前的状态信息，避免状态闪烁
      const currentStatusMap = {}
      databases.value.forEach(db => {
        if (db.id) {
          currentStatusMap[db.id] = db.status
        }
      })
      
      // 标准化数据字段
      databases.value = dataList.map(item => {
        // 处理可能的不同字段名
        const normalizedItem = {
          id: item.id,
          name: item.name,
          type: item.type || item.db_type || item.database_type || 'Unknown',
          host: item.host,
          port: parseInt(item.port) || 0,
          username: item.username || item.user || '',
          password: '******', // 密码不显示原文
          dbName: item.database || item.db_name || item.dbName || '',
          // 保留当前状态，避免闪烁
          status: currentStatusMap[item.id] || item.status || item.connection_status || 'loading',
          tags: Array.isArray(item.tags) ? item.tags : (item.tags ? [item.tags] : []),
          isProduction: item.is_production || item.isProduction || false
        }
        
        return normalizedItem
      })
      
      totalDatabases.value = databases.value.length
      
      // 获取数据库列表后立即更新状态
      await fetchDatabaseStatus()
    } else {
      // 响应无效或没有data属性
      console.error('获取数据库列表返回数据格式无法解析:', res)
      ElMessage.warning('数据库列表格式不正确，显示为空')
      databases.value = []
      totalDatabases.value = 0
    }
  } catch (error) {
    console.error('获取数据库列表失败:', error)
    console.error('错误详情:', error.message)
    if (error.response) {
      console.error('响应状态:', error.response.status)
      console.error('响应数据:', error.response.data)
    }
    ElMessage.error('获取数据库列表失败，请检查网络连接')
    databases.value = []
    totalDatabases.value = 0
  } finally {
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(async () => {
  try {
    // 获取数据库类型列表
    const typesRes = await getDatabaseTypes()
    console.log('获取到的数据库类型数据(原始):', typesRes)
    console.log('数据类型:', typeof typesRes)
    
    // 提取数据库类型列表
    let typesList = null;
    
    if (typesRes && Array.isArray(typesRes)) {
      // 直接是数组
      typesList = typesRes;
    } else if (typesRes && typesRes.data && Array.isArray(typesRes.data)) {
      // 通过data属性返回数组
      typesList = typesRes.data;
    } else if (typesRes && typesRes.results && Array.isArray(typesRes.results)) {
      // 通过results属性返回数组（常见于分页结果）
      typesList = typesRes.results;
    } else if (typesRes && typeof typesRes === 'object') {
      // 尝试寻找数组属性
      const possibleArrayProps = ['types', 'database_types', 'databaseTypes'];
      for (const prop of possibleArrayProps) {
        if (typesRes[prop] && Array.isArray(typesRes[prop])) {
          typesList = typesRes[prop];
          break;
        }
      }
    }
    
    if (typesList && typesList.length > 0) {
      // 检查是否包含必要的属性
      const sampleItem = typesList[0];
      const hasRequiredProps = sampleItem.id !== undefined && sampleItem.name !== undefined;
      
      if (hasRequiredProps) {
        // 标准化处理
        dbTypes.value = typesList.map(type => ({
          label: type.name,
          value: type.name, // 确保value是字符串类型的名称
          id: type.id,
          code: type.code || type.name.toLowerCase(),
          icon: type.icon || `${type.name.toLowerCase()}-icon`
        }));
        console.log('处理后的数据库类型列表:', dbTypes.value);
      } else {
        throw new Error('数据库类型数据格式不符合要求');
      }
    } else {
      throw new Error('未找到有效的数据库类型列表');
    }
  } catch (error) {
    console.error('获取数据库类型列表失败:', error)
    // 出错时使用默认值
    dbTypes.value = [
      { label: 'MySQL', value: 'MySQL', id: 1, code: 'mysql', icon: 'mysql-icon' },
      { label: 'PostgreSQL', value: 'PostgreSQL', id: 2, code: 'postgresql', icon: 'postgres-icon' },
      { label: 'Oracle', value: 'Oracle', id: 3, code: 'oracle', icon: 'oracle-icon' },
      { label: 'SQL Server', value: 'SQL Server', id: 4, code: 'sqlserver', icon: 'sqlserver-icon' }
    ]
    console.log('使用默认数据库类型列表:', dbTypes.value);
  }
  
  // 获取数据库列表（会自动获取状态）
  await fetchDatabases()
  
  // 启动定时获取数据库状态
  startStatusPolling()
})

// 组件卸载时清除定时器
onUnmounted(() => {
  stopStatusPolling()
})

// 状态轮询定时器
let statusPollingTimer = null

// 获取数据库状态
const fetchDatabaseStatus = async () => {
  try {
    console.log('开始获取数据库状态...')
    
    // 设置超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时
    
    try {
      const response = await getDatasourceStatus();
      clearTimeout(timeoutId); // 清除超时
      
      if (Array.isArray(response)) {
        // 更新数据库状态
        response.forEach(item => {
          const index = databases.value.findIndex(db => db.id === item.id)
          if (index !== -1) {
            // 仅当状态发生变化时更新
            if (databases.value[index].status !== item.status) {
              console.log(`数据库 ${item.name} 状态更新: ${databases.value[index].status} -> ${item.status}`)
              databases.value[index].status = item.status
            }
          }
        })
        console.log('数据库状态已更新')
      } else {
        console.error('获取数据库状态返回格式错误:', response)
        // 即使格式错误，也不影响现有状态显示
      }
    } catch (fetchError) {
      clearTimeout(timeoutId);
      throw fetchError; // 重新抛出错误以便外层catch处理
    }
  } catch (error) {
    console.error('获取数据库状态失败:', error)
    // 出错时不更新状态，保持现有显示
    // 不向用户显示错误消息，避免干扰体验
  }
}

// 开始定时获取数据库状态
const startStatusPolling = () => {
  // 这里不需要立即获取一次状态了，因为fetchDatabases会自动获取状态
  
  // 设置定时器，每20秒获取一次
  statusPollingTimer = setInterval(() => {
    // 只有当不在加载状态时才获取状态，避免与其他操作冲突
    if (!loading.value) {
      // 使用setTimeout包装，确保即使fetchDatabaseStatus失败也不会影响轮询
      setTimeout(() => {
        fetchDatabaseStatus().catch(err => {
          console.error('状态轮询出错:', err);
          // 错误已记录，但不中断轮询
        });
      }, 0);
    }
  }, 20000) // 20秒
}

// 停止定时获取数据库状态
const stopStatusPolling = () => {
  if (statusPollingTimer) {
    clearInterval(statusPollingTimer)
    statusPollingTimer = null
  }
}

// 处理刷新
const handleRefresh = async () => {
  loading.value = true
  try {
    // 只获取数据库列表，列表获取完成后会自动获取状态
    await fetchDatabases()
    ElMessage.success('数据已刷新')
  } catch (error) {
    console.error('刷新数据库列表失败:', error)
    ElMessage.error('刷新数据库列表失败')
  } finally {
    loading.value = false
  }
}

// 处理连接
const handleConnect = (row) => {
  ElMessage.success(`正在连接到数据库: ${row.name}`)
  
  // 更新本地状态为连接中
  updateDbStatus(row.id, 'connecting')
  
  // 测试连接状态
  testConnection(row.id)
    .then(() => {
      // 连接成功，更新状态
      updateDbStatus(row.id, 'online')
      
      // 跳转到查询页面
      window.location.href = `/query?db=${row.id}`
    })
    .catch(err => {
      // 连接失败，更新状态
      updateDbStatus(row.id, 'offline')
      
      // 显示错误信息
      if (err.response && err.response.data && (err.response.data.message || err.response.data.detail)) {
        ElMessage.error(err.response.data.message || err.response.data.detail)
      } else {
        ElMessage.error('连接失败，请检查数据库连接信息')
      }
    })
}

// 新建连接
const showCreateDialog = () => {
  isEditing.value = false
  Object.keys(databaseForm).forEach(key => {
    if (key === 'port') {
      databaseForm[key] = 3306
    } else if (key === 'tags') {
      databaseForm[key] = []
    } else if (key === 'isProduction') {
      databaseForm[key] = false
    } else {
      databaseForm[key] = ''
    }
  })
  dialogVisible.value = true
}

// 编辑连接
const handleEdit = (row) => {
  isEditing.value = true
  
  Object.keys(databaseForm).forEach(key => {
    databaseForm[key] = row[key]
  })
  
  dialogVisible.value = true
}

  // 删除连接
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除数据库连接 "${row.name}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      loading.value = true
      const response = await deleteDatasource(row.id)
      console.log('删除响应:', response, '状态码:', response?.status)
      
      // 本地更新列表
      const index = databases.value.findIndex(item => item.id === row.id)
      if (index !== -1) {
        databases.value.splice(index, 1)
        totalDatabases.value--
      }
      ElMessage.success('删除成功')
    } catch (error) {
      console.error('删除数据库连接失败:', error)
      
      // 检查是否为204成功响应但被当做错误处理
      if (error.response && error.response.status === 204) {
        console.log('删除实际上是成功的 (204 No Content)')
        // 本地更新列表
        const index = databases.value.findIndex(item => item.id === row.id)
        if (index !== -1) {
          databases.value.splice(index, 1)
          totalDatabases.value--
        }
        ElMessage.success('删除成功')
      } else {
        ElMessage.error('删除数据库连接失败')
      }
    } finally {
      loading.value = false
    }
  }).catch(() => {
    // 取消删除
  })
}

// 表单提交
const handleSubmitForm = async () => {
  if (!databaseFormRef.value) return
  
  await databaseFormRef.value.validate()
  loading.value = true
  
  try {
    // 获取数据库类型ID
    const selectedType = dbTypes.value.find(type => type.value === databaseForm.type)
    console.log('当前选择的数据库类型值:', databaseForm.type)
    console.log('找到的数据库类型对象:', selectedType)
    
    let typeId = 1; // 默认值
    
    if (!selectedType) {
      console.warn('未找到匹配的数据库类型，使用默认值')
      if (dbTypes.value.length > 0) {
        typeId = dbTypes.value[0].id;
        console.log('使用第一个可用类型:', dbTypes.value[0]);
      } else {
        console.error('数据库类型列表为空')
        ElMessage.warning('找不到数据库类型信息，使用默认值')
      }
    } else {
      typeId = selectedType.id;
    }
    
    // 构建请求数据 - 只提供db_type字段
    const requestData = {
      name: databaseForm.name,
      db_type_id: parseInt(typeId), // 将db_type改为db_type_id
      host: databaseForm.host,
      port: databaseForm.port,
      username: databaseForm.username,
      password: databaseForm.password,
      database: databaseForm.dbName || null, // 如果为空，则传null
      tags: databaseForm.tags,
      is_production: databaseForm.isProduction
    }
    
    // 添加调试日志
    console.log('最终提交的请求数据:', JSON.stringify(requestData))
    
    if (isEditing.value) {
      // 编辑现有数据库连接
      await updateDatasource(databaseForm.id, requestData)
      ElMessage.success('更新成功')
    } else {
      // 添加新数据库连接
      try {
        console.log('提交的数据:', requestData)
        const res = await createDatasource(requestData)
        console.log('创建数据源响应:', res)
        ElMessage.success('添加成功')
      } catch (err) {
        console.error('添加数据库连接失败:', err)
        console.error('错误类型:', err.constructor.name)
        console.error('错误消息:', err.message)
        
        if (err.response) {
          console.error('响应状态:', err.response.status)
          console.error('响应数据:', err.response.data)
          
          // 详细处理错误
          if (err.response.status === 403) {
            ElMessage.warning('很抱歉，您暂时没有添加数据库连接的权限，请联系系统管理员申请')
          } else if (err.response.status === 400) {
            // 处理400错误，显示详细的验证错误
            console.error('请求格式错误(400):', err.response.data)
            if (typeof err.response.data === 'object') {
              const errorMessages = []
              for (const field in err.response.data) {
                const errMsg = Array.isArray(err.response.data[field]) 
                  ? err.response.data[field].join(', ')
                  : err.response.data[field]
                errorMessages.push(`${field}: ${errMsg}`)
              }
              if (errorMessages.length > 0) {
                ElMessage.error(`验证错误: ${errorMessages.join('; ')}`)
              } else {
                ElMessage.error('数据格式错误，请检查输入')
              }
            } else if (err.response.data) {
              ElMessage.error(`错误: ${err.response.data}`)
            } else {
              ElMessage.error('请求格式错误，请检查输入')
            }
          } else if (err.response.data && err.response.data.message) {
            ElMessage.error(err.response.data.message)
          } else if (err.response.data && err.response.data.detail) {
            ElMessage.error(err.response.data.detail)
          } else {
            // 处理字段验证错误
            const errorMessages = []
            for (const field in err.response.data) {
              errorMessages.push(`${field}: ${err.response.data[field]}`)
            }
            if (errorMessages.length > 0) {
              ElMessage.error(`验证错误: ${errorMessages.join(', ')}`)
            } else {
              ElMessage.error(`请求失败(${err.response.status})，请稍后重试`)
            }
          }
        } else {
          ElMessage.error(`添加数据库连接失败: ${err.message}`)
        }
        return // 出错后直接返回，不关闭对话框
      }
    }
    
    // 重新获取数据库列表
    await fetchDatabases()
    dialogVisible.value = false
  } catch (error) {
    console.error('提交数据库连接信息失败:', error)
    if (error.response) {
      // 处理HTTP错误
      const status = error.response.status
      if (status === 403) {
        ElMessage.warning('很抱歉，您暂时没有访问此资源的权限，请联系系统管理员申请')
      } else if (status === 401) {
        ElMessage.error('身份验证失败，请重新登录')
      } else if (error.response.data && (error.response.data.message || error.response.data.detail)) {
        ElMessage.error(error.response.data.message || error.response.data.detail)
      } else {
        ElMessage.error(`请求失败 (${status})`)
      }
    } else if (error.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('提交失败，请检查表单信息')
    }
  } finally {
    loading.value = false
  }
}

// 测试连接
const handleTestConnection = async () => {
  if (!databaseFormRef.value) return
  
  try {
    await databaseFormRef.value.validate()
    loading.value = true
    
    if (isEditing.value && databaseForm.id) {
      // 已有数据库连接，直接测试
      try {
        await testConnection(databaseForm.id)
        ElMessage.success('连接测试成功')
      } catch (err) {
        // 详细处理测试连接错误
        if (err.response && err.response.data && (err.response.data.message || err.response.data.detail)) {
          ElMessage.error(err.response.data.message || err.response.data.detail)
        } else if (err.response && err.response.status === 403) {
          ElMessage.warning('很抱歉，您暂时没有测试此连接的权限，请联系系统管理员申请')
        } else {
          ElMessage.error('连接测试失败，请检查连接信息')
        }
      }
    } else {
      // 新数据库连接，使用临时连接测试API
      // 获取数据库类型对象
      const selectedType = dbTypes.value.find(type => type.value === databaseForm.type)
      console.log('测试连接 - 当前选择的数据库类型值:', databaseForm.type)
      console.log('测试连接 - 找到的数据库类型对象:', selectedType)
      
      // 设置默认类型ID
      let typeId = 1; // 默认为MySQL (假设ID为1)
      
      if (!selectedType) {
        console.warn('测试连接 - 未找到匹配的数据库类型，使用默认值')
        // 检查是否有默认类型可用
        if (dbTypes.value.length > 0) {
          // 使用第一个可用类型
          typeId = dbTypes.value[0].id;
          console.log('测试连接 - 使用第一个可用类型:', dbTypes.value[0]);
        } else {
          console.error('测试连接 - 数据库类型列表为空')
          ElMessage.warning('找不到数据库类型信息，使用默认值')
        }
      } else {
        typeId = selectedType.id;
      }
      
      const requestData = {
        name: databaseForm.name || '临时连接',
        db_type_id: parseInt(typeId), // 将db_type改为db_type_id
        host: databaseForm.host,
        port: databaseForm.port,
        username: databaseForm.username,
        password: databaseForm.password,
        database: databaseForm.dbName || null // 如果为空，则传null
      }
      
      // 添加调试日志
      console.log('测试连接请求数据:', JSON.stringify(requestData))
      
      try {
        // 直接测试连接，不创建数据源记录
        await testTempConnection(requestData)
        ElMessage.success('连接测试成功')
      } catch (err) {
        console.error('测试连接失败:', err)
        console.error('错误类型:', err.constructor.name)
        console.error('错误消息:', err.message)
        
        // 详细处理测试连接错误
        if (err.response && err.response.status === 403) {
          ElMessage.warning('很抱歉，您暂时没有测试连接的权限，请联系系统管理员申请')
        } else if (err.response && err.response.data && (err.response.data.message || err.response.data.detail)) {
          ElMessage.error(err.response.data.message || err.response.data.detail)
        } else {
          ElMessage.error('连接测试失败，请检查连接信息')
        }
      }
    }
  } catch (error) {
    console.error('连接测试失败:', error)
    ElMessage.error('表单验证失败，请检查必填字段')
  } finally {
    loading.value = false
  }
}

// 标签相关方法
const showTagInput = () => {
  tagInputVisible.value = true
  nextTick(() => {
    tagInputRef.value.focus()
  })
}

const handleTagConfirm = () => {
  if (tagInputValue.value) {
    if (!databaseForm.tags.includes(tagInputValue.value)) {
      databaseForm.tags.push(tagInputValue.value)
    }
  }
  tagInputVisible.value = false
  tagInputValue.value = ''
}

const handleRemoveTag = (tag) => {
  databaseForm.tags = databaseForm.tags.filter(t => t !== tag)
}

// 分页相关方法
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchDatabases()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchDatabases()
}

// 处理行点击
const handleRowClick = async (row) => {
  // 移除行点击处理功能
}

// 更新特定数据库的状态
const updateDbStatus = (dbId, status) => {
  if (!dbId) return
  
  // 更新数据库列表中的状态
  const index = databases.value.findIndex(db => db.id === dbId)
  if (index !== -1) {
    databases.value[index].status = status
  }
}
</script>

<style scoped>
.database-container {
  padding: 20px 0;
}

.top-panel {
  margin-bottom: 20px;
}

.action-col {
  display: flex;
  justify-content: flex-end;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-tags {
  display: flex;
  gap: 10px;
}

.db-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.db-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.tag-input {
  width: 100px;
  margin-right: 10px;
  vertical-align: bottom;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 数据库图标样式 */
.mysql-icon {
  color: #00758f;
}

.postgres-icon {
  color: #336791;
}

.oracle-icon {
  color: #f80000;
}

.sqlserver-icon {
  color: #cc2927;
}

.mongodb-icon {
  color: #4db33d;
}

.redis-icon {
  color: #d82c20;
}

.sqlite-icon {
  color: #0f80cc;
}

.db-icon {
  color: #909399;
}

/* 表格行悬停效果 */
:deep(.el-table__row) {
  cursor: pointer;
  transition: background-color 0.3s;
}

:deep(.el-table__row:hover) {
  background-color: #f0f9ff !important;
}

/* 连接状态标签样式 */
:deep(.el-tag.connecting) {
  animation: blink 1.5s infinite;
}

.status-container {
  min-height: 24px; /* 确保状态容器始终有固定高度 */
  display: flex;
  align-items: center;
}

@keyframes blink {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

/* 连接池状态标签 */
.pool-tag {
  margin-left: 5px;
  font-size: 0.8em;
}

:deep(.el-tag.pool-active) {
  background-color: #ecf5ff;
  color: #409eff;
  border-color: #d9ecff;
}
</style> 