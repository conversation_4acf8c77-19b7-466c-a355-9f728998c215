<template>
  <div class="saved-queries-container">
    <div class="top-panel">
      <el-row :gutter="20">
        <el-col :span="16">
          <!-- The search input is removed -->
        </el-col>
        <el-col :span="8" class="action-col">
          <el-button type="primary" @click="createNewQuery">
            <el-icon><Plus /></el-icon>新建查询
          </el-button>
          <el-button @click="handleRefresh">
            <el-icon><Refresh /></el-icon>刷新
          </el-button>
        </el-col>
      </el-row>
    </div>

    <div class="filter-area">
      <el-select 
        v-model="selectedDatabase" 
        clearable 
        placeholder="按数据库筛选" 
        style="width: 200px"
        @change="handleFilter"
      >
        <el-option
          v-for="db in databases"
          :key="db.id"
          :label="db.name"
          :value="db.id"
        >
          <div class="db-option">
            <el-icon :class="getDbIconClass(db.type)"><Connection /></el-icon>
            <span>{{ db.name }}</span>
          </div>
        </el-option>
      </el-select>
      
      <el-select 
        v-model="selectedTag" 
        clearable 
        placeholder="按标签筛选" 
        style="width: 150px"
        @change="handleFilter"
      >
        <el-option
          v-for="tag in queryTags"
          :key="tag"
          :label="tag"
          :value="tag"
        />
      </el-select>
      
      <el-radio-group v-model="visibilityFilter" @change="handleFilter">
        <el-radio-button label="all">全部</el-radio-button>
        <el-radio-button label="mine">我的</el-radio-button>
        <el-radio-button label="shared">共享</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 表格形式展示查询 -->
    <el-table
      :data="filteredQueries"
      v-loading="loading"
      style="width: 100%"
      row-key="id"
      empty-text="没有匹配的查询"
      border
      stripe
      highlight-current-row
    >
      <el-table-column prop="createdAt" label="创建时间" width="180" sortable>
        <template #default="scope">
          {{ formatDate(scope.row.createdAt, true) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="name" label="查询名称" min-width="150" show-overflow-tooltip sortable />
      
      <el-table-column prop="sqlContent" label="SQL内容" min-width="250" show-overflow-tooltip>
        <template #default="scope">
          <el-tooltip :content="scope.row.sqlContent" placement="top" :hide-after="0">
            <span>{{ truncateSql(scope.row.sqlContent) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      
      <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
      
      <el-table-column prop="owner" label="创建人" width="120" sortable />
      
      <el-table-column prop="database.name" label="数据库" width="150" show-overflow-tooltip sortable>
        <template #default="scope">
          <div class="db-name">
            <el-icon :class="getDbIconClass(scope.row.database.type)"><Connection /></el-icon>
            <span>{{ scope.row.database.name }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="visibility" label="是否公开" width="120" sortable>
        <template #default="scope">
          <el-tag :type="scope.row.visibility === 'private' ? 'info' : 'success'" size="small">
            {{ scope.row.visibility === 'private' ? '私有' : scope.row.visibility === 'public' ? '公开' : '共享' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="tags" label="标签" width="150" show-overflow-tooltip>
        <template #default="scope">
          <div class="table-tags">
            <el-tag 
              v-for="tag in scope.row.tags" 
              :key="tag" 
              size="small"
              class="query-tag"
            >{{ tag }}</el-tag>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button size="small" @click.stop="openQuery(scope.row)">打开</el-button>
          <el-dropdown trigger="click" @click.stop>
            <el-button size="small">
              <el-icon><More /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="duplicateQuery(scope.row)">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-dropdown-item>
                <el-dropdown-item v-if="scope.row.owner === currentUser" @click="editQueryInfo(scope.row)">
                  <el-icon><Setting /></el-icon>编辑
                </el-dropdown-item>
                <el-dropdown-item v-if="scope.row.owner === currentUser" @click="confirmDeleteQuery(scope.row)">
                  <el-icon><Delete /></el-icon>删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <div v-if="filteredQueries.length === 0 && !loading" class="empty-state">
      <el-empty description="未找到保存的查询" />
    </div>

    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalQueries"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 编辑查询信息对话框 -->
    <el-dialog 
      v-model="editDialogVisible" 
      :title="isNewQuery ? '新建查询' : '编辑查询'" 
      width="500px"
    >
      <el-form :model="queryForm" :rules="formRules" ref="queryFormRef" label-width="100px">
        <el-form-item label="查询名称" prop="name">
          <el-input v-model="queryForm.name" placeholder="请输入查询名称" />
        </el-form-item>
        
        <el-form-item label="数据库" prop="databaseId">
          <el-select v-model="queryForm.databaseId" placeholder="请选择数据库" style="width: 100%">
            <el-option
              v-for="db in databases"
              :key="db.id"
              :label="db.name"
              :value="db.id"
            >
              <div class="db-option">
                <el-icon :class="getDbIconClass(db.type)"><Connection /></el-icon>
                <span>{{ db.name }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="SQL内容" prop="sqlContent">
          <el-input 
            v-model="queryForm.sqlContent" 
            type="textarea" 
            :rows="10" 
            placeholder="请输入SQL查询语句"
          />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input 
            v-model="queryForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="查询的详细描述"
          />
        </el-form-item>
        
        <el-form-item label="标签">
          <el-tag
            v-for="tag in queryForm.tags"
            :key="tag"
            closable
            @close="removeQueryTag(tag)"
            class="query-tag"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="tagInputVisible"
            ref="tagInputRef"
            v-model="tagInputValue"
            class="tag-input"
            size="small"
            @keyup.enter="handleTagConfirm"
            @blur="handleTagConfirm"
          />
          <el-button v-else size="small" @click="showTagInput">+ 新标签</el-button>
        </el-form-item>
        
        <el-form-item label="共享设置">
          <el-radio-group v-model="queryForm.visibility">
            <el-radio :label="'private'">私有</el-radio>
            <el-radio :label="'shared'">共享</el-radio>
            <el-radio :label="'public'">公开</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveQueryInfo">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  Connection, Search, Plus, Refresh, Edit, Delete, 
  Setting, CopyDocument, User, Calendar, More
} from '@element-plus/icons-vue'
import { getDatasources } from '@/api/datasource'
import { getSavedQueries, updateSavedQuery, deleteSavedQuery, getSavedQueryById } from '@/api/query'

const router = useRouter()

// 数据库列表（将在实际应用中从API获取）
const databases = ref([])

// 当前用户（将从用户会话获取）
const currentUser = ref('')

// 查询列表（将从API获取）
const savedQueries = ref([])

// 提取所有标签
const queryTags = computed(() => {
  const tags = new Set()
  savedQueries.value.forEach(query => {
    query.tags.forEach(tag => tags.add(tag))
  })
  return Array.from(tags)
})

// 状态变量
const loading = ref(false)
const searchText = ref('')
const selectedDatabase = ref(null)
const selectedTag = ref(null)
const visibilityFilter = ref('all')
const currentPage = ref(1)
const pageSize = ref(12)
const totalQueries = ref(0)
const editDialogVisible = ref(false)
const isNewQuery = ref(false)
const tagInputVisible = ref(false)
const tagInputValue = ref('')
const tagInputRef = ref(null)
const queryFormRef = ref(null)

// 表单数据
const queryForm = reactive({
  id: null,
  name: '',
  description: '',
  sqlContent: '',
  databaseId: null,
  tags: [],
  visibility: 'private'
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入查询名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  databaseId: [
    { required: true, message: '请选择数据库', trigger: 'change' }
  ],
  sqlContent: [
    { required: isNewQuery, message: '请输入SQL内容', trigger: 'blur' }
  ]
}

// 筛选后的查询列表
const filteredQueries = computed(() => {
  let result = [...savedQueries.value]
  
  // 搜索过滤
  if (searchText.value) {
    const searchLower = searchText.value.toLowerCase()
    result = result.filter(query => 
      query.name.toLowerCase().includes(searchLower) || 
      query.sqlContent.toLowerCase().includes(searchLower) ||
      (query.owner && query.owner.toLowerCase().includes(searchLower)) ||
      (query.description && query.description.toLowerCase().includes(searchLower))
    )
  }
  
  // 数据库过滤
  if (selectedDatabase.value) {
    result = result.filter(query => query.database.id === selectedDatabase.value)
  }
  
  // 标签过滤
  if (selectedTag.value) {
    result = result.filter(query => query.tags.includes(selectedTag.value))
  }
  
  // 可见性过滤
  if (visibilityFilter.value === 'mine') {
    result = result.filter(query => query.owner === currentUser.value)
  } else if (visibilityFilter.value === 'shared') {
    result = result.filter(query => 
      query.visibility === 'shared' || query.visibility === 'public'
    )
  } else if (visibilityFilter.value === 'all') {
    result = result.filter(query => 
      query.owner === currentUser.value || // 自己的所有查询
      query.visibility === 'shared' || query.visibility === 'public' // 他人共享和公开的查询
    )
  }
  
  totalQueries.value = result.length
  
  // 分页处理（实际应用中这应该在服务器端完成）
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return result.slice(start, end)
})

// 根据数据库类型获取图标类名
const getDbIconClass = (type) => {
  const typeMap = {
    'MySQL': 'mysql-icon',
    'PostgreSQL': 'postgres-icon',
    'Oracle': 'oracle-icon',
    'SQL Server': 'sqlserver-icon',
    'MongoDB': 'mongodb-icon',
    'Redis': 'redis-icon',
    'SQLite': 'sqlite-icon'
  }
  return typeMap[type] || 'db-icon'
}

// 格式化日期
const formatDate = (dateString, showTime = false) => {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  const options = { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit'
  }
  
  if (showTime) {
    options.hour = '2-digit'
    options.minute = '2-digit'
    options.second = '2-digit'
  }
  
  return date.toLocaleDateString('zh-CN', options).replace(/\//g, '-')
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
}

// 处理筛选
const handleFilter = () => {
  currentPage.value = 1
}

// 处理刷新
const handleRefresh = () => {
  searchText.value = ''
  selectedDatabase.value = null
  selectedTag.value = null
  visibilityFilter.value = 'all'
  currentPage.value = 1
  
  loading.value = true
  fetchSavedQueries()
}

// 打开查询
const openQuery = (query) => {
  // 跳转到查询编辑器页面
  router.push({
    path: '/query',
    query: {
      id: query.id,
      dbId: query.database.id,
      sql: query.sqlContent, // 传递SQL内容
      savedQueryId: query.id  // 传递保存的查询ID
    }
  })
}

// 复制查询
const duplicateQuery = (query) => {
  // 创建副本并保存到后端
  ElMessage.success('正在复制查询...')
  
  // 实际应用中，这里应该调用API保存副本
  // const data = {
  //   name: `${query.name} (复制)`,
  //   description: query.description,
  //   sql_content: query.sqlContent,
  //   database_id: query.database.id,
  //   tags: query.tags,
  //   visibility: query.visibility
  // }
  // try {
  //   await createSavedQuery(data)
  //   ElMessage.success('查询已复制')
  //   fetchSavedQueries()
  // } catch (error) {
  //   console.error('复制查询失败:', error)
  //   ElMessage.error('复制查询失败')
  // }
}

// 创建新查询
const createNewQuery = () => {
  isNewQuery.value = true
  queryForm.id = null
  queryForm.name = ''
  queryForm.description = ''
  queryForm.sqlContent = ''
  queryForm.databaseId = null
  queryForm.tags = []
  queryForm.visibility = 'private'
  
  editDialogVisible.value = true
}

// 编辑查询信息
const editQueryInfo = async (query) => {
  isNewQuery.value = false
  queryForm.id = query.id
  queryForm.name = query.name
  queryForm.description = query.description
  queryForm.databaseId = query.database.id
  queryForm.tags = [...query.tags]
  queryForm.visibility = query.visibility
  
  // 加载SQL内容
  try {
    // 如果已经有SQL内容，直接使用
    if (query.sqlContent) {
      queryForm.sqlContent = query.sqlContent;
    } else {
      // 否则从后端获取详细信息
      const detail = await getSavedQueryById(query.id);
      console.log('获取查询详情响应:', detail);
      
      if (detail) {
        if (detail.sql_content) {
          queryForm.sqlContent = detail.sql_content;
        } else if (detail.data && detail.data.sql_content) {
          queryForm.sqlContent = detail.data.sql_content;
        } else {
          queryForm.sqlContent = '';
          ElMessage.warning('无法获取SQL内容');
        }
      }
    }
  } catch (error) {
    console.error('获取SQL内容失败:', error);
    ElMessage.error('获取SQL内容失败: ' + (error.message || '未知错误'));
    queryForm.sqlContent = '';
  }
  
  editDialogVisible.value = true
}

// 确认删除查询
const confirmDeleteQuery = (query) => {
  ElMessageBox.confirm(
    `确定要删除查询 "${query.name}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    // 调用API删除
    try {
      await deleteSavedQuery(query.id)
      ElMessage.success('删除成功')
      fetchSavedQueries()
    } catch (error) {
      console.error('删除查询失败:', error)
      ElMessage.error('删除查询失败: ' + (error.message || '未知错误'))
    }
  }).catch(() => {
    // 取消删除
  })
}

// 更新现有查询
const updateQuery = async () => {
  // 更新现有查询
  const data = {
    name: queryForm.name,
    description: queryForm.description || '',
    sql_content: queryForm.sqlContent || '',
    tags: queryForm.tags.length > 0 ? queryForm.tags.join(',') : '',
    visibility: queryForm.visibility
  }
  
  try {
    const res = await updateSavedQuery(queryForm.id, data)
    if (res.code === 200 || res.success === true) {
      ElMessage.success('查询更新成功')
      fetchSavedQueries()
    } else {
      ElMessage.error(res.msg || res.message || '更新查询失败')
    }
    return true
  } catch (error) {
    console.error('更新查询失败:', error)
    ElMessage.error('更新查询失败: ' + (error.message || '未知错误'))
    return false
  }
}

// 保存查询信息
const saveQueryInfo = async () => {
  if (!queryFormRef.value) return
  
  try {
    await queryFormRef.value.validate()
    
    if (isNewQuery.value) {
      // 创建新查询功能暂未实现
      ElMessage.warning('创建新查询功能暂未实现')
    } else {
      // 更新现有查询
      const success = await updateQuery()
      if (success) {
        editDialogVisible.value = false
      }
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 标签相关方法
const showTagInput = () => {
  tagInputVisible.value = true
  nextTick(() => {
    tagInputRef.value.focus()
  })
}

const handleTagConfirm = () => {
  if (tagInputValue.value) {
    if (!queryForm.tags.includes(tagInputValue.value)) {
      queryForm.tags.push(tagInputValue.value)
    }
  }
  tagInputVisible.value = false
  tagInputValue.value = ''
}

const removeQueryTag = (tag) => {
  queryForm.tags = queryForm.tags.filter(t => t !== tag)
}

// 分页相关方法
const handleSizeChange = (val) => {
  pageSize.value = val
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 获取保存的查询列表
const fetchSavedQueries = async () => {
  loading.value = true
  
  // 构建查询参数
  const params = {
    keyword: searchText.value || '',
    datasource_id: selectedDatabase.value || '',
    tag: selectedTag.value || '',
    visibility: visibilityFilter.value === 'all' ? '' : visibilityFilter.value
  }
  
  console.log('获取保存的查询，参数:', params)
  
  try {
    const res = await getSavedQueries(params)
    console.log('获取保存的查询响应:', res)
    
    // 处理不同的响应格式
    let data = []
    let total = 0
    
    if (res && res.code === 200 && res.data) {
      if (Array.isArray(res.data)) {
        data = res.data
        total = data.length
      } else if (res.data.items && Array.isArray(res.data.items)) {
        data = res.data.items
        total = res.data.total || data.length
      } else if (res.data.results && Array.isArray(res.data.results)) {
        data = res.data.results
        total = res.data.count || data.length
      }
    } else if (Array.isArray(res)) {
      data = res
      total = res.length
    } else if (res && res.results && Array.isArray(res.results)) {
      data = res.results
      total = res.count || data.length
    }
    
    // 格式化数据
    savedQueries.value = data.map(item => {
      const tags = item.tags ? 
        (typeof item.tags === 'string' ? item.tags.split(',').filter(t => t) : item.tags) : 
        [];
      
      return {
        id: item.id,
        name: item.name,
        description: item.description || '',
        sqlContent: item.sql_content || item.sqlContent || '',
        tags: tags,
        database: {
          id: item.datasource_id || (item.datasource ? item.datasource.id : 0),
          name: item.datasource_name || (item.datasource ? item.datasource.name : '未知数据库'),
          type: item.datasource_type || (item.datasource ? item.datasource.type : 'unknown')
        },
        owner: item.owner || currentUser.value,
        createdAt: item.created_at || item.createdAt || new Date().toISOString(),
        updatedAt: item.updated_at || item.updatedAt || new Date().toISOString(),
        visibility: item.visibility || (item.is_public ? 'public' : 'private')
      }
    })
    
    totalQueries.value = total
    
    // 收集所有标签用于筛选
    const allTags = new Set()
    savedQueries.value.forEach(query => {
      if (query.tags && query.tags.length) {
        query.tags.forEach(tag => allTags.add(tag))
      }
    })
    queryTags.value = Array.from(allTags)
    
    console.log('处理后的查询数据:', savedQueries.value)
    console.log('可用的查询标签:', queryTags.value)
    
  } catch (error) {
    console.error('获取保存的查询失败:', error)
    ElMessage.error(`获取保存的查询失败: ${error.message || '未知错误'}`)
    savedQueries.value = []
    totalQueries.value = 0
  } finally {
    loading.value = false
  }
}

// 截断SQL显示
const truncateSql = (sql) => {
  if (!sql) return ''
  return sql.length > 80 ? sql.substring(0, 80) + '...' : sql
}

// 页面加载时获取数据源和查询列表
onMounted(async () => {
  try {
    // 获取当前登录用户
    // 方法1: 如果后端有获取当前用户的API，使用那个API
    // 尝试调用用户信息API获取当前用户
    try {
      // 如果已经从localStorage或其他地方获取了用户信息，直接使用
      const savedUserInfo = localStorage.getItem('userInfo');
      if (savedUserInfo) {
        try {
          const userInfo = JSON.parse(savedUserInfo);
          currentUser.value = userInfo.username || `user_${userInfo.id}` || 'unknown';
          console.log('[DEBUG] 从localStorage中的userInfo获取到用户名:', currentUser.value);
        } catch (parseError) {
          console.warn('解析userInfo出错:', parseError);
        }
      } else {
        // 如果在本地存储中找不到用户信息，可以设置一个默认值
        // 或者调用后端API获取用户信息
        currentUser.value = 'anonymous'; // 默认值，可以根据需要调整
        console.log('[DEBUG] 未找到用户信息，使用默认值:', currentUser.value);
        
        // 如果有获取用户信息的API，可以在这里调用
        // 例如: const userInfoResponse = await getUserInfo();
        // currentUser.value = userInfoResponse.data.username;
      }
    } catch (userError) {
      console.error('获取用户信息失败:', userError);
      currentUser.value = 'anonymous'; // 失败时的默认值
    }
    
    console.log('[DEBUG] onMounted: 最终设置的当前用户:', currentUser.value);
    
    // 获取数据库列表
    console.log('获取数据库列表...')
    const dbRes = await getDatasources()
    console.log('数据库列表响应:', dbRes)
    
    // 处理不同的响应格式
    if (dbRes) {
      let data = []
      
      if (Array.isArray(dbRes)) {
        data = dbRes
      } else if (dbRes.code === 200 && Array.isArray(dbRes.data)) {
        data = dbRes.data
      } else if (dbRes.results && Array.isArray(dbRes.results)) {
        data = dbRes.results
      } else if (typeof dbRes === 'object') {
        for (const key in dbRes) {
          if (Array.isArray(dbRes[key])) {
            data = dbRes[key]
            break
          }
        }
      }
      
      databases.value = data.map(db => ({
        id: db.id,
        name: db.name,
        type: db.db_type || 'mysql',
        host: db.host,
        port: db.port
      }))
      
      console.log('处理后的数据库列表:', databases.value)
    }
  } catch (error) {
    console.error('获取初始数据失败:', error)
    ElMessage.error(`获取初始数据失败: ${error.message || '未知错误'}`)
  }
  
  // 获取保存的查询
  fetchSavedQueries()
})
</script>

<style scoped>
.saved-queries-container {
  padding: 20px 0;
}

.top-panel {
  margin-bottom: 20px;
}

.action-col {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.filter-area {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.empty-state {
  margin: 40px 0;
  text-align: center;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.db-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.db-name {
  display: flex;
  align-items: center;
  gap: 5px;
}

.table-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-height: 40px;
  overflow: hidden;
}

.query-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

.tag-input {
  width: 100px;
  margin-right: 10px;
  vertical-align: bottom;
}

/* 数据库图标样式 */
.mysql-icon {
  color: #00758f;
}

.postgres-icon {
  color: #336791;
}

.oracle-icon {
  color: #f80000;
}

.sqlserver-icon {
  color: #cc2927;
}

.mongodb-icon {
  color: #4db33d;
}

.redis-icon {
  color: #d82c20;
}

.sqlite-icon {
  color: #0f80cc;
}

.db-icon {
  color: #909399;
}
</style> 