<template>
  <div class="profile-container" v-loading="loading">
    <div class="page-header">
      <h2>个人信息</h2>
      <div class="header-actions">
        <el-button v-if="userInfo.is_superuser" type="success" @click="openCreateUserDialog">
          <el-icon><Plus /></el-icon>创建用户
        </el-button>
        <el-button v-if="userInfo.is_superuser" type="primary" @click="fetchUsersList">
          <el-icon><Refresh /></el-icon>刷新用户列表
        </el-button>
      </div>
    </div>
    
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <span>个人信息</span>
          <div class="header-actions">
            <!-- 只有超级管理员可以编辑个人信息 -->
            <el-button v-if="userInfo.is_superuser" type="primary" size="small" @click="editProfile">
              <el-icon><Edit /></el-icon>编辑
            </el-button>
            <!-- 只有超级管理员可以修改密码 -->
            <el-button v-if="userInfo.is_superuser" type="warning" size="small" @click="changePassword">
              <el-icon><Lock /></el-icon>修改密码
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="profile-content">
        <div class="avatar-container">
          <el-avatar :size="100" :style="{ backgroundColor: getUserAvatarColor(userInfo.username) }">
            {{ getUserFirstLetter(userInfo.username) }}
          </el-avatar>
          <div class="avatar-name">{{ userInfo.username }}</div>
          <div class="avatar-role">{{ userInfo.role }}</div>
        </div>
        
        <!-- 简化为只显示用户名 -->
        <el-descriptions class="user-info" :column="1" border>
          <el-descriptions-item label="用户名">{{ userInfo.username }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
    
    <!-- 用户列表区域 -->
    <el-card v-if="userInfo.is_superuser" class="profile-card">
      <template #header>
        <div class="card-header">
          <span>用户列表</span>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="fetchUsersList">
              <el-icon><Refresh /></el-icon>刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <div v-if="usersList.length === 0" class="empty-list">
        <p>暂无用户数据，请点击刷新按钮重新加载</p>
      </div>
      
      <el-table v-else :data="usersList" style="width: 100%" border stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" width="150" />
        <el-table-column label="真实姓名" width="150">
          <template #default="scope">
            {{ scope.row.first_name }} {{ scope.row.last_name }}
          </template>
        </el-table-column>
        <el-table-column prop="email" label="邮箱" />
        <el-table-column label="角色" width="120">
          <template #default="scope">
            <el-tag v-if="scope.row.is_superuser" type="danger">超级管理员</el-tag>
            <el-tag v-else type="info">普通用户</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.is_active" type="success">激活</el-tag>
            <el-tag v-else type="info">未激活</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button 
              size="small" 
              type="primary" 
              @click="openEditRoleDialog(scope.row)"
              :disabled="scope.row.id === userInfo.id">
              编辑角色
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="confirmDeleteUser(scope.row)"
              :disabled="scope.row.id === userInfo.id">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 编辑个人信息对话框 (仅管理员) -->
    <el-dialog v-model="editDialogVisible" title="编辑个人信息" width="500px">
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="editForm.username" disabled />
        </el-form-item>
        
        <el-form-item label="真实姓名">
          <el-input v-model="editForm.realName" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateProfile" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 修改密码对话框 (仅管理员) -->
    <el-dialog v-model="passwordDialogVisible" title="修改密码" width="500px">
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="120px">
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input v-model="passwordForm.oldPassword" type="password" show-password />
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password />
        </el-form-item>
        
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPasswordChange" :loading="loading">确认修改</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 创建用户对话框 -->
    <el-dialog v-model="createUserDialogVisible" title="创建新用户" width="500px">
      <div v-if="usersList.length > 0" class="warning-message">
        <p>注意：系统中当前已有 {{ usersList.length }} 个用户账号</p>
        <p>请确保用户名不重复，否则将无法创建成功</p>
      </div>
      
      <el-form :model="newUserForm" :rules="newUserRules" ref="newUserFormRef" label-width="120px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="newUserForm.username" />
        </el-form-item>
        
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="newUserForm.realName" />
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input v-model="newUserForm.password" type="password" show-password />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="newUserForm.confirmPassword" type="password" show-password />
        </el-form-item>
        
        <el-form-item label="用户权限">
          <el-radio-group v-model="newUserForm.roleType">
            <el-radio :label="'normal'">普通用户</el-radio>
            <el-radio :label="'superadmin'">超级管理员</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createUserDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitCreateUser" :loading="loading">创建</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 编辑用户角色对话框 -->
    <el-dialog v-model="editRoleDialogVisible" title="编辑用户角色" width="500px">
      <el-form :model="editRoleForm" label-width="120px">
        <el-form-item label="用户名">
          <el-input v-model="editRoleForm.username" disabled />
        </el-form-item>
        
        <el-form-item label="用户角色">
          <el-radio-group v-model="editRoleForm.roleType">
            <el-radio :label="'normal'">普通用户</el-radio>
            <el-radio :label="'superadmin'">超级管理员</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editRoleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateUserRole" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Plus, User, Lock, Refresh } from '@element-plus/icons-vue'
import { getCurrentUser, updateUser, changePassword as apiChangePassword, createUser, getUsers, deleteUser as apiDeleteUser } from '@/api/user'
import { useUserStore } from '@/store/user'

// 使用Pinia store
const userStore = useUserStore()

// 用户列表
const usersList = ref([])

// 用户信息
const userInfo = reactive({
  username: '',
  realName: '',
  email: '',
  role: '',
  is_superuser: false,
  is_staff: false
})

// 编辑表单
const editForm = reactive({
  username: '',
  realName: ''
})

// 密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 新用户表单
const newUserForm = reactive({
  username: '',
  realName: '',
  password: '',
  confirmPassword: '',
  roleType: 'normal'  // 默认为普通用户
})

// 密码表单验证规则
const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 新用户表单验证规则
const newUserRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, message: '用户名长度至少为3个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== newUserForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 对话框可见性
const editDialogVisible = ref(false)
const passwordDialogVisible = ref(false)
const createUserDialogVisible = ref(false)
const editRoleDialogVisible = ref(false)

// 表单引用
const passwordFormRef = ref(null)
const newUserFormRef = ref(null)

// 编辑角色表单
const editRoleForm = reactive({
  id: null,
  username: '',
  roleType: 'normal'
})

// 加载状态
const loading = ref(false)

// 获取用户名首字母
const getUserFirstLetter = (username) => {
  if (!username) return '?'
  return username.charAt(0).toUpperCase()
}

// 根据用户名生成头像背景颜色
const getUserAvatarColor = (username) => {
  if (!username) return '#909399'
  
  // 简单的哈希算法生成颜色
  let hash = 0
  for (let i = 0; i < username.length; i++) {
    hash = username.charCodeAt(i) + ((hash << 5) - hash)
  }
  
  // 转换为HSL颜色，保持饱和度和亮度固定，只变化色相
  const hue = Math.abs(hash % 360)
  return `hsl(${hue}, 70%, 60%)`
}

// 编辑个人信息
const editProfile = () => {
  // 复制用户信息到编辑表单
  Object.keys(editForm).forEach(key => {
    if (key in userInfo) {
      editForm[key] = userInfo[key]
    }
  })
  
  editDialogVisible.value = true
}

// 更新个人信息
const updateProfile = async () => {
  loading.value = true
  
  try {
    // 调用API更新信息
    await updateUser(userInfo.id, editForm)
    
    // 更新本地状态
    Object.keys(editForm).forEach(key => {
      if (key in userInfo) {
        userInfo[key] = editForm[key]
      }
    })
    
    // 重新获取用户信息
    await fetchUserInfo()
    
    ElMessage.success('个人信息已更新')
    editDialogVisible.value = false
  } catch (error) {
    console.error('更新个人信息失败:', error)
    ElMessage.error('更新个人信息失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 修改密码
const changePassword = () => {
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  passwordDialogVisible.value = true
}

// 提交密码修改
const submitPasswordChange = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    loading.value = true
    
    // 调用API修改密码
    await apiChangePassword({
      old_password: passwordForm.oldPassword,
      new_password: passwordForm.newPassword
    })
    
    ElMessage.success('密码修改成功')
    passwordDialogVisible.value = false
  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error('修改密码失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 打开创建用户对话框
const openCreateUserDialog = () => {
  // 重置表单
  newUserForm.username = '';
  newUserForm.realName = '';
  newUserForm.password = '';
  newUserForm.confirmPassword = '';
  newUserForm.roleType = 'normal';
  
  createUserDialogVisible.value = true
}

// 创建新用户
const submitCreateUser = async () => {
  if (!newUserFormRef.value) return
  
  try {
    await newUserFormRef.value.validate()
    loading.value = true
    
    // 根据角色类型设置权限
    let is_superuser = false;
    let is_staff = false;
    
    if (newUserForm.roleType === 'superadmin') {
      is_superuser = true;
      is_staff = true; // 超级管理员保留staff权限以保持兼容性
    }
    
    // 准备提交的数据
    const userData = {
      username: newUserForm.username,
      password: newUserForm.password,
      first_name: newUserForm.realName?.split(' ')[0] || '',
      last_name: newUserForm.realName?.split(' ')[1] || '',
      is_superuser: is_superuser,
      is_staff: is_staff,
      is_active: true, // 确保用户被激活
      email: `${newUserForm.username}@example.com` // 提供一个默认邮箱
    }
    
    console.log('创建用户请求数据:', userData);
    
    // 调用API创建用户
    try {
      const response = await createUser(userData);
      console.log('创建用户成功:', response);
      ElMessage.success('用户创建成功');
      createUserDialogVisible.value = false;
      
      // 如果需要，可以在这里刷新用户列表
      await fetchUsersList()
    } catch (createError) {
      console.error('创建用户API错误:', createError);
      
      // 改进错误处理逻辑，避免显示Django错误页面
      if (createError.response) {
        const status = createError.response.status;
        
        // 处理常见的HTTP错误
        if (status === 404) {
          ElMessage.error('创建用户失败: API接口未找到，请联系系统管理员');
        } else if (status === 403) {
          ElMessage.error('创建用户失败: 您没有权限执行此操作');
        } else if (status === 401) {
          ElMessage.error('创建用户失败: 身份验证失败，请重新登录');
        } else if (status === 400) {
          // 处理400错误，通常包含表单验证错误
          try {
            const errorData = createError.response.data;
            
            if (typeof errorData === 'object') {
              // 特别处理用户名已存在的情况
              if (errorData.username && 
                  (errorData.username.includes('已存在') || 
                  errorData.username.includes('already exists'))) {
                ElMessage.error(`用户名 "${newUserForm.username}" 已存在，请尝试其他用户名`);
                return;
              }
              
              // 处理其他字段错误
              const errorMessages = [];
              for (const field in errorData) {
                const fieldErrors = errorData[field];
                if (Array.isArray(fieldErrors)) {
                  errorMessages.push(`${field}: ${fieldErrors.join(', ')}`);
                } else if (typeof fieldErrors === 'string') {
                  errorMessages.push(`${field}: ${fieldErrors}`);
                } else if (typeof fieldErrors === 'object') {
                  errorMessages.push(`${field}: ${JSON.stringify(fieldErrors)}`);
                }
              }
              
              if (errorMessages.length > 0) {
                ElMessage.error(`创建用户失败: ${errorMessages.join('; ')}`);
              } else {
                ElMessage.error('创建用户失败: 表单验证错误');
              }
            } else if (typeof errorData === 'string') {
              ElMessage.error(`创建用户失败: ${errorData}`);
            } else {
              ElMessage.error('创建用户失败: 表单验证错误');
            }
          } catch (e) {
            ElMessage.error('创建用户失败: 无法解析错误信息');
          }
        } else {
          // 处理其他HTTP错误
          ElMessage.error(`创建用户失败: 服务器返回错误 (${status})`);
        }
      } else if (createError.message) {
        // 处理网络错误等
        if (createError.message.includes('Network Error')) {
          ElMessage.error('创建用户失败: 网络错误，请检查您的网络连接');
        } else {
          ElMessage.error(`创建用户失败: ${createError.message}`);
        }
      } else {
        ElMessage.error('创建用户失败: 未知错误');
      }
    }
  } catch (error) {
    console.error('表单验证失败:', error);
    ElMessage.error('请检查表单填写是否正确');
  } finally {
    loading.value = false;
  }
}

// 获取用户信息
const fetchUserInfo = async () => {
  loading.value = true
  
  try {
    // 获取当前用户信息
    const response = await getCurrentUser()
    
    // 更新用户信息
    Object.assign(userInfo, {
      id: response.id,
      username: response.username,
      realName: `${response.first_name || ''} ${response.last_name || ''}`.trim() || response.username,
      email: response.email,
      role: response.is_superuser ? '系统管理员' : '普通用户',
      is_superuser: response.is_superuser,
      is_staff: response.is_staff
    })
    
    // 更新用户store
    await userStore.getUserInfo()
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 获取用户列表
const fetchUsersList = async () => {
  try {
    console.log('开始获取用户列表...');
    const response = await getUsers();
    console.log('用户列表API响应:', response);
    
    if (Array.isArray(response)) {
      usersList.value = response;
      console.log('用户列表是数组，长度:', response.length);
    } else if (response.results) {
      usersList.value = response.results;
      console.log('用户列表包含在results属性中，长度:', response.results.length);
    } else {
      usersList.value = [];
      console.log('用户列表格式不正确或为空，设置为空数组');
    }
    console.log('获取到的用户列表:', usersList.value);
    
    // 检查是否有重复的用户名
    const usernames = {};
    const duplicates = [];
    
    usersList.value.forEach(user => {
      if (usernames[user.username]) {
        duplicates.push(user.username);
      } else {
        usernames[user.username] = true;
      }
    });
    
    if (duplicates.length > 0) {
      console.warn('发现重复的用户名:', duplicates);
    }
    
    // 检查当前用户权限
    console.log('当前用户是否为超级管理员:', userInfo.is_superuser);
    console.log('用户列表显示条件:', userInfo.is_superuser && usersList.value.length > 0);
  } catch (error) {
    console.error('获取用户列表失败:', error);
    if (error.response) {
      console.error('错误响应:', error.response.data);
      console.error('状态码:', error.response.status);
    }
  }
}

// 页面加载时获取用户信息
onMounted(async () => {
  await fetchUserInfo();
  
  // 确保用户信息加载完成后再获取用户列表
  if (userInfo.is_superuser) {
    console.log('当前用户是超级管理员，正在获取用户列表...');
    await fetchUsersList(); // 获取用户列表
  } else {
    console.log('当前用户不是超级管理员，不获取用户列表');
  }
})

// 打开编辑角色对话框
const openEditRoleDialog = (user) => {
  editRoleForm.id = user.id;
  editRoleForm.username = user.username;
  editRoleForm.roleType = user.is_superuser ? 'superadmin' : 'normal';
  
  editRoleDialogVisible.value = true;
}

// 更新用户角色
const updateUserRole = async () => {
  try {
    loading.value = true;
    
    // 根据角色类型设置权限
    const is_superuser = editRoleForm.roleType === 'superadmin';
    const is_staff = is_superuser; // 超级管理员保留staff权限以保持兼容性
    
    // 调用API更新用户角色
    await updateUser(editRoleForm.id, {
      is_superuser,
      is_staff
    });
    
    // 关闭对话框
    editRoleDialogVisible.value = false;
    
    // 刷新用户列表
    await fetchUsersList();
    
    ElMessage.success('用户角色已更新');
  } catch (error) {
    console.error('更新用户角色失败:', error);
    ElMessage.error('更新用户角色失败: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
}

// 确认删除用户
const confirmDeleteUser = (user) => {
  ElMessageBox.confirm(
    `确定要删除用户 "${user.username}" 吗？此操作不可恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(() => {
      deleteUserById(user.id);
    })
    .catch(() => {
      // 取消删除，不做任何操作
    });
}

// 删除用户
const deleteUserById = async (userId) => {
  try {
    loading.value = true;
    
    // 调用API删除用户
    await apiDeleteUser(userId);
    
    // 刷新用户列表
    await fetchUsersList();
    
    ElMessage.success('用户已删除');
  } catch (error) {
    console.error('删除用户失败:', error);
    ElMessage.error('删除用户失败: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.profile-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.profile-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.avatar-name {
  margin-top: 10px;
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.avatar-role {
  margin-top: 5px;
  font-size: 14px;
  color: #909399;
}

.user-info {
  width: 100%;
  max-width: 500px;
  margin-top: 20px;
}

.empty-list {
  text-align: center;
  padding: 20px;
  color: #909399;
}

.warning-message {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #fef6e4;
  border-left: 4px solid #f5a623;
  color: #666;
}

/* 新增：让头像更突出 */
.el-avatar {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 3px solid #fff;
}

/* 新增：简洁版个人信息卡片 */
.profile-content {
  background-color: #f9fafc;
  border-radius: 8px;
  padding: 30px;
}

/* 新增：让标题更显眼 */
.card-header span {
  font-size: 18px;
  font-weight: 500;
  color: #409EFF;
}
</style> 