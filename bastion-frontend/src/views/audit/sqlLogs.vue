<template>
  <div class="audit-logs-container">
    <div class="search-panel">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="SQL内容">
          <el-input 
            v-model="searchForm.keyword" 
            placeholder="搜索SQL内容" 
            clearable 
            @keyup.enter="handleSearch"
            @clear="handleSearch" 
          />
        </el-form-item>
        
        <el-form-item label="数据源">
          <el-select 
            v-model="searchForm.databaseId" 
            placeholder="选择数据源" 
            clearable
            fit-input-width
            style="width: 140px"
            @change="handleSearch"
          >
            <el-option
              v-for="db in databases"
              :key="db.id"
              :label="db.name"
              :value="db.id"
            >
              <div class="db-option">
                <el-icon :class="getDbIconClass(db.type)"><Connection /></el-icon>
                <span>{{ db.name }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="风险等级">
          <el-select 
            v-model="searchForm.riskLevel" 
            placeholder="选择风险等级" 
            clearable
            fit-input-width
            style="width: 140px"
            @change="handleSearch"
          >
            <el-option
              v-for="level in riskLevels"
              :key="level.value"
              :label="level.label"
              :value="level.value"
            >
              <el-tag :type="getRiskTagType(level.value)" size="small">
                {{ level.label }}
              </el-tag>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="时间范围">
          <el-select 
            v-model="searchForm.dateType" 
            placeholder="选择时间范围" 
            style="width: 140px"
            fit-input-width
            @change="handleDateTypeChange"
          >
            <el-option label="今天" value="today" />
            <el-option label="昨天" value="yesterday" />
            <el-option label="最近7天" value="week" />
            <el-option label="最近30天" value="month" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="searchForm.dateType === 'custom'">
          <el-date-picker
            v-model="searchForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleSearch"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>查询
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>重置
          </el-button>
          <el-button type="success" @click="exportLogs">
            <el-icon><Download /></el-icon>导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="logs-table-card">
      <template #header>
        <div class="card-header">
          <span>SQL审计日志</span>
          <div class="header-right">
            <el-tag v-if="totalRiskCount.high > 0" type="danger">
              高风险: {{ totalRiskCount.high }}
            </el-tag>
            <el-tag v-if="totalRiskCount.medium > 0" type="warning">
              中风险: {{ totalRiskCount.medium }}
            </el-tag>
            <el-tag v-if="totalRiskCount.low > 0" type="info">
              低风险: {{ totalRiskCount.low }}
            </el-tag>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="sqlLogs" 
        style="width: 100%" 
        border 
        v-loading="loading"
        :row-class-name="getRowClass"
      >
        <el-table-column type="expand">
          <template #default="props">
            <div class="expanded-row">
              <el-descriptions title="详细信息" :column="3" border>
                <el-descriptions-item label="执行用户">{{ props.row.username }}</el-descriptions-item>
                <el-descriptions-item label="执行时间">
                  {{ formatDateTime(props.row.execute_time) }}
                </el-descriptions-item>
                <el-descriptions-item label="IP地址">{{ props.row.client_ip }}</el-descriptions-item>
                <el-descriptions-item label="数据库">{{ props.row.database.name }}</el-descriptions-item>
                <el-descriptions-item label="风险等级">
                  <el-tag :type="getRiskTagType(props.row.risk_level)">
                    {{ getRiskLevelLabel(props.row.risk_level) }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="影响行数">
                  {{ props.row.affect_rows || 0 }}
                </el-descriptions-item>
                <el-descriptions-item label="SQL内容" :span="3">
                  <pre class="sql-content">{{ formatSqlContent(props.row) }}</pre>
                </el-descriptions-item>
                <el-descriptions-item v-if="props.row.risk_detail" label="风险详情" :span="3">
                  <div class="risk-details">
                    <el-alert
                      :title="props.row.risk_detail"
                      :type="getRiskTagType(props.row.risk_level)"
                      show-icon
                      :closable="false"
                      class="risk-alert"
                    />
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="execute_time" label="执行时间" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.execute_time) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="username" label="执行用户" width="120" />
        
        <el-table-column prop="database.name" label="数据源" width="160">
          <template #default="scope">
            <div class="db-name-cell">
              <el-icon :class="getDbIconClass(scope.row.database.type)"><Connection /></el-icon>
              <span>{{ scope.row.database.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="affect_rows" label="影响行数" width="100" />
        
        <el-table-column prop="risk_level" label="风险等级" width="100">
          <template #default="scope">
            <el-tag :type="getRiskTagType(scope.row.risk_level)">
              {{ getRiskLevelLabel(scope.row.risk_level) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="sql_content" label="SQL内容">
          <template #default="scope">
            <div class="truncated-content">{{ truncateText(formatSqlContent(scope.row), 50) }}</div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button 
              size="small" 
              type="primary" 
              @click="viewDetails(scope.row)"
              :icon="View"
            >详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalLogs"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="SQL审计详情"
      width="70%"
    >
      <el-descriptions v-if="selectedLog" title="基本信息" :column="3" border>
        <el-descriptions-item label="执行用户">{{ selectedLog.username }}</el-descriptions-item>
        <el-descriptions-item label="执行时间">
          {{ formatDateTime(selectedLog.execute_time) }}
        </el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ selectedLog.client_ip }}</el-descriptions-item>
        <el-descriptions-item label="数据库">{{ selectedLog.database.name }}</el-descriptions-item>
        <el-descriptions-item label="风险等级">
          <el-tag :type="getRiskTagType(selectedLog.risk_level)">
            {{ getRiskLevelLabel(selectedLog.risk_level) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="影响行数">
          {{ selectedLog.affect_rows || 0 }}
        </el-descriptions-item>
      </el-descriptions>
      
      <div v-if="selectedLog" class="detail-sql">
        <div class="detail-section-title">SQL内容</div>
        <pre class="sql-content">{{ formatSqlContent(selectedLog) }}</pre>
      </div>
      
      <div v-if="selectedLog && selectedLog.risk_detail" class="detail-risk">
        <div class="detail-section-title">风险详情</div>
        <div class="risk-details">
          <el-alert
            :title="selectedLog.risk_detail"
            :type="getRiskTagType(selectedLog.risk_level)"
            show-icon
            :closable="false"
            class="risk-alert"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Connection, Search, Refresh, Download, View
} from '@element-plus/icons-vue'
import { getSqlAuditLogs, exportSqlAuditLogs, getAuditRules, checkSqlWithRules } from '../../api/audit'
import { getDatasources as fetchDatasources } from '../../api/datasource'

// 数据库列表
const databases = ref([])

// 审计规则列表
const auditRules = ref([])

// 风险等级
const riskLevels = [
  { value: 'high', label: '高风险' },
  { value: 'medium', label: '中风险' },
  { value: 'low', label: '低风险' },
  { value: 'none', label: '无风险' }
]

// 状态变量
const loading = ref(false)
const sqlLogs = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalLogs = ref(0)
const detailDialogVisible = ref(false)
const selectedLog = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  databaseId: null,
  riskLevel: null,
  dateType: 'today',
  timeRange: null
})

// 风险统计
const totalRiskCount = reactive({
  high: 0,
  medium: 0,
  low: 0
})

// 根据数据库类型获取图标类名
const getDbIconClass = (type) => {
  const typeMap = {
    'MySQL': 'mysql-icon',
    'PostgreSQL': 'postgres-icon',
    'Oracle': 'oracle-icon',
    'SQL Server': 'sqlserver-icon',
    'MongoDB': 'mongodb-icon',
    'Redis': 'redis-icon',
    'SQLite': 'sqlite-icon'
  }
  return typeMap[type] || 'db-icon'
}

// 获取风险等级标签类型
const getRiskTagType = (level) => {
  const typeMap = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'info',
    'none': 'success'
  }
  return typeMap[level] || 'info'
}

// 获取风险等级标签文本
const getRiskLevelLabel = (level) => {
  const labelMap = {
    'high': '高风险',
    'medium': '中风险',
    'low': '低风险',
    'none': '无风险'
  }
  return labelMap[level] || '未知'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  
  const date = new Date(dateTime)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 截断文本
const truncateText = (text, maxLength) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// 获取行的类名（为不同风险等级设置不同样式）
const getRowClass = ({ row }) => {
  return `risk-level-${row.risk_level}`
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchSqlLogs()
}

// 重置搜索
const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.databaseId = null
  searchForm.riskLevel = null
  searchForm.dateType = 'today'
  searchForm.timeRange = null
  
  handleSearch()
}

// 查看详情
const viewDetails = (log) => {
  console.log('查看日志详情:', log)
  selectedLog.value = log
  detailDialogVisible.value = true
}

// 表格列内容渲染前处理
const formatSqlContent = (row) => {
  if (!row.sql_content && (row.content || row.sqlContent)) {
    console.log('自动修复SQL内容字段:', row.id)
    // 如果sql_content为空但有其他字段，自动修复
    row.sql_content = row.content || row.sqlContent
  }
  return row.sql_content || '无SQL内容'
}

// 获取审计规则
const fetchAuditRules = async () => {
  try {
    const res = await getAuditRules({ is_enabled: true });
    console.log('获取审计规则响应:', res);
    
    if (res && res.data) {
      if (Array.isArray(res.data)) {
        auditRules.value = res.data;
      } else if (res.data.items && Array.isArray(res.data.items)) {
        auditRules.value = res.data.items;
      } else if (res.data.results && Array.isArray(res.data.results)) {
        auditRules.value = res.data.results;
      }
    }
    
    console.log('处理后的审计规则:', auditRules.value);
  } catch (error) {
    console.error('获取审计规则失败:', error);
    ElMessage.error('获取审计规则失败: ' + (error.message || '未知错误'));
  }
};

// 检查SQL是否符合审计规则
const checkSqlAgainstRules = async (sql, databaseId) => {
  try {
    const res = await checkSqlWithRules({
      sql_content: sql,
      database_id: databaseId
    });
    
    console.log('SQL审计检查结果:', res);
    
    if (res && res.data) {
      return {
        risk_level: res.data.risk_level || 'none',
        risk_detail: res.data.risk_detail || '',
        matched_rules: res.data.matched_rules || []
      };
    }
    
    return { risk_level: 'none', risk_detail: '', matched_rules: [] };
  } catch (error) {
    console.error('检查SQL审计规则失败:', error);
    return { risk_level: 'none', risk_detail: '审计规则检查失败', matched_rules: [] };
  }
};

// 处理SQL日志数据，确保每条SQL都经过审计规则检查
const processSqlLogs = async (logs) => {
  const processedLogs = [];
  
  for (const log of logs) {
    // 确保所有必要字段都有值
    if (!log.database || typeof log.database !== 'object') {
      log.database = {
        id: null,
        name: '未知数据库',
        type: 'unknown'
      };
    }
    
    // 检查SQL内容
    if (log.sql_content === undefined || log.sql_content === null) {
      console.warn('发现记录缺少SQL内容:', log);
      // 尝试从其他可能的字段获取
      log.sql_content = log.content || log.sqlContent || '未知SQL内容';
    }
    
    // 如果没有风险等级或风险等级为none，重新检查SQL
    if (!log.risk_level || log.risk_level === 'none') {
      const checkResult = await checkSqlAgainstRules(log.sql_content, log.database.id);
      log.risk_level = checkResult.risk_level;
      log.risk_detail = checkResult.risk_detail;
      log.matched_rules = checkResult.matched_rules;
      console.log(`重新检查SQL[${log.id}]的风险等级:`, log.risk_level);
    }
    
    processedLogs.push(log);
  }
  
  return processedLogs;
};

// 导出日志
const exportLogs = async () => {
  try {
    ElMessage.success('正在导出SQL审计日志')
    
    const params = {
      keyword: searchForm.keyword,
      database_id: searchForm.databaseId,
      risk_level: searchForm.riskLevel,
      start_date: searchForm.timeRange ? searchForm.timeRange[0] : null,
      end_date: searchForm.timeRange ? searchForm.timeRange[1] : null
    }
    
    const response = await exportSqlAuditLogs(params)
    
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `SQL审计日志_${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('SQL审计日志导出成功')
  } catch (error) {
    console.error('导出SQL审计日志失败:', error)
    ElMessage.error('导出SQL审计日志失败: ' + (error.message || '未知错误'))
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchSqlLogs()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchSqlLogs()
}

// 获取SQL审计日志
const fetchSqlLogs = async () => {
  loading.value = true;
  
  try {
    const params = {
      keyword: searchForm.keyword,
      database_id: searchForm.databaseId,
      risk_level: searchForm.riskLevel,
      start_date: searchForm.timeRange ? searchForm.timeRange[0] : null,
      end_date: searchForm.timeRange ? searchForm.timeRange[1] : null,
      page: currentPage.value,
      limit: pageSize.value
    };
    
    console.log('查询SQL审计日志, 参数:', params);
    
    const res = await getSqlAuditLogs(params);
    console.log('SQL审计日志API响应:', res);
    
    // 处理响应数据，支持多种格式
    if (res) {
      let logsData = [];
      let totalCount = 0;
      let riskCountData = { high: 0, medium: 0, low: 0 };
      
      // 直接包含 items 的格式
      if (res.items && Array.isArray(res.items)) {
        logsData = res.items;
        totalCount = res.total || res.items.length;
        
        if (res.risk_count) {
          riskCountData = {
            high: res.risk_count.high || 0,
            medium: res.risk_count.medium || 0,
            low: res.risk_count.low || 0
          };
        }
        
        console.log('数据格式1 - 直接包含 items：', res.items.length, '条记录');
      }
      // 包含在 data 中的格式
      else if (res.data) {
        if (res.data.items && Array.isArray(res.data.items)) {
          logsData = res.data.items;
          totalCount = res.data.total || res.data.items.length;
          
          if (res.data.risk_count) {
            riskCountData = {
              high: res.data.risk_count.high || 0,
              medium: res.data.risk_count.medium || 0,
              low: res.data.risk_count.low || 0
            };
          }
          
          console.log('数据格式2 - data.items：', res.data.items.length, '条记录');
        }
        // data 直接是数组
        else if (Array.isArray(res.data)) {
          logsData = res.data;
          totalCount = res.data.length;
          
          console.log('数据格式3 - data是数组：', res.data.length, '条记录');
        }
        // Django REST Framework 分页格式
        else if (res.data.results && Array.isArray(res.data.results)) {
          logsData = res.data.results;
          totalCount = res.data.count || res.data.results.length;
          
          console.log('数据格式4 - DRF分页格式：', res.data.results.length, '条记录');
        }
        else {
          console.warn('响应数据格式不符合预期:', res);
        }
      }
      else {
        console.warn('响应数据结构不符合预期:', res);
      }
      
      // 对SQL日志进行处理，确保每条SQL都经过审计规则检查
      logsData = await processSqlLogs(logsData);
      
      // 根据风险等级筛选 - 如果选择了风险等级，确保只显示匹配的
      if (searchForm.riskLevel) {
        logsData = logsData.filter(log => log.risk_level === searchForm.riskLevel);
        totalCount = logsData.length; // 更新总数
        console.log(`按风险等级 ${searchForm.riskLevel} 筛选后有 ${logsData.length} 条记录`);
      }
      
      // 重新计算风险统计
      const newRiskCount = {
        high: logsData.filter(log => log.risk_level === 'high').length,
        medium: logsData.filter(log => log.risk_level === 'medium').length,
        low: logsData.filter(log => log.risk_level === 'low').length
      };
      
      // 将处理后的数据赋值给视图变量
      sqlLogs.value = logsData;
      totalLogs.value = totalCount;
      Object.assign(totalRiskCount, newRiskCount);
      
      console.log('处理后的SQL审计日志数据:', sqlLogs.value);
      console.log('风险统计:', totalRiskCount);
    } else {
      sqlLogs.value = [];
      totalLogs.value = 0;
    }
  } catch (error) {
    console.error('获取SQL审计日志失败:', error);
    ElMessage.error('获取SQL审计日志失败: ' + (error.message || '未知错误'));
    sqlLogs.value = [];
    totalLogs.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理日期类型变化
const handleDateTypeChange = (type) => {
  console.log('时间范围选择:', type)
  
  if (type === 'custom') {
    // 自定义时间范围，清空当前选择，让用户选择
    searchForm.timeRange = null
    return
  }
  
  // 获取当前日期
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  
  // 根据选择的类型设置日期范围
  if (type === 'today') {
    // 今天
    const todayStr = formatDateForAPI(today)
    searchForm.timeRange = [todayStr, todayStr]
  } else if (type === 'yesterday') {
    // 昨天
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    const yesterdayStr = formatDateForAPI(yesterday)
    searchForm.timeRange = [yesterdayStr, yesterdayStr]
  } else if (type === 'week') {
    // 最近7天
    const weekAgo = new Date(today)
    weekAgo.setDate(weekAgo.getDate() - 6) // 今天 + 过去6天 = 7天
    searchForm.timeRange = [formatDateForAPI(weekAgo), formatDateForAPI(today)]
  } else if (type === 'month') {
    // 最近30天
    const monthAgo = new Date(today)
    monthAgo.setDate(monthAgo.getDate() - 29) // 今天 + 过去29天 = 30天
    searchForm.timeRange = [formatDateForAPI(monthAgo), formatDateForAPI(today)]
  }
  
  // 强制更新界面
  setTimeout(() => {
    searchForm.dateType = type
  }, 0)
  
  console.log('设置时间范围:', searchForm.dateType, searchForm.timeRange)
  
  // 更新日志
  handleSearch()
}

// 格式化日期为 API 所需的 YYYY-MM-DD 格式
const formatDateForAPI = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 获取数据源列表
const getDatasources = async () => {
  try {
    console.log('开始获取数据源列表')
    const dbRes = await fetchDatasources()
    console.log('数据源API响应:', dbRes)
    
    // 修复数据源列表处理逻辑
    if (dbRes) {
      let datasourceList = []
      
      if (Array.isArray(dbRes)) {
        // 直接是数组
        datasourceList = dbRes
      } else if (dbRes.data) {
        if (Array.isArray(dbRes.data)) {
          // data是数组
          datasourceList = dbRes.data
        } else if (dbRes.data.items && Array.isArray(dbRes.data.items)) {
          // data.items是数组
          datasourceList = dbRes.data.items
        } else if (dbRes.data.results && Array.isArray(dbRes.data.results)) {
          // DRF格式
          datasourceList = dbRes.data.results
        }
      } else if (dbRes.items && Array.isArray(dbRes.items)) {
        // 直接包含items
        datasourceList = dbRes.items
      }
      
      if (datasourceList.length > 0) {
        databases.value = datasourceList.map(db => ({
          id: db.id,
          name: db.name,
          type: db.db_type || 'MySQL'
        }))
        
        console.log('处理后的数据源列表:', databases.value)
      } else {
        console.warn('未找到有效的数据源数据')
      }
    }
  } catch (error) {
    console.error('获取数据库列表失败:', error)
    ElMessage.warning('获取数据库列表失败，部分筛选功能可能不可用')
  }
  
  // 获取完数据源后立即获取日志
  fetchSqlLogs()
}

// 页面加载时获取数据源和日志
onMounted(() => {
  console.log('组件已挂载，初始化数据')
  // 设置默认为"今天"的日期范围
  handleDateTypeChange('today')
  // 获取审计规则
  fetchAuditRules()
  // 获取数据源列表（内部会调用fetchSqlLogs获取日志）
  getDatasources()
})
</script>

<style scoped>
.audit-logs-container {
  padding: 20px 0;
}

.search-panel {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-right {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.db-option, .db-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.expanded-row {
  padding: 20px;
}

.sql-content {
  margin: 0;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow: auto;
}

.truncated-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.risk-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.risk-alert {
  margin-bottom: 0;
}

.detail-section-title {
  font-weight: bold;
  margin: 15px 0 10px 0;
  padding-bottom: 5px;
  border-bottom: 1px solid #ebeef5;
}

.detail-sql, .detail-risk {
  margin-top: 20px;
}

/* 风险等级行样式 */
:deep(.risk-level-high) {
  background-color: #ffebeb;
}

:deep(.risk-level-medium) {
  background-color: #fef5e6;
}

:deep(.risk-level-low) {
  background-color: #f3f8fa;
}

/* 数据库图标样式 */
.mysql-icon {
  color: #00758f;
}

.postgres-icon {
  color: #336791;
}

.oracle-icon {
  color: #f80000;
}

.sqlserver-icon {
  color: #cc2927;
}

.mongodb-icon {
  color: #4db33d;
}

.redis-icon {
  color: #d82c20;
}

.sqlite-icon {
  color: #0f80cc;
}

.db-icon {
  color: #909399;
}
</style> 