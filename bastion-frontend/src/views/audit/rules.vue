<template>
  <div class="audit-rules-container">
    <el-card class="filter-card" shadow="never">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索规则名称或内容"
            clearable
            prefix-icon="Search"
            class="search-input"
            @clear="handleSearch"
            @input="handleSearch"
          />
          <el-select
            v-model="ruleTypeFilter"
            placeholder="规则类型"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="type in ruleTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
          <el-select
            v-model="riskLevelFilter"
            placeholder="风险等级"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="level in riskLevels"
              :key="level.value"
              :label="level.label"
              :value="level.value"
            >
              <el-tag :type="getRiskTagType(level.value)" size="small">
                {{ level.label }}
              </el-tag>
            </el-option>
          </el-select>
          <el-select
            v-model="statusFilter"
            placeholder="状态"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="status in statusOptions"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            >
              <el-tag :type="status.value ? 'success' : 'info'" size="small">
                {{ status.label }}
              </el-tag>
            </el-option>
          </el-select>
        </div>
        <div class="toolbar-right">
          <el-button type="primary" @click="createRule">
            <el-icon><Plus /></el-icon>新建规则
          </el-button>
          <el-button @click="handleRefresh">
            <el-icon><Refresh /></el-icon>刷新
          </el-button>
        </div>
      </div>
    </el-card>

    <el-card class="rules-table-card" shadow="never">
      <el-table
        :data="filteredRules"
        style="width: 100%"
        border
        v-loading="loading"
      >
        <el-table-column prop="name" label="规则名称" min-width="180">
          <template #default="scope">
            <div class="rule-name">
              <el-tag
                :type="getRiskTagType(scope.row.riskLevel)"
                effect="plain"
                size="small"
                class="risk-tag"
              >
                {{ getRiskLevelLabel(scope.row.riskLevel) }}
              </el-tag>
              <span>{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="ruleType" label="规则类型" width="130">
          <template #default="scope">
            <el-tag>{{ getRuleTypeLabel(scope.row.ruleType) }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="pattern" label="匹配模式" min-width="200">
          <template #default="scope">
            <div class="pattern-content">
              <code>{{ scope.row.pattern }}</code>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="规则描述" min-width="200" />
        
        <el-table-column prop="isEnabled" label="状态" width="80">
          <template #default="scope">
            <el-switch
              v-model="scope.row.isEnabled"
              @change="(val) => toggleRuleStatus(scope.row, val)"
              active-color="#13ce66"
              inactive-color="#ff4949"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="scope">
            <el-button 
              size="small" 
              type="primary" 
              @click="editRule(scope.row)"
            >编辑</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteRule(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalRules"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 规则表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEditing ? '编辑审计规则' : '新建审计规则'"
      width="650px"
    >
      <el-form :model="ruleForm" :rules="formRules" ref="ruleFormRef" label-width="100px">
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="ruleForm.name" placeholder="请输入规则名称" />
        </el-form-item>
        
        <el-form-item label="规则类型" prop="ruleType">
          <el-select v-model="ruleForm.ruleType" placeholder="请选择规则类型" style="width: 100%">
            <el-option
              v-for="type in ruleTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="匹配模式" prop="pattern">
          <el-input 
            v-model="ruleForm.pattern" 
            placeholder="请输入匹配模式（支持正则表达式）"
            type="textarea"
            :rows="3"
          />
          <div class="form-help">
            <p class="help-text">
              {{ getPatternHelp(ruleForm.ruleType) }}
            </p>
          </div>
        </el-form-item>
        
        <el-form-item label="风险等级" prop="riskLevel">
          <el-select v-model="ruleForm.riskLevel" placeholder="请选择风险等级" style="width: 100%">
            <el-option
              v-for="level in riskLevels"
              :key="level.value"
              :label="level.label"
              :value="level.value"
            >
              <el-tag :type="getRiskTagType(level.value)" size="small">
                {{ level.label }}
              </el-tag>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="规则描述" prop="description">
          <el-input 
            v-model="ruleForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入规则描述"
          />
        </el-form-item>
        
        <el-form-item label="适用数据库">
          <el-select
            v-model="ruleForm.databases"
            multiple
            placeholder="请选择适用的数据库类型"
            style="width: 100%"
          >
            <el-option
              v-for="db in databaseTypes"
              :key="db.value"
              :label="db.label"
              :value="db.value"
            />
          </el-select>
          <div class="form-help">
            <p class="help-text">不选择则适用于所有数据库类型</p>
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-checkbox v-model="ruleForm.isEnabled">启用规则</el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRule">保存</el-button>
          <el-button v-if="isEditing" type="info" @click="testRule">测试规则</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 测试规则对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="测试审计规则"
      width="700px"
    >
      <el-form :model="testForm" label-width="100px">
        <el-form-item label="SQL语句" prop="sql">
          <el-input 
            v-model="testForm.sql" 
            type="textarea" 
            :rows="6" 
            placeholder="请输入要测试的SQL语句"
          />
        </el-form-item>
        
        <el-form-item label="数据库类型" prop="dbType">
          <el-select v-model="testForm.dbType" placeholder="请选择数据库类型" style="width: 100%">
            <el-option
              v-for="db in databaseTypes"
              :key="db.value"
              :label="db.label"
              :value="db.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <div v-if="testResult" class="test-result">
        <el-alert
          v-if="testResult.matched"
          title="规则匹配成功"
          type="warning"
          description="该SQL语句触发了当前审计规则"
          show-icon
          :closable="false"
        />
        <el-alert
          v-else
          title="规则未匹配"
          type="success"
          description="该SQL语句未触发当前审计规则"
          show-icon
          :closable="false"
        />
        
        <div v-if="testResult.matched" class="match-details">
          <div class="match-title">匹配详情:</div>
          <pre class="match-content">{{ testResult.matchDetails }}</pre>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="testDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="runTest">开始测试</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Plus, Refresh, Delete
} from '@element-plus/icons-vue'
import { getAuditRules, createAuditRule, updateAuditRule, deleteAuditRule } from '../../api/audit'

// 规则类型
const ruleTypes = [
  { value: 'keyword', label: '关键词匹配' },
  { value: 'regex', label: '正则表达式' },
  { value: 'syntax', label: '语法分析' },
  { value: 'object', label: '对象检查' },
  { value: 'data', label: '数据检查' }
]

// 风险等级
const riskLevels = [
  { value: 'high', label: '高风险' },
  { value: 'medium', label: '中风险' },
  { value: 'low', label: '低风险' }
]

// 状态选项
const statusOptions = [
  { value: true, label: '已启用' },
  { value: false, label: '已禁用' }
]

// 数据库类型
const databaseTypes = [
  { value: 'mysql', label: 'MySQL' },
  { value: 'postgresql', label: 'PostgreSQL' },
  { value: 'oracle', label: 'Oracle' },
  { value: 'sqlserver', label: 'SQL Server' },
  { value: 'mongodb', label: 'MongoDB' }
]

// 初始化空数组
const auditRules = ref([])

// 状态变量
const loading = ref(false)
const searchKeyword = ref('')
const ruleTypeFilter = ref(null)
const riskLevelFilter = ref(null)
const statusFilter = ref(null)
const currentPage = ref(1)
const pageSize = ref(10)
const totalRules = ref(0)
const dialogVisible = ref(false)
const isEditing = ref(false)
const testDialogVisible = ref(false)
const testResult = ref(null)

// 表单数据
const ruleForm = reactive({
  id: null,
  name: '',
  ruleType: '',
  pattern: '',
  description: '',
  riskLevel: 'medium',
  isEnabled: true,
  databases: []
})

// 测试表单
const testForm = reactive({
  sql: '',
  dbType: 'mysql'
})

// 表单引用
const ruleFormRef = ref(null)

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  ruleType: [
    { required: true, message: '请选择规则类型', trigger: 'change' }
  ],
  pattern: [
    { required: true, message: '请输入匹配模式', trigger: 'blur' }
  ],
  riskLevel: [
    { required: true, message: '请选择风险等级', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入规则描述', trigger: 'blur' }
  ]
}

// 筛选后的规则列表
const filteredRules = computed(() => {
  let result = [...auditRules.value]
  
  // 搜索关键词过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(rule => 
      rule.name.toLowerCase().includes(keyword) || 
      rule.pattern.toLowerCase().includes(keyword) ||
      rule.description.toLowerCase().includes(keyword)
    )
  }
  
  // 规则类型过滤
  if (ruleTypeFilter.value) {
    result = result.filter(rule => rule.ruleType === ruleTypeFilter.value)
  }
  
  // 风险等级过滤
  if (riskLevelFilter.value) {
    result = result.filter(rule => rule.riskLevel === riskLevelFilter.value)
  }
  
  // 状态过滤
  if (statusFilter.value !== null && statusFilter.value !== undefined) {
    result = result.filter(rule => rule.isEnabled === statusFilter.value)
  }
  
  totalRules.value = result.length
  
  // 分页处理
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return result.slice(start, end)
})

// 获取风险等级标签类型
const getRiskTagType = (level) => {
  const typeMap = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'info'
  }
  return typeMap[level] || 'info'
}

// 获取风险等级标签文本
const getRiskLevelLabel = (level) => {
  const labelMap = {
    'high': '高风险',
    'medium': '中风险',
    'low': '低风险'
  }
  return labelMap[level] || '未知'
}

// 获取规则类型标签文本
const getRuleTypeLabel = (type) => {
  const found = ruleTypes.find(item => item.value === type)
  return found ? found.label : type
}

// 获取匹配模式帮助文本
const getPatternHelp = (type) => {
  const helpMap = {
    'keyword': '输入关键词，如"DROP TABLE"。规则将检查SQL中是否包含该关键词。',
    'regex': '输入正则表达式，如"DELETE\\s+FROM\\s+[\\w\\.]+\\s+WHERE"。规则将使用正则匹配SQL。',
    'syntax': '输入语法规则，系统将解析SQL语法树并进行匹配。',
    'object': '输入对象名称，系统将检查SQL中是否操作了该对象。',
    'data': '输入数据模式，系统将检查SQL操作的数据是否符合规则。'
  }
  return helpMap[type] || '请输入匹配模式'
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
}

// 处理刷新
const handleRefresh = () => {
  searchKeyword.value = ''
  ruleTypeFilter.value = null
  riskLevelFilter.value = null
  statusFilter.value = null
  currentPage.value = 1
  
  fetchRules()
}

// 创建规则
const createRule = () => {
  isEditing.value = false
  ruleForm.id = null
  ruleForm.name = ''
  ruleForm.ruleType = 'keyword'
  ruleForm.pattern = ''
  ruleForm.description = ''
  ruleForm.riskLevel = 'medium'
  ruleForm.isEnabled = true
  ruleForm.databases = []
  
  dialogVisible.value = true
}

// 编辑规则
const editRule = (rule) => {
  isEditing.value = true
  
  Object.keys(ruleForm).forEach(key => {
    ruleForm[key] = rule[key]
  })
  
  dialogVisible.value = true
}

// 删除规则
const deleteRule = (rule) => {
  ElMessageBox.confirm(
    `确定要删除规则 "${rule.name}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    loading.value = true
    
    try {
      // 在实际应用中调用API
      // await deleteAuditRule(rule.id)
      
      // 模拟删除操作
      const index = auditRules.value.findIndex(item => item.id === rule.id)
      if (index !== -1) {
        auditRules.value.splice(index, 1)
      }
      
      ElMessage.success('规则删除成功')
    } catch (error) {
      console.error('删除规则失败:', error)
      ElMessage.error('删除规则失败')
    } finally {
      loading.value = false
    }
  }).catch(() => {
    // 取消删除
  })
}

// 切换规则状态
const toggleRuleStatus = async (rule, status) => {
  try {
    // 在实际应用中调用API
    // await updateAuditRule(rule.id, { is_enabled: status })
    
    ElMessage.success(`规则已${status ? '启用' : '禁用'}`)
  } catch (error) {
    // 恢复状态
    rule.isEnabled = !status
    console.error('更新规则状态失败:', error)
    ElMessage.error('更新规则状态失败')
  }
}

// 获取规则列表
const fetchRules = async () => {
  loading.value = true
  
  try {
    // 调用API获取规则数据
    const res = await getAuditRules()
    console.log('API返回的审计规则数据:', res)
    
    // 初始化空数组
    auditRules.value = []
    
    if (res) {
      let rawRules = [];
      let totalCount = 0;
      
      // 处理Django REST Framework分页格式
      if (res.count !== undefined && res.results && Array.isArray(res.results)) {
        console.log('检测到Django REST Framework分页格式')
        rawRules = res.results;
        totalCount = res.count;
      }
      // 处理标准data.results格式
      else if (res.data && res.data.results && Array.isArray(res.data.results)) {
        console.log('检测到嵌套的Django REST Framework分页格式')
        rawRules = res.data.results;
        totalCount = res.data.count || rawRules.length;
      }
      // 处理标准分页格式 (total, items)
      else if (res.data && res.data.items && Array.isArray(res.data.items)) {
        console.log('检测到标准分页格式')
        rawRules = res.data.items;
        totalCount = res.data.total || rawRules.length;
      }
      // 处理数组格式
      else if (Array.isArray(res.data)) {
        console.log('检测到数组格式')
        rawRules = res.data;
        totalCount = rawRules.length;
      }
      // 直接响应就是数组
      else if (Array.isArray(res)) {
        console.log('响应本身是数组格式')
        rawRules = res;
        totalCount = rawRules.length;
      }
      // 处理Django REST Framework格式但没有嵌套在data中
      else if (res.results && Array.isArray(res.results)) {
        console.log('检测到直接返回的Django REST Framework格式')
        rawRules = res.results;
        totalCount = res.count || rawRules.length;
      }
      // 不能识别的格式
      else {
        console.warn('未识别的API响应格式:', res)
        rawRules = [];
        totalCount = 0;
      }
      
      console.log(`成功获取了 ${rawRules.length} 条规则数据`)
      
      if (rawRules.length > 0) {
        // 字段映射处理
        auditRules.value = rawRules.map(rule => {
          // 将后端字段名映射到前端字段名
          return {
            id: rule.id,
            name: rule.name,
            ruleType: rule.rule_type || 'keyword',
            pattern: rule.pattern,
            description: rule.description || '',
            riskLevel: rule.risk_level || rule.level || 'medium',
            isEnabled: rule.is_enabled !== undefined ? rule.is_enabled : true,
            databases: rule.databases || [],
            createdBy: rule.created_by ? rule.created_by.username : '',
            createdAt: rule.created_at,
            updatedAt: rule.updated_at
          };
        });
        
        console.log('成功映射了审计规则数据', auditRules.value)
      } else {
        // 如果没有数据，添加一些默认的示例规则
        console.log('没有找到审计规则，显示默认示例规则')
        auditRules.value = getDefaultRules();
        ElMessage.info('没有找到审计规则，显示默认示例规则')
      }
      
      totalRules.value = totalCount > 0 ? totalCount : auditRules.value.length;
    } else {
      console.warn('API响应无效:', res)
      ElMessage.warning('获取规则列表失败，显示示例规则')
      
      // 显示默认示例规则
      auditRules.value = getDefaultRules();
      totalRules.value = auditRules.value.length;
    }
  } catch (error) {
    console.error('获取规则列表失败:', error)
    ElMessage.error('获取规则列表失败: ' + (error.message || '未知错误'))
    
    // 显示默认示例规则
    auditRules.value = getDefaultRules();
    totalRules.value = auditRules.value.length;
  } finally {
    loading.value = false
  }
}

// 获取默认示例规则
const getDefaultRules = () => {
  return [
    {
      id: 1,
      name: '禁止删除表',
      ruleType: 'keyword',
      pattern: 'DROP\\s+TABLE',
      description: '禁止使用DROP TABLE语句删除表',
      riskLevel: 'high',
      isEnabled: true,
      databases: ['mysql', 'postgresql', 'oracle']
    },
    {
      id: 2,
      name: '全表扫描检测',
      ruleType: 'regex',
      pattern: 'SELECT\\s+\\*\\s+FROM\\s+[\\w\\.]+\\s+WHERE\\s+(?!.*\\sLIMIT\\s)',
      description: '检测不带LIMIT条件的全表扫描操作',
      riskLevel: 'medium',
      isEnabled: true,
      databases: []
    },
    {
      id: 3,
      name: '大批量更新检测',
      ruleType: 'regex',
      pattern: 'UPDATE\\s+[\\w\\.]+\\s+SET\\s+.*(?!.*\\sLIMIT\\s)',
      description: '检测不带LIMIT条件的批量更新操作',
      riskLevel: 'medium',
      isEnabled: true,
      databases: ['mysql']
    },
    {
      id: 4,
      name: '检测WHERE 1=1条件',
      ruleType: 'regex',
      pattern: 'WHERE\\s+1\\s*=\\s*1',
      description: '检测SQL中是否包含WHERE 1=1这种风险条件',
      riskLevel: 'high',
      isEnabled: true,
      databases: []
    },
    {
      id: 5,
      name: '禁止使用TRUNCATE',
      ruleType: 'keyword',
      pattern: 'TRUNCATE\\s+TABLE',
      description: '禁止使用TRUNCATE TABLE清空表数据',
      riskLevel: 'high',
      isEnabled: false,
      databases: []
    }
  ];
}

// 保存规则
const saveRule = async () => {
  if (!ruleFormRef.value) return
  
  try {
    await ruleFormRef.value.validate()
    
    loading.value = true
    
    // 准备要发送的数据 - 将前端字段名映射到后端字段名
    const ruleData = {
      name: ruleForm.name,
      rule_type: ruleForm.ruleType,
      pattern: ruleForm.pattern,
      description: ruleForm.description,
      risk_level: ruleForm.riskLevel,
      is_enabled: ruleForm.isEnabled,
      databases: ruleForm.databases
    }
    
    console.log('准备发送的规则数据:', ruleData)
    
    if (isEditing.value) {
      // 更新规则
      try {
        const res = await updateAuditRule(ruleForm.id, ruleData)
        console.log('更新规则响应:', res)
        ElMessage.success('规则更新成功')
        
        // 重新获取规则列表
        fetchRules()
      } catch (apiError) {
        console.error('更新规则API错误:', apiError)
        ElMessage.error('更新规则失败: ' + (apiError.message || '未知错误'))
        
        // 仍然更新本地数据（模拟成功）
        const index = auditRules.value.findIndex(rule => rule.id === ruleForm.id)
        if (index !== -1) {
          auditRules.value[index] = { ...ruleForm }
        }
      }
    } else {
      // 创建规则
      try {
        const res = await createAuditRule(ruleData)
        console.log('创建规则响应:', res)
        ElMessage.success('规则创建成功')
        
        // 重新获取规则列表
        fetchRules()
      } catch (apiError) {
        console.error('创建规则API错误:', apiError)
        ElMessage.error('创建规则失败: ' + (apiError.message || '未知错误'))
        
        // 仍然添加到本地数据（模拟成功）
        const newRule = {
          ...ruleForm,
          id: auditRules.value.length > 0 
            ? Math.max(...auditRules.value.map(rule => rule.id)) + 1 
            : 1
        }
        auditRules.value.push(newRule)
      }
    }
    
    totalRules.value = auditRules.value.length
    dialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 测试规则
const testRule = () => {
  testForm.sql = ''
  testForm.dbType = 'mysql'
  testResult.value = null
  testDialogVisible.value = true
}

// 运行测试
const runTest = () => {
  if (!testForm.sql) {
    ElMessage.warning('请输入要测试的SQL语句')
    return
  }
  
  // 模拟测试结果
  const isMatched = testRegex(testForm.sql, ruleForm.pattern)
  
  testResult.value = {
    matched: isMatched,
    matchDetails: isMatched 
      ? `在SQL语句中发现匹配模式 "${ruleForm.pattern}"\n\n风险等级: ${getRiskLevelLabel(ruleForm.riskLevel)}\n风险描述: ${ruleForm.description}`
      : null
  }
}

// 测试正则匹配
const testRegex = (sql, pattern) => {
  try {
    const regex = new RegExp(pattern, 'i')
    return regex.test(sql)
  } catch (error) {
    ElMessage.error('正则表达式错误: ' + error.message)
    return false
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 页面加载时获取数据
onMounted(() => {
  fetchRules()
})
</script>

<style scoped>
.audit-rules-container {
  padding: 20px 0;
}

.filter-card {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.toolbar-left {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  flex: 1;
}

.toolbar-right {
  display: flex;
  gap: 10px;
}

.search-input {
  width: 250px;
}

.rule-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.risk-tag {
  margin-right: 5px;
}

.pattern-content {
  font-family: monospace;
}

.pattern-content code {
  background-color: #f8f8f8;
  padding: 2px 5px;
  border-radius: 3px;
  color: #e6462e;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.form-help {
  margin-top: 5px;
}

.help-text {
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
  margin: 0;
}

.test-result {
  margin-top: 20px;
}

.match-details {
  margin-top: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background-color: #f8f9fa;
}

.match-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.match-content {
  margin: 0;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
}
</style> 