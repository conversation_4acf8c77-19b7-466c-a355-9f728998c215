<template>
  <div class="audit-logs-container">
    <div class="search-panel">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="关键词">
          <el-input 
            v-model="searchForm.keyword" 
            placeholder="筛选操作内容" 
            clearable 
            @keyup.enter="handleSearch"
            @clear="handleSearch" 
          />
        </el-form-item>
        
        <el-form-item label="操作类型">
          <el-select 
            v-model="searchForm.actionTypes" 
            placeholder="选择操作类型" 
            clearable 
            multiple
            collapse-tags
            collapse-tags-tooltip
            style="width: 220px"
            @change="handleSearch"
          >
            <el-option
              v-for="type in actionTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="风险等级">
          <el-select 
            v-model="searchForm.riskLevels" 
            placeholder="选择风险等级" 
            clearable 
            multiple
            collapse-tags
            collapse-tags-tooltip
            style="width: 220px"
            @change="handleSearch"
          >
            <el-option
              v-for="level in riskLevels"
              :key="level.value"
              :label="level.label"
              :value="level.value"
            >
              <el-tag :type="getRiskTagType(level.value)" size="small">
                {{ level.label }}
              </el-tag>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="时间范围">
          <el-select 
            v-model="searchForm.dateType" 
            placeholder="选择时间范围" 
            style="width: 140px"
            fit-input-width
            @change="handleDateTypeChange"
          >
            <el-option label="今天" value="today" />
            <el-option label="昨天" value="yesterday" />
            <el-option label="最近7天" value="week" />
            <el-option label="最近30天" value="month" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="searchForm.dateType === 'custom'">
          <el-date-picker
            v-model="searchForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleSearch"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>查询
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>重置
          </el-button>
          <el-button type="success" @click="exportLogs">
            <el-icon><Download /></el-icon>导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="logs-table-card">
      <template #header>
        <div class="card-header">
          <span>操作日志列表</span>
          <div class="header-right">
            <el-tag v-if="totalRiskCount.high > 0" type="danger">
              高风险: {{ totalRiskCount.high }}
            </el-tag>
            <el-tag v-if="totalRiskCount.medium > 0" type="warning">
              中风险: {{ totalRiskCount.medium }}
            </el-tag>
            <el-tag v-if="totalRiskCount.low > 0" type="info">
              低风险: {{ totalRiskCount.low }}
            </el-tag>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="auditLogs" 
        style="width: 100%" 
        border 
        v-loading="loading"
        :row-class-name="getRowClass"
      >
        <template #empty>
          <div class="empty-state">
            <el-empty description="没有找到符合条件的日志记录" />
            <div class="empty-actions" v-if="hasActiveFilters">
              <el-button type="primary" @click="resetSearch">清除筛选条件</el-button>
            </div>
          </div>
        </template>

        <el-table-column type="expand">
          <template #default="props">
            <div class="expanded-row">
              <el-descriptions title="详细信息" :column="3" border>
                <el-descriptions-item label="操作用户">{{ props.row.username }}</el-descriptions-item>
                <el-descriptions-item label="操作时间">
                  {{ formatDateTime(props.row.timestamp) }}
                </el-descriptions-item>
                <el-descriptions-item label="IP地址">{{ props.row.clientIp }}</el-descriptions-item>
                <el-descriptions-item label="数据库">{{ props.row.database.name }}</el-descriptions-item>
                <el-descriptions-item label="风险等级">
                  <el-tag :type="getRiskTagType(props.row.riskLevel)">
                    {{ getRiskLevelLabel(props.row.riskLevel) }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="操作类型">
                  {{ getActionTypeLabel(props.row.actionType) }}
                </el-descriptions-item>
                <el-descriptions-item label="操作内容" :span="3">
                  <pre class="sql-content">{{ props.row.content || props.row.sqlContent }}</pre>
                </el-descriptions-item>
                <el-descriptions-item v-if="props.row.riskDetails && props.row.riskDetails.length > 0" label="风险详情" :span="3">
                  <div class="risk-details">
                    <el-alert
                      v-for="(detail, index) in props.row.riskDetails"
                      :key="index"
                      :title="detail.rule"
                      :type="getRiskTagType(detail.level)"
                      :description="detail.description"
                      show-icon
                      :closable="false"
                      class="risk-alert"
                    />
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="timestamp" label="操作时间" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.timestamp) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="username" label="操作用户" width="120" />
        
        <el-table-column prop="database.name" label="数据源" width="160">
          <template #default="scope">
            <div class="db-name-cell">
              <el-icon :class="getDbIconClass(scope.row.database.type)"><Connection /></el-icon>
              <span>{{ scope.row.database.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="actionType" label="操作类型" width="120">
          <template #default="scope">
            {{ getActionTypeLabel(scope.row.actionType) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="riskLevel" label="风险等级" width="100">
          <template #default="scope">
            <el-tag :type="getRiskTagType(scope.row.riskLevel)">
              {{ getRiskLevelLabel(scope.row.riskLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="content" label="操作内容">
          <template #default="scope">
            <div class="truncated-content">{{ truncateText(scope.row.content || scope.row.sqlContent, 50) }}</div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button 
              size="small" 
              type="primary" 
              @click="viewDetails(scope.row)"
              :icon="View"
            >详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalLogs"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="审计日志详情"
      width="70%"
    >
      <el-descriptions v-if="selectedLog" title="基本信息" :column="3" border>
        <el-descriptions-item label="操作用户">{{ selectedLog.username }}</el-descriptions-item>
        <el-descriptions-item label="操作时间">
          {{ formatDateTime(selectedLog.timestamp) }}
        </el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ selectedLog.clientIp }}</el-descriptions-item>
        <el-descriptions-item label="数据库">{{ selectedLog.database.name }}</el-descriptions-item>
        <el-descriptions-item label="风险等级">
          <el-tag :type="getRiskTagType(selectedLog.riskLevel)">
            {{ getRiskLevelLabel(selectedLog.riskLevel) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="操作类型">
          {{ getActionTypeLabel(selectedLog.actionType) }}
        </el-descriptions-item>
      </el-descriptions>
      
      <div v-if="selectedLog" class="detail-sql">
        <div class="detail-section-title">操作内容</div>
        <pre class="sql-content">{{ selectedLog.content || selectedLog.sqlContent }}</pre>
      </div>
      
      <div v-if="selectedLog && selectedLog.riskDetails && selectedLog.riskDetails.length > 0" class="detail-risk">
        <div class="detail-section-title">风险详情</div>
        <div class="risk-details">
          <el-alert
            v-for="(detail, index) in selectedLog.riskDetails"
            :key="index"
            :title="detail.rule"
            :type="getRiskTagType(detail.level)"
            :description="detail.description"
            show-icon
            :closable="false"
            class="risk-alert"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Connection, Search, Refresh, Download, View
} from '@element-plus/icons-vue'
import { getAuditLogs, exportAuditLogs } from '../../api/audit'

// 操作类型
const actionTypes = [
  { value: 'login', label: '登录' },
  { value: 'logout', label: '登出' },
  { value: 'query', label: '查询' },
  { value: 'execute', label: '执行' },
  { value: 'export', label: '导出' },
  { value: 'create_datasource', label: '创建数据源' },
  { value: 'update_datasource', label: '更新数据源' },
  { value: 'delete_datasource', label: '删除数据源' },
  { value: 'test_connection', label: '测试连接' },
  { value: 'get_schemas', label: '获取数据库模式' },
  { value: 'get_schema_from_cache', label: '获取缓存数据模式' },
  { value: 'get_schema', label: '获取数据库结构' },
  { value: 'create_audit_rule', label: '创建审计规则' },
  { value: 'update_audit_rule', label: '更新审计规则' },
  { value: 'delete_audit_rule', label: '删除审计规则' },
  { value: 'rule_edit', label: '编辑规则' },
  { value: 'rule_delete', label: '删除规则' },
  { value: 'export_audit_logs', label: '导出审计日志' }
]

// 风险等级
const riskLevels = [
  { value: 'high', label: '高风险' },
  { value: 'medium', label: '中风险' },
  { value: 'low', label: '低风险' },
  { value: 'none', label: '无风险' }
]

// 状态变量
const loading = ref(false)
const auditLogs = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalLogs = ref(0)
const detailDialogVisible = ref(false)
const selectedLog = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  actionTypes: [],
  riskLevels: [],
  dateType: 'today',
  timeRange: null
})

// 风险统计
const totalRiskCount = reactive({
  high: 0,
  medium: 0,
  low: 0
})

// 根据数据库类型获取图标类名
const getDbIconClass = (type) => {
  const typeMap = {
    'MySQL': 'mysql-icon',
    'PostgreSQL': 'postgres-icon',
    'Oracle': 'oracle-icon',
    'SQL Server': 'sqlserver-icon',
    'MongoDB': 'mongodb-icon',
    'Redis': 'redis-icon',
    'SQLite': 'sqlite-icon'
  }
  return typeMap[type] || 'db-icon'
}

// 获取风险等级标签类型
const getRiskTagType = (level) => {
  const typeMap = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'info',
    'none': 'success'
  }
  return typeMap[level] || 'info'
}

// 获取风险等级标签文本
const getRiskLevelLabel = (level) => {
  const labelMap = {
    'high': '高风险',
    'medium': '中风险',
    'low': '低风险',
    'none': '无风险'
  }
  return labelMap[level] || '未知'
}

// 获取操作类型标签文本
const getActionTypeLabel = (type) => {
  const found = actionTypes.find(item => item.value === type)
  return found ? found.label : type
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  
  const date = new Date(dateTime)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 截断文本
const truncateText = (text, maxLength) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// 获取行的类名（为不同风险等级设置不同样式）
const getRowClass = ({ row }) => {
  return `risk-level-${row.riskLevel}`
}

// 处理搜索
const handleSearch = () => {
  console.log('执行搜索, 筛选条件:', {
    关键词: searchForm.keyword,
    操作类型: searchForm.actionTypes, 
    风险等级: searchForm.riskLevels,
    时间范围类型: searchForm.dateType,
    时间范围: searchForm.timeRange
  })
  
  currentPage.value = 1
  fetchAuditLogs()
}

// 重置搜索
const resetSearch = () => {
  resetFilters()
  handleSearch()
}

// 查看详情
const viewDetails = (log) => {
  selectedLog.value = log
  detailDialogVisible.value = true
}

// 导出日志
const exportLogs = async () => {
  try {
    ElMessage.success('正在导出审计日志')
    
    // 构建与查询相同的参数
    const params = {
      keyword: searchForm.keyword || undefined,
      action_type: searchForm.actionTypes.length > 0 ? searchForm.actionTypes.join(',') : undefined,
      risk_level: searchForm.riskLevels.length > 0 ? searchForm.riskLevels.join(',') : undefined,
      start_date: searchForm.timeRange ? searchForm.timeRange[0] : undefined,
      end_date: searchForm.timeRange ? searchForm.timeRange[1] : undefined
    }
    
    console.log('导出参数:', params)
    
    // 调用导出API
    const response = await exportAuditLogs(params)
    
    // 创建下载
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `审计日志_${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('审计日志导出成功')
  } catch (error) {
    console.error('导出审计日志失败:', error)
    ElMessage.error('导出审计日志失败: ' + (error.message || '未知错误'))
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchAuditLogs()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchAuditLogs()
}

// 获取审计日志
const fetchAuditLogs = async () => {
  loading.value = true
  
  try {
    // 调用API获取数据
    const params = {
      keyword: searchForm.keyword || undefined,
      action_type: searchForm.actionTypes.join(',') || undefined,
      risk_level: searchForm.riskLevels.join(',') || undefined,
      page: currentPage.value,
      limit: pageSize.value,
      exclude_query: true // 排除查询类SQL
    }
    
    // 设置日期范围参数
    if (searchForm.timeRange && searchForm.timeRange.length === 2) {
      params.start_date = searchForm.timeRange[0]
      params.end_date = searchForm.timeRange[1]
    }
    
    console.log('查询参数:', JSON.stringify(params))
    
    try {
      const res = await getAuditLogs(params)
      console.log('审计日志API响应:', res)
      
      if (res) {
        // 处理标准分页格式（直接包含 total 和 items）
        if (res.items && Array.isArray(res.items)) {
          auditLogs.value = res.items || []
          totalLogs.value = res.total || res.items.length || 0
          
          // 计算风险统计
          if (res.risk_count) {
            totalRiskCount.high = res.risk_count.high || 0
            totalRiskCount.medium = res.risk_count.medium || 0
            totalRiskCount.low = res.risk_count.low || 0
          }
        }
        // 处理嵌套在 data 中的标准分页格式
        else if (res.data && res.data.items && Array.isArray(res.data.items)) {
          auditLogs.value = res.data.items || []
          totalLogs.value = res.data.total || res.data.items.length || 0
          
          // 计算风险统计
          if (res.data.risk_count) {
            totalRiskCount.high = res.data.risk_count.high || 0
            totalRiskCount.medium = res.data.risk_count.medium || 0
            totalRiskCount.low = res.data.risk_count.low || 0
          }
        }
        // 处理数组格式
        else if (Array.isArray(res.data)) {
          auditLogs.value = res.data
          totalLogs.value = res.data.length
        }
        // 处理Django REST Framework分页格式
        else if (res.data && res.data.results && Array.isArray(res.data.results)) {
          auditLogs.value = res.data.results
          totalLogs.value = res.data.count || res.data.results.length
        }
        // 无法识别的格式
        else {
          console.warn('响应格式不符合预期:', JSON.stringify(res))
          auditLogs.value = []
          totalLogs.value = 0
        }
        
        // 如果没有数据，显示提示信息
        if (auditLogs.value.length === 0) {
          ElMessage.info('没有找到符合条件的日志记录')
        }
        
        console.log('原始日志数据:', JSON.stringify(auditLogs.value))
        
        // 确保每个日志项都有有效的数据，并做防御性处理
        auditLogs.value = auditLogs.value.map(log => {
          if (!log) return null
          
          try {
            // 创建防御性副本，避免直接修改原对象
            const safeLog = { ...log }
            
            // 确保有效的database对象
            if (!safeLog.database && !safeLog.datasource) {
              safeLog.database = {
                id: null,
                name: '系统操作',
                type: 'system'
              }
            } else if (!safeLog.database && safeLog.datasource) {
              safeLog.database = {
                id: safeLog.datasource.id,
                name: safeLog.datasource.name || '未知数据库',
                type: safeLog.datasource.db_type || 'unknown'
              }
            }
            
            // 确保其他必要字段都有默认值
            safeLog.id = safeLog.id || 0
            safeLog.username = safeLog.username || log.user?.username || 'unknown'
            safeLog.actionType = safeLog.action_type || safeLog.actionType || 'unknown'
            safeLog.timestamp = safeLog.timestamp || safeLog.action_time || new Date().toISOString()
            safeLog.clientIp = safeLog.clientIp || safeLog.ip_address || '0.0.0.0'
            
            // 优先使用 content 字段
            safeLog.content = safeLog.content || log.content || safeLog.sqlContent || log.sqlContent || ''
            // 保留 sqlContent 字段以兼容旧数据
            safeLog.sqlContent = safeLog.sqlContent || log.sqlContent || safeLog.content || log.content || ''
            
            // 处理风险等级
            safeLog.riskLevel = safeLog.riskLevel || safeLog.risk_level || 'low'
            
            return safeLog
          } catch (e) {
            console.error('处理日志对象时出错:', e, log)
            // 返回一个安全的默认对象
            return {
              id: 0,
              username: 'error',
              actionType: 'unknown',
              timestamp: new Date().toISOString(),
              clientIp: '0.0.0.0',
              content: '数据处理错误',
              riskLevel: 'low',
              database: {
                id: null,
                name: '系统操作',
                type: 'system'
              }
            }
          }
        }).filter(log => log !== null)
      } else {
        console.warn('API返回了空响应')
        auditLogs.value = []
        totalLogs.value = 0
        ElMessage.info('没有找到符合条件的日志记录')
      }
    } catch (error) {
      console.error('获取审计日志失败:', error)
      ElMessage.error('获取审计日志失败: ' + (error.message || '未知错误'))
      auditLogs.value = []
      totalLogs.value = 0
    }
    
    loading.value = false
  } catch (error) {
    console.error('获取审计日志失败:', error)
    ElMessage.error('获取审计日志失败: ' + (error.message || '未知错误'))
    loading.value = false
  }
}

// 处理日期类型变化
const handleDateTypeChange = (type) => {
  console.log('时间范围选择:', type)
  
  if (type === 'custom') {
    // 自定义时间范围，清空当前选择，让用户选择
    searchForm.timeRange = null
    return
  }
  
  // 获取当前日期
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  
  // 根据选择的类型设置日期范围
  if (type === 'today') {
    // 今天
    const todayStr = formatDateForAPI(today)
    searchForm.timeRange = [todayStr, todayStr]
  } else if (type === 'yesterday') {
    // 昨天
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    const yesterdayStr = formatDateForAPI(yesterday)
    searchForm.timeRange = [yesterdayStr, yesterdayStr]
  } else if (type === 'week') {
    // 最近7天
    const weekAgo = new Date(today)
    weekAgo.setDate(weekAgo.getDate() - 6) // 今天 + 过去6天 = 7天
    searchForm.timeRange = [formatDateForAPI(weekAgo), formatDateForAPI(today)]
  } else if (type === 'month') {
    // 最近30天
    const monthAgo = new Date(today)
    monthAgo.setDate(monthAgo.getDate() - 29) // 今天 + 过去29天 = 30天
    searchForm.timeRange = [formatDateForAPI(monthAgo), formatDateForAPI(today)]
  }
  
  // 强制更新界面
  setTimeout(() => {
    searchForm.dateType = type
  }, 0)
  
  console.log('设置时间范围:', searchForm.dateType, searchForm.timeRange)
  
  // 更新日志
  handleSearch()
}

// 格式化日期为 API 所需的 YYYY-MM-DD 格式
const formatDateForAPI = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 计算是否有激活的筛选条件
const hasActiveFilters = computed(() => {
  return !!(
    searchForm.keyword || 
    searchForm.actionTypes.length > 0 || 
    searchForm.riskLevels.length > 0 ||
    (searchForm.dateType !== 'today')
  )
})

// 页面加载时获取数据源和日志
onMounted(async () => {
  try {
    // 重置所有筛选条件
    resetFilters()
    
    // 获取日志
    fetchAuditLogs()
  } catch (error) {
    console.error('初始化页面失败:', error)
    ElMessage.warning('初始化页面失败，请刷新重试')
  }
})

// 重置所有筛选条件到默认值
const resetFilters = () => {
  // 初始化默认日期为今天
  const today = new Date()
  const todayStr = formatDateForAPI(today)
  
  // 确保UI能正确反映这些值
  searchForm.keyword = ''
  searchForm.actionTypes = []
  searchForm.riskLevels = []
  searchForm.dateType = 'today'
  searchForm.timeRange = [todayStr, todayStr]
  
  console.log('初始化所有筛选条件:', searchForm)
  
  // 强制更新组件状态
  nextTick(() => {
    if (searchForm.dateType === 'today') {
      // 确保时间范围是今天
      searchForm.timeRange = [todayStr, todayStr]
    }
  })
}
</script>

<style scoped>
.audit-logs-container {
  padding: 20px 0;
}

.search-panel {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

/* 确保筛选器下拉框正确显示选中值 */
:deep(.el-select .el-input__wrapper) {
  min-width: 120px;
}

:deep(.el-select__popper) {
  min-width: 120px !important;
}

:deep(.filter-select-dropdown) {
  min-width: 120px !important;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-right {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.db-option, .db-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.expanded-row {
  padding: 20px;
}

.sql-content {
  margin: 0;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow: auto;
}

.truncated-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.risk-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.risk-alert {
  margin-bottom: 0;
}

.detail-section-title {
  font-weight: bold;
  margin: 15px 0 10px 0;
  padding-bottom: 5px;
  border-bottom: 1px solid #ebeef5;
}

.detail-sql, .detail-risk {
  margin-top: 20px;
}

/* 风险等级行样式 */
:deep(.risk-level-high) {
  background-color: #ffebeb;
}

:deep(.risk-level-medium) {
  background-color: #fef5e6;
}

:deep(.risk-level-low) {
  background-color: #f3f8fa;
}

/* 数据库图标样式 */
.mysql-icon {
  color: #00758f;
}

.postgres-icon {
  color: #336791;
}

.oracle-icon {
  color: #f80000;
}

.sqlserver-icon {
  color: #cc2927;
}

.mongodb-icon {
  color: #4db33d;
}

.redis-icon {
  color: #d82c20;
}

.sqlite-icon {
  color: #0f80cc;
}

.db-icon {
  color: #909399;
}

.empty-state {
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-actions {
  margin-top: 20px;
}
</style>

<style>
/* 全局样式，确保下拉框正确显示 */
.el-select .el-input__wrapper {
  min-width: 120px;
}

.el-select__popper {
  min-width: 120px !important;
}

.filter-select-dropdown {
  min-width: 120px !important;
}
</style> 