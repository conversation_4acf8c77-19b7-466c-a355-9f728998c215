<template>
  <div class="dashboard-container">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24" :sm="12" :md="6" :lg="6">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>数据库总数</span>
            </div>
          </template>
          <div class="card-body">
            <div class="card-value">{{ statistics.databaseCount }}</div>
            <div class="card-trend">
              <span>较上周</span>
              <span :class="statistics.databaseTrend > 0 ? 'up' : 'down'">
                {{ statistics.databaseTrend > 0 ? '+' : '' }}{{ statistics.databaseTrend }}%
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6" :xs="24" :sm="12" :md="6" :lg="6">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>查询总数</span>
            </div>
          </template>
          <div class="card-body">
            <div class="card-value">{{ statistics.queryCount }}</div>
            <div class="card-trend">
              <span>较上周</span>
              <span :class="statistics.queryTrend > 0 ? 'up' : 'down'">
                {{ statistics.queryTrend > 0 ? '+' : '' }}{{ statistics.queryTrend }}%
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6" :xs="24" :sm="12" :md="6" :lg="6">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>活跃用户</span>
            </div>
          </template>
          <div class="card-body">
            <div class="card-value">{{ statistics.activeUsers }}</div>
            <div class="card-trend">
              <span>较上周</span>
              <span :class="statistics.userTrend > 0 ? 'up' : 'down'">
                {{ statistics.userTrend > 0 ? '+' : '' }}{{ statistics.userTrend }}%
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6" :xs="24" :sm="12" :md="6" :lg="6">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>审计日志数</span>
            </div>
          </template>
          <div class="card-body">
            <div class="card-value">{{ statistics.auditCount }}</div>
            <div class="card-trend">
              <span>较上周</span>
              <span :class="statistics.auditTrend > 0 ? 'up' : 'down'">
                {{ statistics.auditTrend > 0 ? '+' : '' }}{{ statistics.auditTrend }}%
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" class="dashboard-row">
      <el-col :span="12" :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>查询统计</span>
              <div class="chart-controls">
                <el-radio-group v-model="queryChartType" size="small" @change="handleChartTypeChange">
                  <el-radio-button label="time">总统计</el-radio-button>
                  <el-radio-button label="user">用户统计</el-radio-button>
                </el-radio-group>
                <el-radio-group v-model="queryChartPeriod" size="small" @change="handlePeriodChange" style="margin-left: 10px;">
                  <el-radio-button label="week">本周</el-radio-button>
                  <el-radio-button label="month">本月</el-radio-button>
                </el-radio-group>
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  size="small"
                  style="margin-left: 10px; width: 220px;"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="handleDateRangeChange"
                  value-format="YYYY-MM-DD"
                />
              </div>
            </div>
          </template>
          <div class="chart-container">
            <div id="queryChart" style="width: 100%; height: 100%;"></div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12" :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>数据库类型分布</span>
            </div>
          </template>
          <div class="chart-container">
            <div id="dbTypeChart" style="width: 100%; height: 100%;"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" class="dashboard-row">
      <el-col :span="12" :xs="24" :sm="24" :md="12" :lg="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近查询</span>
              <!-- <el-link type="primary" :underline="false" href="#/query/history">查看全部</el-link> -->
            </div>
          </template>
          <el-table :data="recentQueries" style="width: 100%" stripe v-loading="loading">
            <el-table-column prop="database" label="数据库" width="150" />
            <el-table-column prop="query" label="查询语句" show-overflow-tooltip />
            <el-table-column prop="time" label="执行时间" width="180" />
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="12" :xs="24" :sm="24" :md="12" :lg="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近审计日志</span>
              <!-- <el-link type="primary" :underline="false" href="#/audit/logs">查看全部</el-link> -->
            </div>
          </template>
          <el-table :data="recentAudits" style="width: 100%" stripe v-loading="loading">
            <el-table-column prop="user" label="用户" width="120" />
            <el-table-column prop="operation" label="操作" width="120" />
            <el-table-column prop="detail" label="详情" show-overflow-tooltip />
            <el-table-column prop="time" label="时间" width="180" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { getDashboardStatistics } from '@/api/dashboard'
import * as echarts from 'echarts/core'
import { LineChart, PieChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 注册echarts组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  LineChart,
  PieChart,
  BarChart,
  CanvasRenderer
])

// 仪表盘统计数据
const statistics = ref({
  databaseCount: 0,
  databaseTrend: 0,
  queryCount: 0,
  queryTrend: 0,
  activeUsers: 0,
  userTrend: 0,
  auditCount: 0,
  auditTrend: 0
})

// 查询图表周期
const queryChartPeriod = ref('week')

// 查询图表类型
const queryChartType = ref('time')

// 自定义日期范围
const dateRange = ref(null)

// 是否使用自定义日期范围
const useCustomDateRange = ref(false)

// 最近查询
const recentQueries = ref([])

// 最近审计日志
const recentAudits = ref([])

// 加载状态
const loading = ref(true)

// echarts实例
let queryChartInstance = null
let dbTypeChartInstance = null

// 页面加载时获取数据
onMounted(() => {
  // 初始化图表
  initCharts()
  
  // 获取仪表盘数据
  getDashboardData()
  
  // 添加窗口调整大小事件监听
  window.addEventListener('resize', handleResize)
  
  // 添加延迟重绘机制，确保图表正确显示
  setTimeout(() => {
    redrawCharts()
  }, 300)
})

// 组件卸载时清理
onUnmounted(() => {
  // 移除窗口调整大小事件监听
  window.removeEventListener('resize', handleResize)
  
  // 销毁图表实例
  if (queryChartInstance) {
    queryChartInstance.dispose()
  }
  
  if (dbTypeChartInstance) {
    dbTypeChartInstance.dispose()
  }
})

// 初始化图表
const initCharts = () => {
  // 确保DOM元素存在
  const queryChartEl = document.getElementById('queryChart')
  const dbTypeChartEl = document.getElementById('dbTypeChart')
  
  if (!queryChartEl || !dbTypeChartEl) {
    // 如果DOM元素不存在，延迟初始化
    setTimeout(initCharts, 100)
    return
  }
  
  // 初始化查询统计图表
  queryChartInstance = echarts.init(queryChartEl)
  
  // 初始化数据库类型分布图表
  dbTypeChartInstance = echarts.init(dbTypeChartEl)
  
  // 设置初始图表配置，确保DOM元素准备好 - 修改为更简单的配置避免axis.getAxesOnZeroOf错误
  queryChartInstance.setOption({
    title: {
      text: '加载中...',
      left: 'center'
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '8%',
      top: '15%',
      containLabel: true
    }
  }, true)
  
  // 为饼图设置简单的初始配置
  dbTypeChartInstance.setOption({
    title: {
      text: '加载中...',
      left: 'center'
    }
  }, true)
}

// 图表重绘函数
const redrawCharts = () => {
  nextTick(() => {
    if (queryChartInstance) {
      try {
        queryChartInstance.resize();
      } catch (error) {
        console.error('查询图表重绘失败:', error);
      }
    }
    if (dbTypeChartInstance) {
      try {
        dbTypeChartInstance.resize();
      } catch (error) {
        console.error('数据库类型图表重绘失败:', error);
      }
    }
  })
}

// 处理窗口大小变化
const handleResize = () => {
  if (queryChartInstance && dbTypeChartInstance) {
    // 使用防抖动处理窗口调整事件
    clearTimeout(window.resizeTimer)
    window.resizeTimer = setTimeout(() => {
      redrawCharts()
    }, 200)
  }
}

// 处理查询周期变化
const handlePeriodChange = () => {
  useCustomDateRange.value = false
  dateRange.value = null
  getDashboardData()
  // 在数据更新后重绘图表
  setTimeout(redrawCharts, 100)
}

// 处理查询类型变化
const handleChartTypeChange = () => {
  getDashboardData()
  // 在数据更新后重绘图表
  setTimeout(redrawCharts, 100)
}

// 处理日期范围变化
const handleDateRangeChange = (val) => {
  if (val) {
    useCustomDateRange.value = true
    queryChartPeriod.value = 'custom'
  } else {
    useCustomDateRange.value = false
    queryChartPeriod.value = 'week'
  }
  getDashboardData()
  // 在数据更新后重绘图表
  setTimeout(redrawCharts, 100)
}

// 获取仪表盘数据
const getDashboardData = async () => {
  try {
    loading.value = true
    
    // 构建API请求参数
    const params = {
      period: queryChartPeriod.value
    }
    
    // 如果使用自定义日期范围
    if (useCustomDateRange.value && dateRange.value) {
      params.start_date = dateRange.value[0]
      params.end_date = dateRange.value[1]
    }
    
    const res = await getDashboardStatistics(params)
    
    // 更新统计数据
    statistics.value = res.statistics
    
    // 更新最近查询和审计日志
    recentQueries.value = res.recentQueries
    recentAudits.value = res.recentAudits
    
    // 更新图表数据
    updateQueryChart(res)
    updateDbTypeChart(res.dbTypeDistribution)
  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 更新查询统计图表
const updateQueryChart = (data) => {
  if (queryChartType.value === 'time') {
    // 总统计图表
    const option = {
      title: {
        text: getChartTitle(),
        left: 'center',
        top: 10
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          const date = params[0].axisValue;
          const value = params[0].value;
          return `${date}<br/><span style="display:inline-block;margin-right:4px;border-radius:50%;width:10px;height:10px;background-color:#409EFF;"></span>查询数量: ${value}`;
        },
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '8%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: data.queryChartData.dates,
        axisLabel: {
          interval: 0,
          rotate: 45,
          fontSize: 12,
          formatter: function(value) {
            return value.substring(5); // 只显示月-日部分
          }
        },
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '查询数量',
        nameLocation: 'end',
        nameGap: 15,
        nameTextStyle: {
          fontWeight: 'bold',
          padding: [0, 0, 0, 10]
        },
        minInterval: 1, // 最小间隔为1，确保Y轴值为整数
        splitLine: {
          lineStyle: {
            color: '#f0f0f0'
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#ddd'
          }
        },
        axisTick: {
          show: true
        }
      },
      series: [
        {
          name: '查询数量',
          type: 'line',
          data: data.queryChartData.counts,
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          showSymbol: true,
          areaStyle: {
            opacity: 0.5,
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(88, 145, 255, 0.8)' },
                { offset: 1, color: 'rgba(88, 145, 255, 0.1)' }
              ]
            }
          },
          itemStyle: {
            color: '#409EFF',
            borderWidth: 2
          },
          emphasis: {
            scale: true,
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          }
        }
      ]
    }
    
    // 先清除旧图表实例并重新初始化
    try {
      queryChartInstance.clear();
      queryChartInstance.setOption(option, true);
    } catch (error) {
      console.error('更新图表时出错:', error);
      // 如果更新失败，尝试重新创建图表实例
      const queryChartEl = document.getElementById('queryChart');
      if (queryChartEl) {
        queryChartInstance.dispose();
        queryChartInstance = echarts.init(queryChartEl);
        queryChartInstance.setOption(option, true);
      }
    }
  } else {
    // 用户查询统计图表
    const option = {
      title: {
        text: getChartTitle(true),
        left: 'center',
        top: 10
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}次 ({d}%)',
        backgroundColor: 'rgba(255,255,255,0.9)',
        borderColor: '#ccc',
        borderWidth: 1,
        textStyle: {
          color: '#333'
        }
      },
      legend: {
        type: 'scroll',
        orient: 'vertical',
        left: 10,
        top: 'middle',
        textStyle: {
          fontSize: 12
        },
        pageIconSize: 12,
        pageTextStyle: {
          fontSize: 12
        },
        formatter: function(name) {
          // 获取对应数据
          const item = data.userQueryStats.find(item => item.name === name);
          if (item) {
            return `${name} (${item.value}次)`;
          }
          return name;
        }
      },
      grid: {
        show: false,
        left: '5%',
        right: '5%',
        bottom: '8%',
        top: '15%',
        containLabel: true
      },
      series: [
        {
          name: '查询次数',
          type: 'pie',
          radius: ['35%', '65%'],
          center: ['60%', '50%'],
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 6,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          emphasis: {
            focus: 'series',
            label: {
              show: true,
              fontSize: '14',
              fontWeight: 'bold',
              formatter: '{b}: {c}次 ({d}%)'
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          data: data.userQueryStats.map(item => ({
            name: item.name,
            value: item.value
          }))
        }
      ]
    }
    
    // 先清除旧图表实例并重新初始化
    try {
      queryChartInstance.clear();
      queryChartInstance.setOption(option, true);
    } catch (error) {
      console.error('更新图表时出错:', error);
      // 如果更新失败，尝试重新创建图表实例
      const queryChartEl = document.getElementById('queryChart');
      if (queryChartEl) {
        queryChartInstance.dispose();
        queryChartInstance = echarts.init(queryChartEl);
        queryChartInstance.setOption(option, true);
      }
    }
  }
}

// 获取图表标题
const getChartTitle = (isUserChart = false) => {
  const prefix = isUserChart ? '用户查询次数统计' : '查询总统计';
  
  if (useCustomDateRange.value && dateRange.value) {
    return `${prefix} (${dateRange.value[0]} 至 ${dateRange.value[1]})`;
  } else if (queryChartPeriod.value === 'week') {
    return `本周${prefix}`;
  } else if (queryChartPeriod.value === 'month') {
    return `本月${prefix}`;
  }
  
  return prefix;
}

// 更新数据库类型分布图表
const updateDbTypeChart = (data) => {
  const option = {
    title: {
      text: '数据库类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [
      {
        name: '数据库类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  }
  
  // 先清除旧图表实例并重新初始化
  try {
    dbTypeChartInstance.clear();
    dbTypeChartInstance.setOption(option, true);
  } catch (error) {
    console.error('更新数据库类型图表时出错:', error);
    // 如果更新失败，尝试重新创建图表实例
    const dbTypeChartEl = document.getElementById('dbTypeChart');
    if (dbTypeChartEl) {
      dbTypeChartInstance.dispose();
      dbTypeChartInstance = echarts.init(dbTypeChartEl);
      dbTypeChartInstance.setOption(option, true);
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  padding: 20px 0;
}

.dashboard-row {
  margin-top: 20px;
}

.box-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-controls {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.card-body {
  text-align: center;
  padding: 10px 0;
}

.card-value {
  font-size: 30px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.card-trend {
  font-size: 14px;
  color: #909399;
}

.up {
  color: #f56c6c;
}

.down {
  color: #67c23a;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 14px;
  text-align: center;
}

@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .chart-controls > * {
    margin-top: 5px;
    margin-left: 0 !important;
  }
}
</style> 