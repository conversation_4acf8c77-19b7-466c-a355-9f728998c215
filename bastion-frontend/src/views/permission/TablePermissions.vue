<template>
  <div class="table-permissions">
    <div class="actions-bar">
      <div class="filter-container">
        <el-input 
          v-model="searchQuery" 
          placeholder="搜索用户、数据源等" 
          prefix-icon="Search" 
          clearable 
          @clear="handleSearch"
          @input="handleSearch"
          class="search-input"
        />
        
        <el-select 
          v-model="filterDatasource" 
          placeholder="选择数据源" 
          clearable
          @change="handleSearch"
          class="filter-select"
        >
          <el-option
            v-for="ds in userDatasources"
            :key="ds.id"
            :label="ds.name"
            :value="ds.id"
          />
        </el-select>
        
        <el-select 
          v-model="filterUser" 
          placeholder="选择用户" 
          clearable
          filterable
          @change="handleSearch"
          class="filter-select"
        >
          <el-option
            v-for="user in users"
            :key="user.id"
            :label="user.username"
            :value="user.id"
          />
        </el-select>
      </div>
      
      <div class="action-buttons">
        <el-button type="danger" @click="batchDeletePermissions" :disabled="!selectedPermissions.length">
          <el-icon><Delete /></el-icon> 批量删除
        </el-button>
        <el-button type="primary" @click="showAddDialog">
          <el-icon><Plus /></el-icon> 新增表权限
        </el-button>
        <el-button type="success" @click="showBatchDialog">
          <el-icon><Plus /></el-icon> 批量授权
        </el-button>
      </div>
    </div>
    
    <el-table
      v-loading="loading"
      :data="paginatedGroupedPermissions"
      border
      style="width: 100%"
      max-height="calc(100vh - 300px)"
      row-key="groupKey"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="user_username" label="用户" min-width="150" />
      <el-table-column prop="datasource_name" label="数据源" min-width="150" />
      <el-table-column label="表数量" min-width="100">
        <template #default="scope">
          {{ scope.row.permission_count || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="主要权限类型" min-width="150">
        <template #default="scope">
          {{ scope.row.permission_types || getMainPermissionTypes(scope.row.permissions) }}
        </template>
      </el-table-column>
      <el-table-column label="最近授权时间" min-width="180">
        <template #default="scope">
          {{ scope.row.latest_granted_at || getLatestGrantedAt(scope.row.permissions) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="120">
        <template #default="scope">
          <el-button 
            type="primary" 
            size="small" 
            @click="viewTablePermissions(scope.row)"
            plain
          >
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页控件 -->
    <div class="pagination-container">
      <el-pagination
        background
        layout="total, prev, pager, next"
        :total="totalGrouped" 
        :current-page.sync="currentGroupPage"
        :page-size="groupPageSize"
        @current-change="handleGroupPageChange"
      />
    </div>
    
    <!-- 新增/编辑权限对话框 -->
    <el-dialog
      :title="dialogType === 'add' ? '新增表级权限' : '编辑表级权限'"
      v-model="dialogVisible"
      width="600px"
    >
      <el-form :model="permissionForm" label-width="100px" :rules="rules" ref="permissionFormRef">
        <el-form-item label="用户" prop="user">
          <el-select v-model="permissionForm.user" filterable placeholder="选择用户" class="form-select" @change="handleUserChange">
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="数据源" prop="datasource">
          <el-select 
            v-model="permissionForm.datasource" 
            filterable 
            placeholder="选择数据源" 
            class="form-select"
            @change="handleDatasourceChange"
            :disabled="!permissionForm.user"
          >
            <el-option
              v-for="ds in targetUserDatasources"
              :key="ds.id"
              :label="ds.name"
              :value="ds.id"
            />
          </el-select>
          <div class="form-tip" v-if="permissionForm.user">
            {{ getTargetUserTip(permissionForm.user) }}
          </div>
        </el-form-item>
        
        <el-form-item label="Schema" prop="schema_name">
          <el-select 
            v-model="permissionForm.schema_name" 
            filterable 
            clearable
            placeholder="选择Schema (可选)" 
            class="form-select"
            :disabled="!permissionForm.datasource"
            @change="handleSchemaChange"
          >
            <el-option
              v-for="schema in schemas"
              :key="schema"
              :label="schema"
              :value="schema"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="表名" prop="table_name">
          <el-select 
            v-model="permissionForm.table_name" 
            filterable 
            placeholder="选择表" 
            class="form-select"
            :disabled="!permissionForm.datasource"
          >
            <el-option
              v-for="table in tables"
              :key="table.name"
              :label="table.name"
              :value="table.name"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="权限类型" prop="permission_type">
          <el-select v-model="permissionForm.permission_type" placeholder="选择权限类型" class="form-select">
            <el-option label="只读" value="read" />
            <el-option v-if="isAdmin" label="读写" value="write" />
            <el-option v-if="isAdmin" label="禁止访问" value="denied" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="说明" prop="description">
          <el-input 
            v-model="permissionForm.description" 
            type="textarea" 
            rows="2" 
            placeholder="权限说明 (可选)"
          />
        </el-form-item>

        <el-form-item v-if="dialogType === 'add'" label="授权期限">
          <p class="form-tip">默认授权有效期为 <strong>24小时</strong>，到期后权限将自动失效。</p>
        </el-form-item>

      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPermission" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 批量授权对话框 -->
    <el-dialog
      title="批量授权表权限"
      v-model="batchDialogVisible"
      width="700px"
    >
      <el-form :model="batchForm" :rules="batchRules" ref="batchFormRef" label-width="100px">
        <el-form-item label="用户" prop="user_id">
          <el-select v-model="batchForm.user_id" filterable placeholder="选择用户" class="form-select" @change="handleBatchUserChange">
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="数据源" prop="datasource_id">
          <el-select 
            v-model="batchForm.datasource_id" 
            filterable 
            placeholder="选择数据源" 
            class="form-select"
            @change="handleBatchDatasourceChange"
            :disabled="!batchForm.user_id"
          >
            <el-option
              v-for="ds in targetUserDatasources"
              :key="ds.id"
              :label="ds.name"
              :value="ds.id"
            />
          </el-select>
          <div class="form-tip" v-if="batchForm.user_id">
            {{ getTargetUserTip(batchForm.user_id) }}
          </div>
        </el-form-item>
        
        <el-form-item label="Schema" prop="schema_name">
          <el-select 
            v-model="batchForm.schema_name" 
            filterable 
            placeholder="选择Schema" 
            class="form-select"
            @change="handleBatchSchemaChange"
            :loading="schemasLoading"
          >
            <el-option
              v-for="schema in schemas"
              :key="schema"
              :label="schema"
              :value="schema"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="权限类型" prop="permission_type">
          <el-select v-model="batchForm.permission_type" placeholder="选择权限类型" class="form-select">
            <el-option label="只读" value="read" />
            <el-option v-if="isAdmin" label="读写" value="write" />
            <el-option v-if="isAdmin" label="禁止访问" value="denied" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="选择表" prop="selected_tables">
          <div v-loading="tablesLoading" class="tables-container">
            <el-checkbox v-model="selectAllTables" @change="handleSelectAllTables">全选</el-checkbox>
            <el-checkbox-group v-model="batchForm.selected_tables">
              <el-checkbox 
                v-for="table in batchTables" 
                :key="table.name" 
                :label="table.name"
              >
                {{ table.name }} ({{ table.row_count || 0 }}行)
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitBatchPermissions" :loading="batchSubmitLoading">
            批量授权
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 新增：表权限详情对话框 -->
    <el-dialog
      :title="`用户 ${currentDetailGroup?.user_username || ''} 在数据源 ${currentDetailGroup?.datasource_name || ''} 下的详细权限`"
      v-model="detailDialogVisible"
      width="80%"
      class="permission-detail-dialog"
    >
      <div v-loading="detailLoading" class="permission-detail-content">
        <!-- 内部表格搜索和筛选 -->
        <div class="inner-filter">
          <el-input
            v-model="detailSearchQuery"
            placeholder="搜索表名、Schema"
            clearable
            @input="handleDetailSearch"
            class="inner-search"
          />
          <el-select
            v-model="detailFilterType"
            placeholder="权限类型"
            clearable
            @change="handleDetailSearch"
            class="inner-filter-select"
          >
            <el-option label="只读" value="read" />
            <el-option label="读写" value="write" />
            <el-option label="禁止访问" value="denied" />
          </el-select>
        </div>
        
        <el-table 
          :data="paginatedDetailPermissions" 
          border 
          size="small"
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="70" />
          <el-table-column prop="schema_name" label="Schema" min-width="100">
            <template #default="scope">
              {{ scope.row.schema_name || '(默认)' }}
            </template>
          </el-table-column>
          <el-table-column prop="table_name" label="表名" min-width="130" />
          <el-table-column prop="permission_type" label="权限类型" width="100">
            <template #default="scope">
              <el-tag :type="getPermissionTagType(scope.row.permission_type)">
                {{ getPermissionText(scope.row.permission_type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="granted_by_username" label="授权人" min-width="120" />
          <el-table-column prop="granted_at" label="授权时间" min-width="160" />
          <el-table-column prop="description" label="说明" min-width="150" />
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                @click="editPermission(scope.row)"
                plain
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deletePermission(scope.row)"
                plain
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 内部表格的分页控件 -->
        <div class="inner-pagination">
          <el-pagination
            background
            layout="total, prev, pager, next, sizes"
            :total="filteredDetailPermissions.length"
            :current-page.sync="detailCurrentPage"
            :page-size="detailPageSize"
            :page-sizes="[10, 20, 50, 100]"
            @current-change="handleDetailPageChange"
            @size-change="handleDetailSizeChange"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Delete } from '@element-plus/icons-vue'
import { 
  getTablePermissions, 
  createTablePermission,
  updateTablePermission,
  deleteTablePermission,
  batchCreateTablePermissions,
  batchDeleteTablePermissions,
  getDatasourcePermissions
} from '@/api/permission'
import { getDatasources } from '@/api/datasource'
import { getUsers } from '@/api/user'
import { getDatabaseSchema, getSchemaList } from '@/api/datasource'

// 状态变量
const loading = ref(false)
const submitLoading = ref(false)
const permissions = ref([])
const groupedPermissions = ref([]) // 新增：用于存储聚合后的权限数据
const total = ref(0)
const totalGrouped = ref(0) // 新增：聚合后数据的总数
const currentPage = ref(1)
const currentGroupPage = ref(1) // 新增：聚合后数据的当前页
const pageSize = ref(10) 
const groupPageSize = ref(10) // 新增：聚合后数据的每页条数
const searchQuery = ref('')
const filterDatasource = ref(null)
const filterType = ref('')
const filterUser = ref(null); // 新增：用户筛选

// 新增：选中的权限列表
const selectedPermissions = ref([])

// 新增：表权限详情对话框相关状态
const detailDialogVisible = ref(false)
const detailLoading = ref(false)
const currentDetailGroup = ref(null) // 当前查看详情的分组
const detailSearchQuery = ref('')
const detailFilterType = ref('')
const detailCurrentPage = ref(1)
const detailPageSize = ref(10)
const filteredDetailPermissions = ref([])

// 下拉选项数据
const users = ref([])
const datasources = ref([])
const userDatasources = ref([]) // 当前用户有权限的数据源
const targetUserDatasources = ref([]) // 目标用户有权限的数据源
const schemas = ref([])
const tables = ref([])
const tablesLoading = ref(false)

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref('add')
const permissionFormRef = ref(null)
const permissionForm = reactive({
  user: null,
  datasource: null,
  schema_name: '',
  table_name: '',
  permission_type: 'read',
  description: ''
})

// 批量授权对话框
const batchDialogVisible = ref(false)
const batchSubmitLoading = ref(false)
const batchFormRef = ref(null)
const batchForm = reactive({
  user_id: null,
  datasource_id: null,
  schema_name: '',
  permission_type: 'read',
  selected_tables: []
})
const batchTables = ref([])
const schemasLoading = ref(false)
const selectAllTables = ref(false)

// 表单验证规则
const rules = {
  user: [{ required: true, message: '请选择用户', trigger: 'change' }],
  datasource: [{ required: true, message: '请选择数据源', trigger: 'change' }],
  table_name: [{ required: true, message: '请选择表', trigger: 'change' }],
  permission_type: [{ required: true, message: '请选择权限类型', trigger: 'change' }]
}

// 批量表单验证规则
const batchRules = {
  user_id: [{ required: true, message: '请选择用户', trigger: 'change' }],
  datasource_id: [{ required: true, message: '请选择数据源', trigger: 'change' }],
  permission_type: [{ required: true, message: '请选择权限类型', trigger: 'change' }],
  selected_tables: [{ required: true, message: '请选择至少一张表', trigger: 'change' }]
}

// 判断当前用户是否为管理员
const isAdmin = computed(() => {
  const userInfoStr = localStorage.getItem('userInfo')
  if (!userInfoStr) return false
  
  try {
    const userInfo = JSON.parse(userInfoStr)
    return userInfo.is_superuser || userInfo.is_staff || false
  } catch (e) {
    console.error('解析用户信息失败:', e)
    return false
  }
})

// 过滤后的权限列表 - 这个计算属性现在用于聚合数据
const filteredGroupedPermissions = computed(() => {
  let result = groupedPermissions.value;

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(group =>
      (group.user_username && group.user_username.toLowerCase().includes(query)) ||
      (group.datasource_name && group.datasource_name.toLowerCase().includes(query)) ||
      (group.permissions.some(p => p.table_name && p.table_name.toLowerCase().includes(query)))
    );
  }

  if (filterDatasource.value) {
    result = result.filter(group => group.datasource_id === filterDatasource.value);
  }
  
  if (filterUser.value) {
    result = result.filter(group => group.user_id === filterUser.value);
  }
  
  if (filterType.value) {
    result = result.filter(group => group.permissions.some(p => p.permission_type === filterType.value));
  }

  totalGrouped.value = result.length;
  return result;
});

const paginatedGroupedPermissions = computed(() => {
  const start = (currentGroupPage.value - 1) * groupPageSize.value;
  const end = start + groupPageSize.value;
  return filteredGroupedPermissions.value.slice(start, end);
});

// 新增：详情对话框分页数据
const paginatedDetailPermissions = computed(() => {
  const start = (detailCurrentPage.value - 1) * detailPageSize.value;
  const end = start + detailPageSize.value;
  return filteredDetailPermissions.value.slice(start, end);
});

// 生命周期
onMounted(async () => {
  await loadDropdownData()
  loadData() // loadData内部会调用groupAndFilterPermissions
})

// 加载下拉选项数据
async function loadDropdownData() {
  try {
    const [usersRes, allDatasourcesRes, userPermissionsRes] = await Promise.all([
      getUsers(),
      getDatasources(),
      getDatasourcePermissions()
    ])
    
    if (usersRes.results || usersRes.data || Array.isArray(usersRes)) {
      users.value = usersRes.results || usersRes.data || usersRes
    }
    
    if (allDatasourcesRes.results || allDatasourcesRes.data || Array.isArray(allDatasourcesRes)) {
      datasources.value = allDatasourcesRes.results || allDatasourcesRes.data || allDatasourcesRes
    }
    
    // 处理用户有权限的数据源
    if (userPermissionsRes.results || userPermissionsRes.data || Array.isArray(userPermissionsRes)) {
      const permissions = userPermissionsRes.results || userPermissionsRes.data || userPermissionsRes
      // 提取用户有权限的数据源
      const permittedDatasourceIds = new Set()
      permissions.forEach(perm => {
        // 数据源ID可能在datasource字段或其下的id子字段
        if (perm.datasource) {
          if (typeof perm.datasource === 'object' && perm.datasource.id) {
            permittedDatasourceIds.add(perm.datasource.id)
          } else if (typeof perm.datasource === 'number') {
            permittedDatasourceIds.add(perm.datasource)
          }
        }
      })
      
      // 筛选出用户有权限的数据源
      userDatasources.value = datasources.value.filter(ds => 
        permittedDatasourceIds.has(ds.id) || isAdmin.value
      )
      
      // 如果用户是管理员且筛选出来的数量为0，则使用所有数据源
      if (isAdmin.value && userDatasources.value.length === 0) {
        userDatasources.value = datasources.value
      }
    }
    
    console.log(`用户有权限的数据源: ${userDatasources.value.length}个，总数据源: ${datasources.value.length}个`)
  } catch (error) {
    console.error('加载下拉选项数据出错:', error)
    ElMessage.error('加载下拉选项数据失败')
  }
}

// 加载权限数据
async function loadData() {
  loading.value = true;
  
  try {
    // 获取用户和数据源的聚合信息，不包含详细的表权限
    const res = await getTablePermissions({ group_only: true });
    
    if (res.results || res.data || Array.isArray(res)) {
      // 处理返回的聚合数据
      const aggregatedData = res.results || res.data || res;
      groupedPermissions.value = Array.isArray(aggregatedData) ? aggregatedData : [];
      
      // 确保每个分组对象都有空的permissions数组，以备后续加载
      groupedPermissions.value.forEach(group => {
        if (!group.permissions) {
          group.permissions = [];
        }
      });
    } else {
      groupedPermissions.value = [];
      ElMessage.warning('获取数据格式异常');
    }
  } catch (error) {
    console.error('加载表级权限摘要出错:', error);
    ElMessage.error('加载表级权限摘要失败');
    groupedPermissions.value = [];
  } finally {
    loading.value = false;
  }
}

function getMainPermissionTypes(permissionsInGroup) {
  if (!permissionsInGroup || permissionsInGroup.length === 0) return '无';
  const types = new Set(permissionsInGroup.map(p => getPermissionText(p.permission_type)));
  return Array.from(types).join(', ');
}

function getLatestGrantedAt(permissionsInGroup) {
  if (!permissionsInGroup || permissionsInGroup.length === 0) return '-';
  // 假设 granted_at 是一个可以比较的日期字符串或时间戳
  return permissionsInGroup.reduce((latest, p) => {
    return p.granted_at > latest ? p.granted_at : latest;
  }, permissionsInGroup[0].granted_at);
}

// 加载Schema列表
async function loadSchemas(datasourceId) {
  schemas.value = []
  tables.value = []
  
  if (!datasourceId) return
  
  try {
    const res = await getSchemaList(datasourceId)
    
    if (res.success && res.schemas) {
      schemas.value = res.schemas
    } else if (Array.isArray(res)) {
      schemas.value = res
    } else {
      ElMessage.warning('获取Schema列表格式异常')
    }
  } catch (error) {
    console.error('加载Schema列表出错:', error)
    ElMessage.error('加载Schema列表失败')
  }
}

// 加载表列表
async function loadTables(datasourceId, schemaName) {
  tablesLoading.value = true
  tables.value = []
  
  if (!datasourceId) {
    tablesLoading.value = false
    return
  }
  
  try {
    const res = await getDatabaseSchema(datasourceId, schemaName || '')
    
    if (res.success && res.schema) {
      // 提取表和视图
      const tablesData = res.schema.tables || []
      const viewsData = res.schema.views || []
      
      // 合并并转换格式
      tables.value = [
        ...tablesData.map(t => ({ name: t.name, type: 'table' })),
        ...viewsData.map(v => ({ name: v.name, type: 'view' }))
      ]
    } else {
      ElMessage.warning('获取表列表格式异常')
    }
  } catch (error) {
    console.error('加载表列表出错:', error)
    ElMessage.error('加载表列表失败')
  } finally {
    tablesLoading.value = false
  }
}

// 显示新增对话框
function showAddDialog() {
  dialogType.value = 'add'
  resetForm()
  targetUserDatasources.value = [] // 初始不显示任何数据源，等用户选择后再加载
  dialogVisible.value = true
}

// 显示批量授权对话框
function showBatchDialog() {
  batchDialogVisible.value = true
  batchForm.user_id = null
  batchForm.datasource_id = null
  batchForm.schema_name = ''
  batchForm.permission_type = 'read'
  batchForm.selected_tables = []
  batchTables.value = []
  selectAllTables.value = false
  targetUserDatasources.value = [] // 初始不显示任何数据源，等用户选择后再加载
}

// 编辑权限
function editPermission(row) {
  dialogType.value = 'edit'
  resetForm()
  
  // 关键：确保 permissionForm 的 user 和 datasource 被正确设置，
  // 这样后续的 loadUserDatasources, loadSchemas, loadTables 才能正确加载依赖数据
  permissionForm.user = row.user // user ID
  permissionForm.datasource = row.datasource // datasource ID

  // 先加载用户相关的数据源，然后加载Schemas和Tables
  loadUserDatasources(row.user).then(() => {
    // 确保数据源列表加载完成后再设置 datasource，触发级联加载
    // 此时 permissionForm.datasource 已经被上面赋值，这里主要是为了触发change事件（如果需要）
    // 但更稳妥的方式是直接按ID加载所需数据

    Promise.all([
      loadSchemas(row.datasource), // 使用 datasource ID 加载 schemas
      loadTables(row.datasource, row.schema_name) // 使用 datasource ID 和 schema_name 加载 tables
    ]).then(() => {
      // 所有下拉数据加载完毕后，填充表单的其他字段
      permissionForm.id = row.id
      permissionForm.schema_name = row.schema_name
      permissionForm.table_name = row.table_name
      permissionForm.permission_type = row.permission_type
      permissionForm.description = row.description
      
      dialogVisible.value = true // 所有数据准备好后再显示对话框
    }).catch(error => {
      console.error('编辑权限时加载schemas或tables失败:', error);
      ElMessage.error('加载编辑数据失败，请重试。');
    });
  }).catch(error => {
    console.error('编辑权限时加载用户数据源失败:', error);
    ElMessage.error('加载用户数据源列表失败，请重试。');
  });
}

// 删除权限
async function deletePermission(row) { // row 现在是展开行内部表格的单条权限数据
  ElMessageBox.confirm(
    `确定要删除用户 "${row.user_username}" 对表 "${row.schema_name ? row.schema_name + '.' : ''}${row.table_name}" 的 "${getPermissionText(row.permission_type)}" 权限吗?`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteTablePermission(row.id) // API调用依然使用原始权限ID
      
      ElMessage.success('删除权限成功')
      // 删除成功后，需要重新加载并聚合数据
      loadData() 
    } catch (error) {
      console.error('删除权限出错11111:', error)
      ElMessage.error(`删除权限失败: ${error.message || '未知错误'}`)
    }
  }).catch(() => {})
}

// 提交权限表单
async function submitPermission() {
  if (!permissionFormRef.value) return
  
  await permissionFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    submitLoading.value = true
    
    try {
      if (dialogType.value === 'add') {
        await createTablePermission(permissionForm)
        ElMessage.success('创建表权限成功')
      } else {
        await updateTablePermission(permissionForm.id, permissionForm)
        ElMessage.success('更新表权限成功')
      }
      
      dialogVisible.value = false
      loadData() // 重新加载并聚合数据
    } catch (error) {
      console.error('提交表权限出错:', error)
      ElMessage.error(`操作失败: ${error.message || '未知错误'}`)
    } finally {
      submitLoading.value = false
    }
  })
}

// 提交批量授权表单
async function submitBatchPermissions() {
  if (!batchFormRef.value) return
  
  try {
    await batchFormRef.value.validate()
    
    if (batchForm.selected_tables.length === 0) {
      ElMessage.warning('请至少选择一张表')
      return
    }
    
    batchSubmitLoading.value = true
    
    const tablesToGrant = batchForm.selected_tables.map(tableName => ({
      schema_name: batchForm.schema_name, // schema_name 可能为空字符串，后端应能处理
      table_name: tableName
    }))
    
    await batchCreateTablePermissions({
      user_id: batchForm.user_id,
      datasource_id: batchForm.datasource_id,
      tables: tablesToGrant,
      permission_type: batchForm.permission_type
    })
    
    ElMessage.success(`成功批量授权 ${tablesToGrant.length} 张表`)
    batchDialogVisible.value = false
    loadData() // 重新加载并聚合数据
  } catch (error) {
    console.error('批量授权出错:', error)
    ElMessage.error('批量授权失败: ' + (error.message || '未知错误'))
  } finally {
    batchSubmitLoading.value = false
  }
}

// 处理数据源变更
async function handleDatasourceChange(datasourceId) {
  permissionForm.schema_name = ''
  permissionForm.table_name = ''
  
  if (datasourceId) {
    await loadSchemas(datasourceId)
  }
}

// 处理Schema变更
async function handleSchemaChange(schemaName) {
  permissionForm.table_name = ''
  
  if (permissionForm.datasource) {
    await loadTables(permissionForm.datasource, schemaName)
  }
}

// 处理批量数据源变更
async function handleBatchDatasourceChange(datasourceId) {
  batchForm.schema_name = ''
  batchForm.selected_tables = []
  batchTables.value = []
  
  if (datasourceId) {
    schemasLoading.value = true
    try {
      const res = await getSchemaList(datasourceId)
      
      if (Array.isArray(res)) {
        schemas.value = res
      } else if (res.data && Array.isArray(res.data)) {
        schemas.value = res.data
      } else if (typeof res === 'object') {
        // 尝试找到数组属性
        for (const key in res) {
          if (Array.isArray(res[key])) {
            schemas.value = res[key]
            break
          }
        }
      } else {
        schemas.value = []
        ElMessage.warning('获取Schema列表格式异常')
      }
    } catch (error) {
      console.error('加载Schema列表出错:', error)
      ElMessage.error('加载Schema列表失败')
      schemas.value = []
    } finally {
      schemasLoading.value = false
    }
  } else {
    schemas.value = []
  }
}

// 处理批量Schema变更
async function handleBatchSchemaChange(schema) {
  batchForm.selected_tables = []
  batchTables.value = []
  selectAllTables.value = false
  
  if (batchForm.datasource_id && schema) {
    tablesLoading.value = true
    try {
      const res = await getDatabaseSchema(batchForm.datasource_id, schema)
      
      if (res.success && res.schema) {
        batchTables.value = res.schema.tables || []
      } else {
        batchTables.value = []
        ElMessage.warning('获取表列表格式异常')
      }
    } catch (error) {
      console.error('加载表列表出错:', error)
      ElMessage.error('加载表列表失败')
      batchTables.value = []
    } finally {
      tablesLoading.value = false
    }
  }
}

// 处理全选表
function handleSelectAllTables(val) {
  if (val) {
    batchForm.selected_tables = batchTables.value.map(table => table.name)
  } else {
    batchForm.selected_tables = []
  }
}

// 重置表单
function resetForm() {
  if (permissionFormRef.value) {
    permissionFormRef.value.resetFields()
  }
  
  permissionForm.id = null
  permissionForm.user = null
  permissionForm.datasource = null
  permissionForm.schema_name = ''
  permissionForm.table_name = ''
  permissionForm.permission_type = 'read'
  permissionForm.description = ''
  
  schemas.value = []
  tables.value = []
}

// 重置批量表单
function resetBatchForm() {
  if (batchFormRef.value) {
    batchFormRef.value.resetFields()
  }
  
  batchForm.user_id = null
  batchForm.datasource_id = null
  batchForm.schema_name = ''
  batchForm.permission_type = 'read'
  batchForm.selected_tables = []
  
  schemas.value = []
  batchTables.value = []
}

// 处理搜索 - 现在它会触发 filteredGroupedPermissions 的重新计算
function handleSearch() {
  currentGroupPage.value = 1; // 搜索时回到第一页
  // filteredGroupedPermissions 会自动重新计算
}

// 处理分页变更 - 现在用于聚合后的数据
function handleGroupPageChange(page) {
  currentGroupPage.value = page;
}

// 获取权限标签类型
function getPermissionTagType(type) {
  const types = {
    'read': 'info',
    'write': 'success',
    'denied': 'danger'
  }
  return types[type] || 'info'
}

// 获取权限文本
function getPermissionText(type) {
  const texts = {
    'read': '只读',
    'write': '读写',
    'denied': '禁止访问'
  }
  return texts[type] || type
}

// 处理用户选择变更
async function handleUserChange(userId) {
  permissionForm.datasource = null
  permissionForm.schema_name = ''
  permissionForm.table_name = ''
  schemas.value = []
  tables.value = []
  
  if (userId) {
    await loadUserDatasources(userId)
  } else {
    targetUserDatasources.value = []
  }
}

// 处理批量用户选择变更
async function handleBatchUserChange(userId) {
  batchForm.datasource_id = null
  batchForm.schema_name = ''
  batchForm.selected_tables = []
  batchTables.value = []
  schemas.value = []
  
  if (userId) {
    await loadUserDatasources(userId)
  } else {
    targetUserDatasources.value = []
  }
}

// 加载指定用户有权限的数据源
async function loadUserDatasources(userId) {
  try {
    // 获取被授权用户的权限信息
    const permissionsRes = await getDatasourcePermissions({ user_id: userId })
    
    // 检查被授权用户是否为管理员
    const targetUserInfo = users.value.find(user => user.id === userId)
    const isTargetUserAdmin = targetUserInfo && (targetUserInfo.is_superuser || targetUserInfo.is_staff)
    
    if (isTargetUserAdmin) {
      // 如果被授权用户是管理员，显示所有数据源
      targetUserDatasources.value = datasources.value
      console.log(`被授权用户是管理员：显示所有${datasources.value.length}个数据源`)
      return
    }
    
    // 对于非管理员用户，只显示他们已有权限的数据源
    if (permissionsRes.results || permissionsRes.data || Array.isArray(permissionsRes)) {
      const userPerms = permissionsRes.results || permissionsRes.data || permissionsRes
      
      // 提取用户有权限的数据源ID
      const permittedDatasourceIds = new Set()
      userPerms.forEach(perm => {
        if (perm.datasource) {
          if (typeof perm.datasource === 'object' && perm.datasource.id) {
            permittedDatasourceIds.add(perm.datasource.id)
          } else if (typeof perm.datasource === 'number') {
            permittedDatasourceIds.add(perm.datasource)
          }
        }
      })
      
      // 筛选出用户有权限的数据源
      targetUserDatasources.value = datasources.value.filter(ds => 
        permittedDatasourceIds.has(ds.id)
      )
      
      console.log(`普通用户：已有权限的数据源${targetUserDatasources.value.length}个，总数据源${datasources.value.length}个`)
    } else {
      targetUserDatasources.value = []
      console.log('未找到目标用户的数据源权限')
    }
  } catch (error) {
    console.error('加载用户数据源权限出错:', error)
    ElMessage.error('加载用户数据源权限失败')
    targetUserDatasources.value = []
  }
}

// 获取目标用户的提示信息
function getTargetUserTip(userId) {
  // 检查目标用户是否为管理员
  const targetUser = users.value.find(user => user.id === userId)
  const isTargetUserAdmin = targetUser && (targetUser.is_superuser || targetUser.is_staff)
  
  if (isTargetUserAdmin) {
    return '管理员用户可以访问所有数据源'
  } else if (targetUserDatasources.value.length === 0) {
    return '该用户没有任何数据源权限，请先授予数据源权限'
  } else {
    return `该用户有权限访问 ${targetUserDatasources.value.length} 个数据源`
  }
}

// 新增：查看表权限详情
async function viewTablePermissions(group) {
  detailLoading.value = true;
  detailDialogVisible.value = true;
  currentDetailGroup.value = group;
  
  // 重置详情页面的筛选和分页状态
  detailSearchQuery.value = '';
  detailFilterType.value = '';
  detailCurrentPage.value = 1;
  
  try {
    // 如果该组合的权限数据尚未加载，则从后端获取
    if (!group.permissions || group.permissions.length === 0 || !group.detailsLoaded) {
      // 调用API获取特定用户和数据源的表权限详情
      const response = await getTablePermissions({
        user_id: group.user_id,
        datasource_id: group.datasource_id,
        details: true
      });
      
      // 处理返回的数据
      let detailPermissions = [];
      if (response.results || response.data || Array.isArray(response)) {
        detailPermissions = response.results || response.data || response;
      }
      
      // 更新组合的权限数据
      group.permissions = Array.isArray(detailPermissions) ? detailPermissions : [];
      group.detailsLoaded = true; // 标记为已加载详情
      
      // 同时更新原始数据中的权限信息
      const index = groupedPermissions.value.findIndex(g => g.groupKey === group.groupKey);
      if (index !== -1) {
        groupedPermissions.value[index].permissions = group.permissions;
        groupedPermissions.value[index].detailsLoaded = true;
      }
    }
    
    // 设置当前详情权限数据
    filteredDetailPermissions.value = [...group.permissions];
  } catch (error) {
    console.error('加载表权限详情出错:', error);
    ElMessage.error('加载表权限详情失败');
    filteredDetailPermissions.value = [];
  } finally {
    detailLoading.value = false;
  }
}


// 新增：处理详情搜索
function handleDetailSearch() {
  detailCurrentPage.value = 1; // 搜索时回到第一页
  
  if (!currentDetailGroup.value) return;
  
  // 筛选数据
  let result = [...currentDetailGroup.value.permissions];
  
  // 搜索表名和Schema
  if (detailSearchQuery.value) {
    const query = detailSearchQuery.value.toLowerCase();
    result = result.filter(item => 
      (item.table_name && item.table_name.toLowerCase().includes(query)) ||
      (item.schema_name && item.schema_name.toLowerCase().includes(query))
    );
  }
  
  // 按权限类型筛选
  if (detailFilterType.value) {
    result = result.filter(item => item.permission_type === detailFilterType.value);
  }
  
  filteredDetailPermissions.value = result;
}

// 新增：处理详情页码变化
function handleDetailPageChange(page) {
  detailCurrentPage.value = page;
}

// 新增：处理详情每页条数变化
function handleDetailSizeChange(size) {
  detailPageSize.value = size;
  detailCurrentPage.value = 1; // 更改每页条数时重置到第一页
}

// 新增：处理表格选择变更
async function handleSelectionChange(selection) {
  selectedPermissions.value = selection
  
  // 为每个选中的组加载权限详情
  for (const group of selection) {
    if (!group.permissions || group.permissions.length === 0 || !group.detailsLoaded) {
      try {
        const response = await getTablePermissions({
          user_id: group.user_id,
          datasource_id: group.datasource_id,
          details: true
        });
        
        let detailPermissions = [];
        if (response.results || response.data || Array.isArray(response)) {
          detailPermissions = response.results || response.data || response;
        }
        
        group.permissions = Array.isArray(detailPermissions) ? detailPermissions : [];
        group.detailsLoaded = true;
        
        const index = groupedPermissions.value.findIndex(g => g.groupKey === group.groupKey);
        if (index !== -1) {
          groupedPermissions.value[index].permissions = group.permissions;
          groupedPermissions.value[index].detailsLoaded = true;
        }
      } catch (error) {
        console.error('加载表权限详情出错:', error);
        ElMessage.error(`加载用户 ${group.user_username} 的权限详情失败`);
      }
    }
  }
}

// 新增：批量删除权限
async function batchDeletePermissions() {
  if (!selectedPermissions.value.length) {
    ElMessage.warning('请至少选择一条权限记录')
    return
  }

  // 收集所有选中组的权限ID
  const permissionIds = []
  let totalPermissions = 0
  
  for (const group of selectedPermissions.value) {
    if (group.permissions && group.permissions.length) {
      group.permissions.forEach(perm => {
        permissionIds.push(perm.id)
      })
      totalPermissions += group.permissions.length
    }
  }

  if (!permissionIds.length) {
    ElMessage.warning('未找到可删除的权限记录')
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${totalPermissions} 条表级权限吗？此操作不可恢复！`,
    '批量删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 调用批量删除API
      await batchDeleteTablePermissions(permissionIds)
      ElMessage.success('批量删除权限成功')
      // 重新加载数据
      loadData()
    } catch (error) {
      console.error('批量删除权限出错:', error)
      ElMessage.error(`批量删除失败: ${error.message || '未知错误'}`)
    }
  }).catch(() => {})
}

// 暴露给父组件的方法
defineExpose({
  loadData
})
</script>

<style scoped>
.table-permissions {
  padding: 20px;
}

.actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  gap: 10px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.search-input {
  width: 250px;
}

.filter-select {
  width: 180px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.form-select {
  width: 100%;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
}

.permission-detail-content {
  padding: 10px 0;
}

.inner-filter {
  display: flex;
  margin-bottom: 10px;
  gap: 10px;
}

.inner-search {
  width: 220px;
}

.inner-filter-select {
  width: 150px;
}

.inner-pagination {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

/* 详情对话框样式 */
.permission-detail-dialog :deep(.el-dialog__body) {
  padding-top: 10px;
}

.tables-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  padding: 10px;
  border-radius: 4px;
}
</style> 