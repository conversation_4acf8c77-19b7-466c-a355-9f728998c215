<template>
  <div class="permission-manager">
    <el-card class="page-header">
      <div class="header-content">
        <h2><el-icon><Lock /></el-icon> 权限管理</h2>
        <div class="header-actions">
          <el-button type="primary" @click="refreshData">
            <el-icon><Refresh /></el-icon> 刷新
          </el-button>
        </div>
      </div>
    </el-card>
    
    <div v-if="!isAdmin" class="no-permission">
      <el-empty 
        description="您暂时没有访问此页面的权限" 
        :image-size="200"
      >
        <template #description>
          <p>很抱歉，您暂时没有访问此页面的权限，只有系统管理员可以管理权限</p>
          <p>如需帮助，请联系系统管理员</p>
        </template>
        <el-button type="primary" @click="goToDashboard">返回首页</el-button>
      </el-empty>
    </div>
    
    <el-tabs v-if="isAdmin" v-model="activeTab" class="permission-tabs">
      <el-tab-pane label="数据源权限" name="datasource">
        <datasource-permissions ref="datasourcePermissionsRef" />
      </el-tab-pane>
      <el-tab-pane label="表级权限" name="table">
        <table-permissions ref="tablePermissionsRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Lock, Refresh } from '@element-plus/icons-vue'
import DatasourcePermissions from './DatasourcePermissions.vue'
import TablePermissions from './TablePermissions.vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const isAdmin = ref(false)

// 当前活动标签页
const activeTab = ref('datasource')

// 组件引用
const datasourcePermissionsRef = ref(null)
const tablePermissionsRef = ref(null)

// 检查用户是否为管理员
function checkAdminPermission() {
  const userInfoStr = localStorage.getItem('userInfo')
  let userInfo = {}
  
  try {
    userInfo = userInfoStr ? JSON.parse(userInfoStr) : {}
    isAdmin.value = userInfo.is_superuser || userInfo.is_staff || false
  } catch (e) {
    console.error('解析用户信息失败:', e)
    isAdmin.value = false
  }
  
  console.log('用户权限检查 - 是否管理员:', isAdmin.value)
}

// 返回首页
function goToDashboard() {
  router.push('/dashboard')
}

// 刷新数据
function refreshData() {
  if (!isAdmin.value) return
  
  if (activeTab.value === 'datasource' && datasourcePermissionsRef.value) {
    datasourcePermissionsRef.value.loadData()
    ElMessage.success('数据源权限已刷新')
  } else if (activeTab.value === 'table' && tablePermissionsRef.value) {
    tablePermissionsRef.value.loadData()
    ElMessage.success('表级权限已刷新')
  }
}

// 页面加载时检查权限
onMounted(() => {
  checkAdminPermission()
})
</script>

<style scoped>
.permission-manager {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 20px;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.permission-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.permission-tabs :deep(.el-tabs__content) {
  flex: 1;
  padding: 20px 0;
  overflow: auto;
}

.permission-tabs :deep(.el-tab-pane) {
  height: 100%;
}

.no-permission {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}
</style>