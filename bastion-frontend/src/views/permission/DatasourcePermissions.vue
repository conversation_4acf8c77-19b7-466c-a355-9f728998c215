<template>
  <div class="datasource-permissions">
    <div class="actions-bar">
      <div class="filter-container">
        <el-input 
          v-model="searchQuery" 
          placeholder="搜索用户名、数据源等" 
          prefix-icon="Search" 
          clearable 
          @clear="handleSearch"
          @input="handleSearch"
          class="search-input"
        />
      </div>
      
      <el-button type="primary" @click="showAddDialog">
        <el-icon><Plus /></el-icon> 新增权限
      </el-button>
    </div>
    
    <el-table
      v-loading="loading"
      :data="filteredPermissions"
      border
      style="width: 100%"
      max-height="calc(100vh - 300px)"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="user_username" label="用户" min-width="120" />
      <el-table-column prop="datasource_name" label="数据源" min-width="150" />
      
      <el-table-column prop="granted_by_username" label="授权人" min-width="120" />
      <el-table-column prop="granted_at" label="授权时间" min-width="180" />
      
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button
            type="danger"
            size="small"
            @click="deletePermission(scope.row)"
            plain
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页控件 -->
    <div class="pagination-container">
      <el-pagination
        background
        layout="total, prev, pager, next"
        :total="total"
        :current-page.sync="currentPage"
        :page-size="pageSize"
        @current-change="handlePageChange"
      />
    </div>
    
    <!-- 新增权限对话框 -->
    <el-dialog
      title="新增数据源权限"
      v-model="dialogVisible"
      width="500px"
    >
      <el-form :model="permissionForm" label-width="100px" :rules="rules" ref="permissionFormRef">
        <el-form-item label="用户" prop="user">
          <el-select v-model="permissionForm.user" filterable placeholder="选择用户" class="form-select">
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="数据源" prop="datasource">
          <el-select v-model="permissionForm.datasource" filterable placeholder="选择数据源" class="form-select">
            <el-option
              v-for="ds in datasources"
              :key="ds.id"
              :label="ds.name"
              :value="ds.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPermission" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { 
  getDatasourcePermissions, 
  createDatasourcePermission,
  deleteDatasourcePermission
} from '@/api/permission'
import { getDatasources } from '@/api/datasource'
import { getUsers } from '@/api/user'

// 状态变量
const loading = ref(false)
const submitLoading = ref(false)
const permissions = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')

// 判断当前用户是否为管理员
const isAdmin = computed(() => {
  const userInfoStr = localStorage.getItem('userInfo')
  if (!userInfoStr) return false
  
  try {
    const userInfo = JSON.parse(userInfoStr)
    return userInfo.is_superuser || userInfo.is_staff || false
  } catch (e) {
    console.error('解析用户信息失败:', e)
    return false
  }
})

// 对话框相关
const dialogVisible = ref(false)
const permissionFormRef = ref(null)
const permissionForm = reactive({
  user: null,
  datasource: null,
  permission_type: 'read' // 保留默认值为read，但在界面上不再显示选择
})

// 下拉选项数据
const users = ref([])
const datasources = ref([])

// 表单验证规则
const rules = {
  user: [{ required: true, message: '请选择用户', trigger: 'change' }],
  datasource: [{ required: true, message: '请选择数据源', trigger: 'change' }]
}

// 过滤后的权限列表
const filteredPermissions = computed(() => {
  let result = permissions.value
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(item => 
      (item.user_username && item.user_username.toLowerCase().includes(query)) ||
      (item.datasource_name && item.datasource_name.toLowerCase().includes(query)) ||
      (item.granted_by_username && item.granted_by_username.toLowerCase().includes(query))
    )
  }
  
  // 执行分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  
  total.value = result.length
  return result.slice(start, end)
})

// 生命周期
onMounted(async () => {
  await loadDropdownData()
  loadData()
})

// 加载下拉选项数据
async function loadDropdownData() {
  try {
    const [usersRes, datasourcesRes] = await Promise.all([
      getUsers(),
      getDatasources()
    ])
    
    if (usersRes.results || usersRes.data || Array.isArray(usersRes)) {
      users.value = usersRes.results || usersRes.data || usersRes
    }
    
    if (datasourcesRes.results || datasourcesRes.data || Array.isArray(datasourcesRes)) {
      datasources.value = datasourcesRes.results || datasourcesRes.data || datasourcesRes
    }
  } catch (error) {
    console.error('加载下拉选项数据出错:', error)
    ElMessage.error('加载下拉选项数据失败')
  }
}

// 加载权限数据
async function loadData() {
  loading.value = true
  
  try {
    const res = await getDatasourcePermissions()
    
    if (res.results || res.data || Array.isArray(res)) {
      permissions.value = res.results || res.data || res
    } else {
      permissions.value = []
      ElMessage.warning('获取数据格式异常')
    }
  } catch (error) {
    console.error('加载数据源权限出错:', error)
    ElMessage.error('加载数据源权限失败')
    permissions.value = []
  } finally {
    loading.value = false
  }
}

// 显示新增对话框
function showAddDialog() {
  resetForm()
  dialogVisible.value = true
}

// 删除权限
function deletePermission(row) {
  ElMessageBox.confirm(
    `确定要删除用户 "${row.user_username}" 对数据源 "${row.datasource_name}" 的权限吗？\n注意：此操作将同时删除该用户在此数据源下的所有表级权限！`,
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true
    }
  ).then(async () => {
    try {
      await deleteDatasourcePermission(row.id)
      ElMessage.success('删除权限成功')
      loadData()
    } catch (error) {
      console.error('删除权限出错:', error)
      ElMessage.error(`删除权限失败: ${error.message || '未知错误'}`)
    }
  }).catch(() => {})
}

// 提交权限表单
async function submitPermission() {
  if (!permissionFormRef.value) return
  
  await permissionFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    submitLoading.value = true
    
    try {
      // 创建新权限
      await createDatasourcePermission(permissionForm)
      ElMessage.success('创建权限成功')
      
      dialogVisible.value = false
      loadData()
    } catch (error) {
      console.error('提交权限出错:', error)
      ElMessage.error(`操作失败: ${error.message || '未知错误'}`)
    } finally {
      submitLoading.value = false
    }
  })
}

// 重置表单
function resetForm() {
  if (permissionFormRef.value) {
    permissionFormRef.value.resetFields()
  }
  
  permissionForm.user = null
  permissionForm.datasource = null
  permissionForm.permission_type = 'read' // 保持默认值
}

// 处理搜索
function handleSearch() {
  currentPage.value = 1
}

// 处理分页变化
function handlePageChange(page) {
  currentPage.value = page
}

// 暴露给父组件的方法
defineExpose({
  loadData
})
</script>

<style scoped>
.datasource-permissions {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.actions-bar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-container {
  display: flex;
  gap: 10px;
}

.search-input {
  width: 240px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.form-select {
  width: 100%;
}
</style> 