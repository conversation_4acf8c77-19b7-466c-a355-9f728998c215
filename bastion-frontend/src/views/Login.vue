<template>
  <div class="login-container">
    <div class="login-form-wrapper">
      <div class="login-header">
        <h1 class="login-title">数据库堡垒机</h1>
        <p class="login-subtitle">安全、高效的数据库访问管理平台</p>
        <!-- <div class="login-message">
          <el-alert
            title="欢迎使用"
            type="success"
            description="请使用您的账号登录系统，如有问题请联系系统管理员"
            :closable="false"
            show-icon
          />
        </div> -->
      </div>
      
      <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef" class="login-form">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" prefix-icon="el-icon-user" placeholder="用户名" />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input v-model="loginForm.password" prefix-icon="el-icon-lock" type="password" placeholder="密码" show-password />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" class="login-button" :loading="loading" @click="handleLogin">登录</el-button>
        </el-form-item>
        
        <div class="login-options">
          <el-checkbox v-model="loginForm.remember">记住我</el-checkbox>
          <el-button type="text" @click="switchToRegister">注册新账号</el-button>
        </div>
      </el-form>
      
      <el-dialog v-model="registerVisible" title="注册新账号" width="400px" :append-to-body="true">
        <el-form :model="registerForm" :rules="registerRules" ref="registerFormRef" label-width="80px">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="registerForm.username" placeholder="请输入用户名" />
          </el-form-item>
          
          <el-form-item label="密码" prop="password">
            <el-input v-model="registerForm.password" type="password" placeholder="请输入密码" show-password />
          </el-form-item>
          
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input v-model="registerForm.confirmPassword" type="password" placeholder="请再次输入密码" show-password />
          </el-form-item>
          
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="registerForm.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-form>
        
        <template #footer>
          <el-button @click="registerVisible = false">取消</el-button>
          <el-button type="primary" :loading="loading" @click="handleRegister">注册</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '../store/user'
import { login, register } from '../api/auth'

const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)
const registerVisible = ref(false)
const loginFormRef = ref(null)
const registerFormRef = ref(null)

// 获取重定向路径
const redirect = ref(router.currentRoute.value.query.redirect || '')

// 登录表单
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// 注册表单
const registerForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  email: ''
})

// 登录表单校验规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码至少6个字符', trigger: 'blur' }
  ]
}

// 注册表单校验规则
const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, message: '用户名至少3个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码至少6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 切换到注册
const switchToRegister = () => {
  registerVisible.value = true
}

// 登录处理
const handleLogin = async () => {
  try {
    await loginFormRef.value.validate()
    
    loading.value = true
    const result = await userStore.login({
      username: loginForm.username,
      password: loginForm.password
    })
    
    if (result.success) {
      ElMessage.success('登录成功')
      
      // 更新最后活动时间
      localStorage.setItem('lastActiveTime', Date.now().toString())
      
      // 获取用户信息
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
      const isAdmin = userInfo.is_superuser || userInfo.is_staff || false
      
      // 如果有重定向路径，优先跳转到重定向路径
      if (redirect.value) {
        router.push(redirect.value)
      } else {
        // 否则根据用户权限跳转到不同页面
        router.push(isAdmin ? '/dashboard' : '/query')
      }
    } else {
      ElMessage.error(result.message || '登录失败，请检查用户名和密码')
    }
  } catch (error) {
    // 表单验证失败
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 注册处理
const handleRegister = async () => {
  try {
    await registerFormRef.value.validate()
    
    loading.value = true
    const response = await register({
      username: registerForm.username,
      password: registerForm.password,
      email: registerForm.email
    })
    
    if (response.success) {
      ElMessage.success('注册成功，请登录')
      registerVisible.value = false
      
      // 填充登录表单
      loginForm.username = registerForm.username
      loginForm.password = registerForm.password
      
      // 清空注册表单
      registerForm.username = ''
      registerForm.password = ''
      registerForm.confirmPassword = ''
      registerForm.email = ''
    } else {
      ElMessage.error(response.message || '注册失败')
    }
  } catch (error) {
    console.error('注册失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
  background-image: linear-gradient(to bottom right, #3a8ee6, #5fb878);
}

.login-form-wrapper {
  width: 400px;
  padding: 30px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #303133;
}

.login-subtitle {
  font-size: 14px;
  color: #909399;
}

.login-message {
  margin: 20px 0 10px;
}

.login-form {
  margin-bottom: 20px;
}

.login-button {
  width: 100%;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style> 