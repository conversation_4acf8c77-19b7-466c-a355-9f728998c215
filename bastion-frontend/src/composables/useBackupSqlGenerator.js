import { ElMessage } from 'element-plus';

const formatSqlValue = (value, fieldName = '') => {
  if (value === null || value === undefined) {
    return 'NULL';
  }
  
  // 处理日期时间格式，确保与后端一致
  if (value instanceof Date) {
    // 使用24小时制格式化日期时间，与后端保持一致
    const pad = (num) => num.toString().padStart(2, '0');
    const formattedDate = `${value.getFullYear()}-${pad(value.getMonth() + 1)}-${pad(value.getDate())} ${pad(value.getHours())}:${pad(value.getMinutes())}:${pad(value.getSeconds())}`;
    console.log(`日期时间格式化为: ${formattedDate}`);
    return `'${formattedDate}'`;
  }
  
  // 检测时间字符串并标准化格式
  if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}/.test(value)) {
    // 将 'T' 替换为空格，确保格式一致性
    const formattedDate = value.replace('T', ' ').substring(0, 19);
    console.log(`时间字符串标准化为: ${formattedDate}`);
    return `'${formattedDate}'`;
  }
  
  // 处理大整数 - JavaScript中超过安全整数范围的值会丢失精度
  // 始终使用字符串表示大整数，但不修改其值
  if (typeof value === 'number') {
    // 如果是大整数（超过JS安全整数范围或接近该范围），则使用字符串表示
    if (Math.abs(value) > Number.MAX_SAFE_INTEGER / 10) {
      console.log(`大整数转为字符串(保持原值): ${value}`);
      return `'${value.toString()}'`;
    }
    return value;
  }
  
  // 检测字符串值是否可能是大整数（数字字符串且长度大于等于15位）
  if (typeof value === 'string') {
    // 大整数字符串处理：纯数字且长度>=15 (接近JS安全整数范围)
    if (/^\d{15,}$/.test(value)) {
      console.log(`处理大整数字符串(保持原值): ${value}`);
      return `'${value}'`;
    }
    
    // 处理以数字开头但混合了其他字符的ID字符串
    if (/^\d{10,}[a-zA-Z0-9_-]*$/.test(value)) {
      console.log(`处理混合ID字符串(保持原值): ${value}`);
      return `'${value}'`;
    }
  }
  
  // Escape single quotes by doubling them up for SQL strings
  const escapedValue = String(value).replace(/'/g, "''");
  return `'${escapedValue}'`;
};

export const useBackupSqlGenerator = () => {
  const generateInsertSql = (tableName, dataRows) => {
    console.log('generateInsertSql 开始生成备份:', { 
      tableName, 
      dataRowsLength: dataRows ? dataRows.length : 0,
      dataRowsType: typeof dataRows
    });
    
    if (!tableName || !Array.isArray(dataRows) || dataRows.length === 0) {
      console.error('generateInsertSql 失败 - 表名为空或数据行不是数组或为空', {
        tableName,
        isArray: Array.isArray(dataRows),
        length: dataRows ? dataRows.length : 0
      });
      return '';
    }
    
    // 增加日志，确保使用的是正确的表名
    console.log(`generateInsertSql - 使用的表名: ${tableName}`);
    if (tableName.includes('craftsman_year_report') && !dataRows[0].hasOwnProperty('craftsman_name')) {
      console.warn('警告：可能使用了错误的表名！数据行不包含该表的字段');
      console.warn('数据行的字段:', Object.keys(dataRows[0]).join(', '));
    }

    try {
      console.log('检查数据行的第一行:', dataRows[0]);
      const columns = Object.keys(dataRows[0]);
      console.log('提取得到的列:', columns);
      
      const columnList = columns.map(col => `\`${col}\``).join(', ');
      console.log('列清单生成完成, 开始生成数据行');

      const valuesList = dataRows.map((row, idx) => {
        try {
          const rowValues = columns.map(col => formatSqlValue(row[col])).join(', ');
          return `(${rowValues})`;
        } catch (rowError) {
          console.error(`处理第 ${idx} 行时出错:`, rowError, '行内容:', row);
          return `(/* 处理行 ${idx} 出错 */)`;
        }
      }).join(',\n');

      const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
      
      let sql = `-- Backup generated on ${timestamp}\n`;
      sql += `-- Data for table \`${tableName}\`\n\n`;
      // 修复：表名不加反引号，库名.表名也不加反引号
      let cleanTableName = tableName.replace(/`/g, '');
      sql += `INSERT INTO ${cleanTableName} (${columnList}) VALUES\n${valuesList};`;

      console.log('备份SQL生成成功，长度:', sql.length);
      return sql;
    } catch (error) {
      console.error('生成INSERT SQL时出错:', error);
      return '';
    }
  };

  const downloadSqlFile = (sqlContent, tableName) => {
    if (!sqlContent) return;

    const blob = new Blob([sqlContent], { type: 'application/sql;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    link.href = url;
    link.setAttribute('download', `backup_${tableName}_${timestamp}.sql`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const generateBackupSelectSql = (dmlSql) => {
    if (typeof dmlSql !== 'string' || !dmlSql.trim()) {
      return null;
    }

    const sqlUpper = dmlSql.toUpperCase().trim();
    let backupSql = null;
    let tableName = null;

    try {
      if (sqlUpper.startsWith('UPDATE')) {
        // Try to match: UPDATE (potential comments) table_name (potential alias) SET ... WHERE ...
        // This regex is basic and might need refinement for complex table names or schemas.
        const updateRegex = /UPDATE\s*(?:[\s\S]*?--.*?\n|[\s\S]*?\/\*[\s\S]*?\*\/)*\s*([a-zA-Z0-9_."`]+)(?:\s+[a-zA-Z0-9_]+)?\s+SET\s+[\s\S]*?(WHERE\s+[\s\S]+)?$/i;
        const match = dmlSql.match(updateRegex);
        if (match) {
          tableName = match[1];
          const whereClause = match[2] ? match[2].trim() : ''; // WHERE clause or empty
          if (whereClause) {
            backupSql = `SELECT * FROM ${tableName} ${whereClause};`;
          } else {
            // If no WHERE clause, backup the whole table (potentially dangerous for large tables)
            // Consider adding a warning or making this configurable
            backupSql = `SELECT * FROM ${tableName};`;
            ElMessage.warning(`为UPDATE语句生成了全表备份SQL，因为未找到WHERE子句。请谨慎操作。`);
          }
        }
      } else if (sqlUpper.startsWith('DELETE')) {
        // Try to match: DELETE (potential comments) FROM table_name WHERE ...
        const deleteRegex = /DELETE\s*(?:[\s\S]*?--.*?\n|[\s\S]*?\/\*[\s\S]*?\*\/)*\s*FROM\s+([a-zA-Z0-9_."`]+)\s*(WHERE\s+[\s\S]+)?$/i;
        const match = dmlSql.match(deleteRegex);
        if (match) {
          tableName = match[1];
          const whereClause = match[2] ? match[2].trim() : ''; // WHERE clause or empty
          if (whereClause) {
            backupSql = `SELECT * FROM ${tableName} ${whereClause};`;
          } else {
            // If no WHERE clause, backup the whole table
            backupSql = `SELECT * FROM ${tableName};`;
            ElMessage.warning(`为DELETE语句生成了全表备份SQL，因为未找到WHERE子句。请谨慎操作。`);
          }
        }
      }

      if (backupSql) {
        return { backupSql, tableName: tableName || 'unknown_table' };
      }
      return null;

    } catch (error) {
      console.error("Error generating backup SQL:", error);
      ElMessage.error("生成备份SQL时出错。");
      return null;
    }
  };

  // 添加生成UPDATE语句的函数
  const generateUpdateSql = (tableName, whereConditions, setValues, rowData) => {
    if (!tableName) {
      console.error('generateUpdateSql: 表名不能为空');
      return '';
    }

    try {
      // 日志行数据，帮助调试
      console.log('生成UPDATE SQL，行数据:', rowData);
      console.log('设置的值:', setValues);
      
      // 立即创建一个排除字段集合，包含所有不应出现在WHERE条件中的字段
      const excludeFromWhere = new Set();
      
      // 将所有SET子句的字段添加到排除集合
      if (setValues && typeof setValues === 'object') {
        Object.keys(setValues).forEach(key => {
          excludeFromWhere.add(key);
          console.log(`将SET字段 ${key} 添加到排除集合中`);
        });
      }
      
      // 主键检测逻辑保留，但不再仅使用主键作为条件
      let primaryKeyFields = [];
      if (rowData) {
        primaryKeyFields = Object.keys(rowData).filter(key => 
          /^(id|.*_id|primary|key)$/i.test(key) && 
          rowData[key] !== undefined && rowData[key] !== null
        );
        console.log('检测到可能的主键字段:', primaryKeyFields);
      }
      
      let whereClause = '';
      
      // 使用Set来跟踪已添加到WHERE条件的字段，避免重复
      const addedWhereFields = new Set();
      
      // 优先使用ID字段作为唯一条件，与后端逻辑保持一致
      if (whereConditions && whereConditions.id) {
        // 只使用ID作为条件
        whereClause = `\`id\` = ${formatSqlValue(whereConditions.id, 'id')}`;
        console.log(`只使用ID作为唯一条件: ${whereConditions.id}`);
        addedWhereFields.add('id');
      }
      // 如果rowData中有id字段且未在whereConditions中指定
      else if (rowData && rowData.id && !addedWhereFields.has('id')) {
        whereClause = `\`id\` = ${formatSqlValue(rowData.id, 'id')}`;
        console.log(`从rowData中获取ID作为唯一条件: ${rowData.id}`);
        addedWhereFields.add('id');
      }
      // 如果没有ID字段，回退到使用所有条件
      else if (rowData && typeof rowData === 'object') {
        // 记录要过滤的SET字段
        console.log('SET要更新的字段(排除集合):', Array.from(excludeFromWhere));
        
        // 使用所有非null、非undefined字段，排除将要更新的字段
        let whereEntries = Object.entries(rowData).filter(([key, value]) => {
          // 基本过滤条件
          const isValidField = !key.startsWith('__v') && !key.startsWith('_') && 
                              value !== undefined && value !== null;
          
          // 强制检查是否在排除集合中
          const isExcluded = excludeFromWhere.has(key);
          
          // 检查是否已添加到WHERE条件
          const isAlreadyAdded = addedWhereFields.has(key);
          
          // 记录为什么排除某些字段
          if (!isValidField) {
            console.log(`排除无效字段 ${key}:`, value);
          } else if (isExcluded) {
            console.log(`强制排除SET字段 ${key}，避免在WHERE中使用`);
          } else if (isAlreadyAdded) {
            console.log(`排除已添加到WHERE的字段 ${key}`);
          }
          
          return isValidField && !isExcluded && !isAlreadyAdded;
        }).map(([key, value]) => {
          // 标记该字段已添加到WHERE条件
          addedWhereFields.add(key);
          return [key, value];
        });
        
        console.log('使用所有非空字段作为WHERE条件:', whereEntries);
        
        // 构建WHERE子句
        whereClause = whereEntries
          .map(([column, value]) => {
            // 检查这个字段是否可能是大整数
            if (column.toLowerCase().includes('id') && 
                ((typeof value === 'string' && /^\d{15,}$/.test(value)) ||
                (typeof value === 'number' && Math.abs(value) > Number.MAX_SAFE_INTEGER / 10))) {
              console.log(`检测到可能的ID字段 ${column} 包含大整数: ${value}，特殊处理`);
              return `\`${column}\` = ${formatSqlValue(value, column)}`;
            }
            
            // 对NULL值特殊处理
            if (value === null) {
              return `\`${column}\` IS NULL`;
            }
            
            return `\`${column}\` = ${formatSqlValue(value, column)}`;
          })
          .join(' AND ');
      } else {
        // 使用传入的whereConditions
        // 去重复字段，确保每个字段只使用一次
        const uniqueConditions = new Map();
        
        Object.entries(whereConditions || {}).forEach(([column, value]) => {
          // 检查该字段是否在排除集合中
          if (excludeFromWhere.has(column)) {
            console.log(`强制排除SET字段 ${column} 不在WHERE条件中使用`);
            return;
          }
          
          // 如果当前字段尚未添加，且不在setValues中，则添加到唯一条件Map
          if (!uniqueConditions.has(column)) {
            uniqueConditions.set(column, value);
          } else {
            console.log(`跳过WHERE条件中的字段(防止重复): ${column}`);
          }
        });
        
        console.log('过滤后的WHERE条件字段:', Array.from(uniqueConditions.keys()));
        console.log('SET子句字段(已排除):', Array.from(excludeFromWhere));
        
        whereClause = Array.from(uniqueConditions.entries())
          .map(([column, value]) => {
            // 对NULL值特殊处理
            if (value === null) {
              return `\`${column}\` IS NULL`;
            }
            
            // 检查条件字段是否可能是大整数
            const isLargeIntField = 
              (typeof value === 'string' && /^\d{15,}$/.test(value)) ||
              (typeof value === 'number' && Math.abs(value) > Number.MAX_SAFE_INTEGER / 10);
            
            if (isLargeIntField) {
              console.log(`检测到条件字段 ${column} 可能是大整数: ${value}，特殊处理`);
              // 确保大整数在WHERE条件中使用相同的精确字符串值
              return `\`${column}\` = ${formatSqlValue(value, column)}`;
            }
            
            return `\`${column}\` = ${formatSqlValue(value, column)}`;
          })
          .join(' AND ');
      }

      // 构建SET子句
      const setClause = Object.entries(setValues || {})
        .map(([column, value]) => {
          // 对NULL值特殊处理
          if (value === null) {
            return `\`${column}\` = NULL`;
          }
          return `\`${column}\` = ${formatSqlValue(value, column)}`;
        })
        .join(', ');

      if (!setClause) {
        console.error('generateUpdateSql: 没有设置值');
        return '';
      }

      // 记录SET子句，帮助调试
      console.log('最终SET子句:', setClause);
      console.log('最终WHERE子句:', whereClause);

      // 安全检查：强制从WHERE条件中删除所有SET子句的字段
      Array.from(excludeFromWhere).forEach(key => {
        // 检查WHERE条件中是否包含了SET字段，使用严格的正则匹配
        const keyPattern = new RegExp(`\`${key}\`\\s*=`, 'i');
        if (keyPattern.test(whereClause)) {
          console.warn(`强制清除：WHERE条件中仍包含SET字段 ${key}，将其移除`);
          
          // 使用三种不同的模式替换，确保涵盖各种情况
          // 1. 处理 AND `key` = value
          whereClause = whereClause.replace(
            new RegExp(`\\s*AND\\s+\`${key}\`\\s*=\\s*[^\\s]+`, 'gi'), 
            ''
          );
          
          // 2. 处理 `key` = value AND 
          whereClause = whereClause.replace(
            new RegExp(`\`${key}\`\\s*=\\s*[^\\s]+\\s*AND\\s*`, 'gi'), 
            ''
          );
          
          // 3. 处理没有AND的单一条件 `key` = value
          if (whereClause.trim().match(new RegExp(`^\`${key}\`\\s*=\\s*[^\\s]+$`, 'i'))) {
            whereClause = '';
          }
          
          // 4. 清理开头或结尾的AND
          whereClause = whereClause.replace(/^\s*AND\s+|\s+AND\s*$/gi, '');
        }
      });
      
      // 再做一次简单的清理，防止有连续的AND
      whereClause = whereClause.replace(/\s+AND\s+AND\s+/gi, ' AND ');
      
      console.log('最终清理后的WHERE条件:', whereClause);
      
      // 最终确认：确保SET字段不在WHERE中
      const setFields = Object.keys(setValues || {});
      let hasProblem = false;
      
      setFields.forEach(field => {
        const fieldPattern = new RegExp(`\`${field}\`\\s*=`, 'i');
        if (whereClause && fieldPattern.test(whereClause)) {
          console.error(`严重问题: ${field} 字段仍然出现在WHERE条件中!`);
          hasProblem = true;
          // 紧急处理：完全移除这个字段的WHERE条件
          whereClause = whereClause.replace(fieldPattern, '`REMOVED_' + field + '`=');
        }
      });
      
      if (hasProblem) {
        console.warn('检测到问题，尝试紧急修复WHERE条件:', whereClause);
        // 清理混乱的条件
        whereClause = whereClause.replace(/\s+AND\s+AND\s+/g, ' AND ')
                               .replace(/^\s*AND\s+|\s+AND\s*$/g, '');
      }
      
      const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
      let sql = `-- Backup UPDATE generated on ${timestamp}\n`;
      // 修复：表名不加反引号，库名.表名也不加反引号
      let cleanTableName = tableName.replace(/`/g, '');
      sql += `-- For table ${cleanTableName}\n\n`;
      sql += `UPDATE ${cleanTableName} SET ${setClause}`;
      
      if (whereClause && whereClause.trim()) {
        sql += ` WHERE ${whereClause}`;
      } else {
        sql += ` -- WARNING: No WHERE clause specified!`;
      }
      
      // 记录最终生成的SQL
      console.log('生成的UPDATE SQL:', sql);

      return sql;
    } catch (error) {
      console.error('生成UPDATE SQL时出错:', error);
      return '';
    }
  };

  // 添加生成DELETE语句的函数
  const generateDeleteSql = (tableName, whereConditions) => {
    if (!tableName) {
      console.error('generateDeleteSql: 表名不能为空');
      return '';
    }

    try {
      let whereClause = '';
      
      // 优先使用ID字段作为唯一条件，与后端逻辑保持一致
      if (whereConditions && whereConditions.id) {
        // 只使用ID作为条件
        whereClause = `\`id\` = ${formatSqlValue(whereConditions.id, 'id')}`;
        console.log(`DELETE操作只使用ID作为唯一条件: ${whereConditions.id}`);
      } else {
        // 去重复字段，确保每个字段只使用一次
        const uniqueConditions = new Map();
        
        Object.entries(whereConditions || {}).forEach(([column, value]) => {
          // 如果当前字段尚未添加，则添加到唯一条件Map
          if (!uniqueConditions.has(column)) {
            uniqueConditions.set(column, value);
          }
        });
        
        whereClause = Array.from(uniqueConditions.entries())
          .map(([column, value]) => {
            // 对NULL值特殊处理
            if (value === null) {
              return `\`${column}\` IS NULL`;
            }
            
            // 检查条件字段是否可能是大整数
            const isLargeIntField = 
              (typeof value === 'string' && /^\d{15,}$/.test(value)) ||
              (typeof value === 'number' && Math.abs(value) > Number.MAX_SAFE_INTEGER / 10);
            
            if (isLargeIntField) {
              console.log(`DELETE语句中检测到条件字段 ${column} 可能是大整数: ${value}，特殊处理`);
              // 确保大整数在WHERE条件中使用相同的精确字符串值
              return `\`${column}\` = ${formatSqlValue(value, column)}`;
            }
            
            return `\`${column}\` = ${formatSqlValue(value, column)}`;
          })
          .join(' AND ');
      }

      const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
      let sql = `-- Backup DELETE generated on ${timestamp}\n`;
      // 修复：表名不加反引号，库名.表名也不加反引号
      let cleanTableName = tableName.replace(/`/g, '');
      sql += `-- For table ${cleanTableName}\n\n`;
      sql += `DELETE FROM ${cleanTableName}`;
      
      if (whereClause) {
        sql += ` WHERE ${whereClause}`;
      } else {
        sql += ` -- WARNING: No WHERE clause specified!`;
      }

      return sql;
    } catch (error) {
      console.error('生成DELETE SQL时出错:', error);
      return '';
    }
  };

  return {
    // generateBackupSelectSql,
    generateInsertSql,
    generateUpdateSql,
    generateDeleteSql,
    // downloadSqlFile,
  };
}; 