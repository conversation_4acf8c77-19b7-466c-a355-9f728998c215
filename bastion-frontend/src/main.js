import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import pinia from './store'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './assets/css/main.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

// 全局抑制 ResizeObserver 错误 - 这是浏览器的已知问题，不影响功能
const originalResizeObserver = window.ResizeObserver;
window.ResizeObserver = class extends originalResizeObserver {
  constructor(callback) {
    super((entries, observer) => {
      try {
        callback(entries, observer);
      } catch (error) {
        // 忽略 ResizeObserver 循环错误
        if (error.message && error.message.includes('ResizeObserver loop')) {
          // 完全静默处理，不输出任何日志
          return;
        }
        // 重新抛出其他错误
        throw error;
      }
    });
  }
};

// 拦截控制台错误输出，过滤常见的开发环境错误
const originalConsoleError = console.error;
console.error = function(...args) {
  // 检查是否是需要过滤的错误
  const message = args.join(' ');
  if (message.includes('ResizeObserver loop') ||
      message.includes('ResizeObserver loop completed with undelivered notifications') ||
      message.includes('SES_UNCAUGHT_EXCEPTION') ||
      message.includes('lockdown-install.js')) {
    return; // 静默忽略这些开发环境的已知问题
  }
  // 调用原始的 console.error
  originalConsoleError.apply(console, args);
};

// 添加全局错误处理
window.addEventListener('error', (event) => {
  // 过滤掉开发环境的已知问题，这些不影响应用功能
  if (event.message && (
    event.message.includes('ResizeObserver loop') ||
    event.message.includes('ResizeObserver loop completed with undelivered notifications') ||
    event.message.includes('SES_UNCAUGHT_EXCEPTION') ||
    event.filename && event.filename.includes('lockdown-install.js')
  )) {
    return; // 忽略这些开发环境的已知问题
  }

  console.error('全局错误:', event.error);
  console.error('错误信息:', event.message);
  console.error('错误位置:', event.filename, 'Line:', event.lineno, 'Column:', event.colno);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise拒绝:', event.reason);
});

// 创建Vue应用实例
const app = createApp(App)

// 添加全局错误处理
app.config.errorHandler = (err, vm, info) => {
  // 过滤掉开发环境的已知问题
  if (err && err.message && (
    err.message.includes('ResizeObserver loop') ||
    err.message.includes('ResizeObserver loop completed with undelivered notifications') ||
    err.message.includes('SES_UNCAUGHT_EXCEPTION')
  )) {
    return; // 忽略这些开发环境的已知问题
  }

  console.error('Vue全局错误:', err);
  console.error('错误组件:', vm);
  console.error('错误信息:', info);
};

// 注册插件
app.use(router)
app.use(pinia)
app.use(ElementPlus, {
  locale: zhCn, // 设置中文语言
  size: 'default',
})

// 挂载应用
app.mount('#app')
