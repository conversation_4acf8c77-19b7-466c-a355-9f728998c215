import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const service = axios.create({
  baseURL: '/api', // API基础URL
  timeout: 60000, // 增加默认超时时间到60秒
  headers: {
    'Content-Type': 'application/json'
  },
  withCredentials: true // 允许跨域请求携带凭据（Cookie）
})

// 获取CSRF令牌
function getCsrfToken() {
  // 从cookie中获取CSRF令牌
  const name = 'csrftoken='
  const decodedCookie = decodeURIComponent(document.cookie)
  const cookies = decodedCookie.split(';')
  
  for (let i = 0; i < cookies.length; i++) {
    let cookie = cookies[i].trim()
    if (cookie.indexOf(name) === 0) {
      return cookie.substring(name.length, cookie.length)
    }
  }
  return null
}

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 记录请求开始时间，用于重试逻辑
    config.metadata = { startTime: new Date() }
    
    // 从本地存储中获取token
    const token = localStorage.getItem('token')
    
    // 如果有token，添加到请求头（添加Bearer前缀）
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
      console.log('添加认证头: Bearer Token')
    } else {
      console.log('未找到token，请求将使用会话认证')
    }
    
    // 对非GET请求添加CSRF令牌
    if (config.method !== 'get') {
      const csrfToken = getCsrfToken()
      if (csrfToken) {
        config.headers['X-CSRFToken'] = csrfToken
        console.log('添加CSRF令牌:', csrfToken)
      } else {
        console.log('未找到CSRF令牌，这可能导致POST/PUT/DELETE请求被拒绝')
      }
    }
    
    // 增强日志记录，特别是表格编辑相关请求
    const isTableEditRequest = config.url && config.url.includes('/table/edit/');
    if (isTableEditRequest) {
      console.log(`[表格编辑] 发送${config.method}请求到: ${config.url}`, {
        url: config.url,
        method: config.method,
        headers: config.headers,
        data: JSON.stringify(config.data),
        params: config.params
      });
    } else {
      console.log(`发送${config.method}请求到: ${config.url}`, {
        url: config.url,
        method: config.method,
        headers: config.headers,
        data: config.data,
        params: config.params
      });
    }
    
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    
    // 增强日志记录，特别是表格编辑相关请求
    const isTableEditRequest = response.config.url && response.config.url.includes('/table/edit/');
    if (isTableEditRequest) {
      console.log(`[表格编辑] 接收到响应: ${response.config.url}`, {
        status: response.status,
        data: res,
        headers: response.headers
      });
    } else {
      console.log(`接收到响应: ${response.config.url}`, {
        status: response.status,
        data: res,
        headers: response.headers
      });
    }
    
    // 如果请求成功，直接返回数据（处理200、201和204状态码）
    if (response.status === 200 || response.status === 201 || response.status === 204) {
      // 对于204 No Content状态码，可以返回一个空对象
      if (response.status === 204) {
        console.log('成功处理204 No Content响应')
        return { success: true, status: 204 }
      }

      // 确保返回的是有效数据
      if (res === null || res === undefined) {
        console.warn('API返回了空数据');
        return [];
      }

      // 对于查询执行API，即使success为false也正常返回，让业务代码处理
      if (response.config.url && response.config.url.includes('/query/execute/')) {
        console.log('查询执行API响应，直接返回数据让业务代码处理');
        return res;
      }

      return res
    }
    
    // 处理请求错误
    ElMessage({
      message: res.message || '请求错误',
      type: 'error',
      duration: 3000
    })
    
    return Promise.reject(new Error(res.message || '请求错误'))
  },
  error => {
    // 重试逻辑
    const config = error.config || {}
    
    // 检查是否有备选URL可用
    if (config.fallbackUrls && config.fallbackUrls.length > 0) {
      // 如果没有使用过fallbackUrlIndex属性，初始化为0
      if (config.fallbackUrlIndex === undefined) {
        config.fallbackUrlIndex = 0;
      } else {
        // 如果已经使用过了，增加索引
        config.fallbackUrlIndex++;
      }
      
      // 如果还有可用的备选URL
      if (config.fallbackUrlIndex < config.fallbackUrls.length) {
        const nextUrl = config.fallbackUrls[config.fallbackUrlIndex];
        console.log(`URL ${config.url} 失败，尝试备选URL: ${nextUrl}`);
        
        // 创建新的配置对象，更新URL
        const newConfig = { ...config };
        newConfig.url = nextUrl;
        
        // 返回使用新URL的请求
        return service(newConfig);
      }
    }
    
    // 对于超时错误尝试重试
    if (error.code === 'ECONNABORTED' && error.message && error.message.includes('timeout') && 
        (!config.retryCount || config.retryCount < 2)) {
      
      config.retryCount = (config.retryCount || 0) + 1
      console.log(`请求超时，正在进行第 ${config.retryCount} 次重试...`)
      
      // 添加递增的重试延迟
      const delay = config.retryCount * 1000
      
      // 返回一个延迟执行的Promise
      return new Promise(resolve => {
        setTimeout(() => {
          console.log(`重试请求: ${config.url}`)
          ElMessage({
            message: `请求超时，正在重试(${config.retryCount}/2)...`,
            type: 'warning',
            duration: 2000
          })
          resolve(service(config))
        }, delay)
      })
    }
    
    // 获取错误信息
    let errorMessage = ''
    
    // 处理后端返回的标准错误格式
    if (error.response?.data) {
      const responseData = error.response.data
      
      // 提取格式化错误信息
      if (responseData.error_message) {
        errorMessage = responseData.error_message
      } else if (responseData.message) {
        errorMessage = responseData.message
      } else if (responseData.msg) {
        errorMessage = responseData.msg
      } else if (responseData.error) {
        errorMessage = responseData.error
      } else if (typeof responseData === 'string') {
        errorMessage = responseData
      }
      
      // 如果是数据库连接错误，添加更友好的说明
      if (responseData.error_code === 2006 || responseData.error_code === 2013) {
        errorMessage = `数据库连接已断开(错误码: ${responseData.error_code})，请重试或检查服务器状态`;
        
        ElMessage({
          message: errorMessage,
          type: 'error',
          duration: 5000
        })
        
        return Promise.reject(new Error(errorMessage))
      }
    }
    
    if (!errorMessage) {
      errorMessage = error.response?.data?.message || error.message || '服务器错误'
    }
    
    // 处理特定错误码
    if (error.response?.status === 401) {
      // 未授权，清除用户状态并重定向到登录页
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      
      // 清除其他可能的状态
      localStorage.removeItem('lastActiveTime')
      sessionStorage.clear()
      
      // 如果使用了 Pinia，重置 store
      try {
        const { useUserStore } = require('@/store/user')
        const userStore = useUserStore()
        if (userStore) {
          userStore.resetState()
        }
      } catch (e) {
        console.warn('重置用户 store 失败:', e)
      }
      
      ElMessage({
        message: '登录已过期，请重新登录',
        type: 'warning',
        duration: 3000,
        showClose: true
      })
      
      // 立即重定向到登录页，并记录当前页面
      const currentPath = window.location.pathname + window.location.search
      const loginPath = `/login?redirect=${encodeURIComponent(currentPath)}`
      window.location.href = loginPath
    } else if (error.response?.status === 403) {
      // 禁止访问
      ElMessage({
        message: '很抱歉，您没有权限进行此操作。如需帮助，请联系系统管理员',
        type: 'warning',
        duration: 4000
      })
    } else if (error.response?.status === 504) {
      // 网关超时
      ElMessage({
        message: '请求超时，服务器处理时间过长。请稍后再试或联系管理员',
        type: 'error',
        duration: 4000
      })
    } else if (!error.response && error.message && error.message.includes('Network Error')) {
      // 网络错误，可能是后端服务不可用
      ElMessage({
        message: '网络连接错误，请检查您的网络连接或服务器是否可用',
        type: 'error',
        duration: 4000
      })
    } else if (!error.response && error.message && error.message.includes('timeout')) {
      // 超时错误
      ElMessage({
        message: '请求超时，服务器响应时间过长，请稍后再试',
        type: 'error',
        duration: 4000
      })
    } else {
      // 其他错误
      ElMessage({
        message: errorMessage,
        type: 'error',
        duration: 3000
      })
    }
    
    return Promise.reject(error)
  }
)

// 增强版请求函数，支持备选URL
const enhancedRequest = (config) => {
  // 如果提供了fallbackUrls，确保它被包含在配置中
  if (config.fallbackUrls) {
    const { fallbackUrls, ...restConfig } = config;
    return service({ ...restConfig, fallbackUrls });
  }
  return service(config);
};

export default enhancedRequest; 