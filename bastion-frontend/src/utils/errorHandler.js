import { ElMessage, ElMessageBox } from 'element-plus';

/**
 * 处理API错误并显示友好的错误消息
 * @param {Error} error - 错误对象
 * @param {Object} options - 配置选项
 * @param {string} options.title - 错误标题
 * @param {Function} options.onRetry - 重试回调函数
 * @param {boolean} options.showDetail - 是否显示详细信息按钮
 * @returns {void}
 */
export function handleApiError(error, options = {}) {
  const title = options.title || '操作失败';
  const showDetail = options.showDetail !== false;
  
  let errorMessage = '发生未知错误';
  let detailMessage = '';
  
  // 提取错误信息
  if (error.response) {
    // 处理HTTP错误
    const status = error.response.status;
    const responseData = error.response.data;
    
    detailMessage = formatErrorDetail(error);
    
    if (status === 500) {
      // 处理500内部服务器错误
      if (responseData && responseData.message) {
        errorMessage = responseData.message;
      } else if (typeof responseData === 'string' && responseData.includes('FieldError')) {
        errorMessage = '数据库服务器错误: 字段错误。这可能是由数据库模型字段定义导致的，请联系系统管理员。';
      } else if (typeof responseData === 'string' && responseData.includes('Cannot resolve keyword')) {
        errorMessage = '数据库模型定义错误，请联系系统管理员修复。';
      } else {
        errorMessage = '服务器内部错误，请联系系统管理员';
      }
    } else if (status === 403) {
      errorMessage = '您暂时没有权限执行此操作，如需帮助请联系系统管理员';
    } else if (status === 404) {
      errorMessage = '请求的资源不存在';
    } else if (status === 401) {
      errorMessage = '您的登录已过期或未授权，请重新登录';
    } else if (status === 400) {
      // 处理400验证错误
      if (responseData && typeof responseData === 'object') {
        const errors = [];
        for (const field in responseData) {
          if (Array.isArray(responseData[field])) {
            errors.push(`${field}: ${responseData[field].join(', ')}`);
          } else if (typeof responseData[field] === 'string') {
            errors.push(`${field}: ${responseData[field]}`);
          }
        }
        if (errors.length > 0) {
          errorMessage = `验证错误: ${errors.join('; ')}`;
        } else {
          errorMessage = '请求参数有误';
        }
      } else if (typeof responseData === 'string') {
        errorMessage = responseData;
      } else {
        errorMessage = '请求参数有误';
      }
    } else {
      errorMessage = `请求失败 (${status})`;
    }
  } else if (error.message) {
    // 处理网络或其他非HTTP错误
    errorMessage = error.message;
    
    if (error.message.includes('Network Error')) {
      errorMessage = '网络连接错误，请检查您的网络连接或服务器是否可用';
    } else if (error.message.includes('timeout')) {
      errorMessage = '请求超时，服务器响应时间过长';
    }
    
    detailMessage = formatErrorDetail(error);
  }
  
  // 显示错误消息
  if (options.showDialog !== false) {
    const buttons = ['确定'];
    if (options.onRetry) {
      buttons.push('重试');
    }
    if (showDetail) {
      buttons.push('查看详情');
    }
    
    ElMessageBox.alert(errorMessage, title, {
      confirmButtonText: '确定',
      type: 'warning',
      showCancelButton: options.onRetry ? true : false,
      cancelButtonText: options.onRetry ? '重试' : '',
      showClose: true,
      callback: (action) => {
        if (action === 'cancel' && options.onRetry) {
          options.onRetry();
        } else if (action === 'detail' && showDetail) {
          showErrorDetail(title, detailMessage);
        }
      },
      dangerouslyUseHTMLString: false,
    }).catch(() => {
      // 忽略取消的Promise错误
    });
  } else {
    // 只显示简单的消息提示
    ElMessage({
      message: errorMessage,
      type: 'error',
      duration: 5000
    });
  }
  
  // 记录到控制台以便调试
  console.error('API错误:', error);
  if (detailMessage) {
    console.error('详细错误信息:', detailMessage);
  }
  
  return {
    message: errorMessage,
    detail: detailMessage
  };
}

/**
 * 显示错误详情对话框
 * @param {string} title - 标题
 * @param {string} detail - 详细信息
 */
function showErrorDetail(title, detail) {
  ElMessageBox.alert(detail, `${title} - 详细信息`, {
    confirmButtonText: '关闭',
    customClass: 'error-detail-dialog',
    dangerouslyUseHTMLString: false,
  });
}

/**
 * 格式化错误详细信息
 * @param {Error} error - 错误对象
 * @returns {string} 格式化后的详细信息
 */
function formatErrorDetail(error) {
  if (!error) return '未知错误';
  
  let details = '';
  
  if (error.message) {
    details += `错误消息: ${error.message}\n`;
  }
  
  if (error.name) {
    details += `错误类型: ${error.name}\n`;
  }
  
  if (error.code) {
    details += `错误代码: ${error.code}\n`;
  }
  
  if (error.response) {
    details += `状态码: ${error.response.status}\n`;
    
    if (error.response.data) {
      details += `响应数据: `;
      if (typeof error.response.data === 'string') {
        details += error.response.data;
      } else {
        try {
          details += JSON.stringify(error.response.data, null, 2);
        } catch (e) {
          details += '[无法序列化的响应数据]';
        }
      }
      details += '\n';
    }
  }
  
  if (error.config) {
    details += `请求URL: ${error.config.url}\n`;
    details += `请求方法: ${error.config.method}\n`;
    
    if (error.config.params) {
      details += `查询参数: ${JSON.stringify(error.config.params, null, 2)}\n`;
    }
    
    if (error.config.data) {
      details += `请求数据: `;
      try {
        details += JSON.stringify(JSON.parse(error.config.data), null, 2);
      } catch (e) {
        details += error.config.data;
      }
      details += '\n';
    }
  }
  
  if (error.stack) {
    details += `\n堆栈跟踪:\n${error.stack}`;
  }
  
  return details;
}

/**
 * 处理后端模型字段错误 - 特别针对"Cannot resolve keyword"错误
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否是已知的模型字段错误
 */
export function isModelFieldError(error) {
  if (error.response && error.response.status === 500) {
    const responseData = error.response.data;
    if (typeof responseData === 'string' && 
        (responseData.includes('FieldError') || 
         responseData.includes('Cannot resolve keyword'))) {
      return true;
    }
  }
  return false;
}

/**
 * 显示提示信息，提醒用户联系管理员修复后端问题
 */
export function showServerErrorHelp() {
  ElMessageBox.confirm(
    '系统检测到后端数据库模型存在问题，可能是由于代码更新导致的不兼容性。这需要系统管理员解决。',
    '系统错误',
    {
      confirmButtonText: '我已了解',
      cancelButtonText: '联系管理员',
      type: 'warning',
    }
  ).then(() => {
    ElMessage({
      type: 'info',
      message: '请避免执行导致此错误的操作，直到问题修复',
    })
  }).catch(() => {
    // 提供联系管理员的方式
    ElMessageBox.alert(
      '请将以下错误信息发送给系统管理员：<br><br>' +
      'FieldError: Cannot resolve keyword \'group\' into field.<br>' +
      '这是数据库模型定义问题，需要更新后端代码。',
      '联系管理员',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '复制信息',
        callback: (action) => {
          if (action === 'confirm') {
            navigator.clipboard.writeText(
              'FieldError: Cannot resolve keyword \'group\' into field. ' +
              '这是数据库模型定义问题，需要更新后端代码。'
            ).then(() => {
              ElMessage.success('错误信息已复制到剪贴板');
            }).catch(() => {
              ElMessage.error('复制失败，请手动复制错误信息');
            });
          }
        }
      }
    );
  });
}

export default {
  handleApiError,
  isModelFieldError,
  showServerErrorHelp
};