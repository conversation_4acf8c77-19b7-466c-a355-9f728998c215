import { getCurrentUser } from '@/api/user'
import { getUserTables } from '@/api/permission'

// 权限缓存
let userTablesCache = null
let lastFetchTime = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存
const MAX_RETRIES = 3 // 最大重试次数

/**
 * 检查用户是否有表的操作权限
 * @param {String} datasourceId - 数据源ID
 * @param {String} schema - Schema名称
 * @param {String} tableName - 表名
 * @param {String} operation - 操作类型 (read/write)
 * @returns {Promise<Boolean>} - 返回是否有权限
 */
export async function hasTablePermission(datasourceId, schema, tableName, operation = 'read') {
  try {
    if (!tableName) {
      console.warn('表名为空，无法检查权限')
      return false
    }
    
    console.log(`检查权限: 数据源=${datasourceId}, schema=${schema}, 表=${tableName}, 操作=${operation}`)
    
    // 获取当前用户
    const userResponse = await getCurrentUser()
    const user = userResponse.data || userResponse
    
    // 如果是超级管理员，直接返回 true
    if (user.is_superuser || user.is_staff) {
      console.log('用户是超级管理员，自动有权限')
      return true
    }
    
    // 获取用户表权限
    await refreshTablePermissions(datasourceId)
    
    if (!userTablesCache || !userTablesCache.length) {
      console.warn('用户表权限缓存为空，没有权限数据')
      return false
    }
    
    console.log(`检查表 ${tableName} 的权限，当前缓存中有 ${userTablesCache.length} 个权限记录`)
    
    // 表名转小写进行比较
    const lowerTableName = tableName.toLowerCase()
    
    // 查找匹配的表权限（不区分schema）
    const foundPermission = userTablesCache.find(p => {
      return p.table_name.toLowerCase() === lowerTableName
    })
    
    if (!foundPermission) {
      console.warn(`未找到表 ${tableName} 的权限记录`)
      return false
    }
    
    console.log(`找到表 ${tableName} 的权限: ${foundPermission.permission_type}`)
    
    // 检查操作类型
    if (operation === 'read') {
      // 读操作: 只读/读写权限都可以
      return ['read', 'write'].includes(foundPermission.permission_type)
    } else if (operation === 'write') {
      // 写操作: 只有读写权限可以
      return foundPermission.permission_type === 'write'
    }
    
    return false
  } catch (error) {
    console.error('检查表权限出错:', error)
    // 出错时默认返回false，确保安全
    return false
  }
}

/**
 * 清除权限缓存
 */
export function clearPermissionCache() {
  console.log('手动清除表权限缓存')
  userTablesCache = null
  lastFetchTime = 0
}

/**
 * 刷新表权限缓存
 * @param {String} datasourceId - 可选，数据源ID
 * @param {Number} retryCount - 当前重试次数
 * @param {Boolean} forceRefresh - 是否强制从服务器刷新数据
 * @returns {Promise<Array>} - 返回用户表权限列表
 */
export async function refreshTablePermissions(datasourceId, retryCount = 0, forceRefresh = false) {
  const now = Date.now()
  
  // 如果缓存有效且不需要强制刷新，直接返回缓存
  if (userTablesCache && lastFetchTime > 0 && (now - lastFetchTime) < CACHE_DURATION && !forceRefresh) {
    console.log('使用内存中的表权限缓存数据')
    return userTablesCache
  }
  
  // 尝试从本地存储获取权限数据
  if (!forceRefresh && datasourceId) {
    try {
      const storageKey = `table_permissions_${datasourceId}`
      const storedData = localStorage.getItem(storageKey)
      
      if (storedData) {
        const parsedData = JSON.parse(storedData)
        const storedTime = parsedData.timestamp
        
        // 检查本地存储的数据是否仍然有效（不超过缓存时间）
        if (now - storedTime < CACHE_DURATION) {
          console.log('使用本地存储的表权限缓存数据')
          userTablesCache = parsedData.permissions
          lastFetchTime = storedTime
          return userTablesCache
        } else {
          console.log('本地存储的表权限数据已过期，需要刷新')
        }
      }
    } catch (error) {
      console.error('读取本地存储的表权限数据失败:', error)
    }
  }
  
  // 从服务器获取最新数据
  try {
    console.log('从服务器刷新表权限缓存，数据源ID:', datasourceId, '重试次数:', retryCount)
    const response = await getUserTables(datasourceId)
    
    // 确保权限数据有效
    if (response && (response.results || response.data || Array.isArray(response))) {
      userTablesCache = response.results || response.data || response
      console.log('获取到表权限数据:', userTablesCache)
      lastFetchTime = now
      
      // 保存到本地存储
      if (datasourceId && userTablesCache.length > 0) {
        try {
          const storageKey = `table_permissions_${datasourceId}`
          localStorage.setItem(storageKey, JSON.stringify({
            timestamp: now,
            permissions: userTablesCache
          }))
          console.log('表权限数据已保存到本地存储')
        } catch (error) {
          console.error('保存表权限数据到本地存储失败:', error)
        }
      }
    } else {
      console.warn('获取到的表权限数据无效:', response)
      // 如果数据无效且未超过最大重试次数，进行重试
      if (retryCount < MAX_RETRIES) {
        console.log(`表权限数据无效，进行第${retryCount + 1}次重试`)
        // 等待短暂时间后重试
        await new Promise(resolve => setTimeout(resolve, 500))
        return refreshTablePermissions(datasourceId, retryCount + 1, forceRefresh)
      } else {
        console.error('获取表权限数据失败，已达到最大重试次数')
        userTablesCache = []
      }
    }
  } catch (error) {
    console.error('获取用户表权限失败:', error)
    // 如果请求失败且未超过最大重试次数，进行重试
    if (retryCount < MAX_RETRIES) {
      console.log(`表权限请求失败，进行第${retryCount + 1}次重试`)
      // 等待短暂时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000))
      return refreshTablePermissions(datasourceId, retryCount + 1, forceRefresh)
    } else {
      console.error('获取表权限失败，已达到最大重试次数')
      userTablesCache = []
    }
  }
  return userTablesCache
}

/**
 * 获取用户的所有有权限表列表
 * @param {String} datasourceId - 可选，数据源ID
 * @param {String} operation - 可选，操作类型 (read/write)
 * @returns {Promise<Array>} - 返回有权限的表列表
 */
export async function getUserPermittedTables(datasourceId, operation = 'read') {
  await refreshTablePermissions(datasourceId)
  
  if (!userTablesCache || !userTablesCache.length) {
    return []
  }
  
  // 根据操作类型过滤表
  if (operation === 'read') {
    return userTablesCache.filter(p => ['read', 'write'].includes(p.permission_type))
  } else if (operation === 'write') {
    return userTablesCache.filter(p => p.permission_type === 'write')
  }
  
  return userTablesCache
} 