/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background-color: #f5f7fa;
}

#app {
  height: 100%;
  width: 100%;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 布局样式 */
.page-container {
  padding: 20px;
  height: 100%;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 20px;
  font-weight: bold;
}

.page-content {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

/* 表单样式 */
.form-container {
  max-width: 800px;
  margin: 0 auto;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
}

/* 查询编辑器样式 */
.query-editor {
  height: 300px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 15px;
}

.result-table {
  margin-top: 20px;
}

/* 工具栏样式 */
.toolbar {
  padding: 8px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
    margin-bottom: 10px;
  }
}

/* 表格相关样式优化 */
.el-table {
  margin-bottom: 0;
}

/* 减少表格上下空白区域 */
.results-table-wrapper {
  padding: 0 !important;
  margin: 0 !important;
}

/* 优化表格头部和内容区域的间距 */
.editable-result-table-container {
  margin-top: 0;
  border-top: 1px solid #ebeef5;
}

/* 减少表格操作区域的高度 */
.table-actions {
  padding: 4px 8px !important;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

/* 优化表格内容区域 */
.table-wrapper {
  border: none;
  margin: 0;
}

/* 优化工具栏样式 */
.toolbar {
  padding: 8px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 优化编辑按钮样式 */
.edit-mode-button {
  margin-right: 10px;
}

/* 优化数据库选择器样式 */
.database-selector-right {
  display: flex;
  gap: 10px;
}

/* 优化表格头部样式 */
.el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  padding: 8px 0;
}

/* 优化表格行高 */
.el-table__row td {
  padding: 6px 0;
}

/* 优化表格外边框 */
.el-table--border {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

/* 优化表格分页器样式 */
.pagination-container {
  padding: 8px;
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
}

/* 优化空白区域 */
.empty-result-content {
  padding: 20px;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin: 10px 0;
}

/* 优化查询结果容器 */
.query-results-container {
  border-top: 1px solid #e4e7ed;
  background-color: #fff;
} 