<template>
  <div class="sidebar">
    <div class="logo-container">
      <router-link to="/">
        <img src="../../assets/logo.png" alt="Bastion Logo" class="logo" />
        <span class="title">Bastion</span>
      </router-link>
    </div>
    
    <el-menu
      :default-active="activeMenu"
      :collapse="isCollapse"
      :unique-opened="true"
      background-color="#304156"
      text-color="#bfcbd9"
      active-text-color="#409EFF"
      router
    >
      <el-menu-item index="/dashboard">
        <el-icon><Monitor /></el-icon>
        <span>仪表盘</span>
      </el-menu-item>
      
      <el-menu-item index="/database">
        <el-icon><Connection /></el-icon>
        <span>数据库管理</span>
      </el-menu-item>
      
      <el-menu-item index="/query">
        <el-icon><Cpu /></el-icon>
        <span>SQL查询</span>
      </el-menu-item>
      
      <el-menu-item index="/saved">
        <el-icon><Star /></el-icon>
        <span>保存的查询</span>
      </el-menu-item>
      
      <el-sub-menu index="/audit">
        <template #title>
          <el-icon><Warning /></el-icon>
          <span>审计管理</span>
        </template>
        <el-menu-item index="/audit/logs">
          <el-icon><Document /></el-icon>
          <span>操作审计</span>
        </el-menu-item>
        <el-menu-item index="/audit/sql-logs">
          <el-icon><Cpu /></el-icon>
          <span>SQL审计</span>
        </el-menu-item>
        <el-menu-item index="/audit/rules">
          <el-icon><Setting /></el-icon>
          <span>审计规则</span>
        </el-menu-item>
      </el-sub-menu>
      
      <!-- 权限管理菜单项 -->
      <el-menu-item index="/permission">
        <el-icon><Lock /></el-icon>
        <span>权限管理</span>
      </el-menu-item>
      
      <el-menu-item index="/profile">
        <el-icon><User /></el-icon>
        <span>个人设置</span>
      </el-menu-item>
    </el-menu>
    
    <div class="collapse-button" @click="toggleCollapse">
      <el-icon v-if="isCollapse"><DArrowRight /></el-icon>
      <el-icon v-else><DArrowLeft /></el-icon>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/store/user'
import { 
  Monitor, Connection, Cpu, Star, Document, Warning, WarningFilled,
  Setting, User, DArrowLeft, DArrowRight, Lock
} from '@element-plus/icons-vue'

const route = useRoute()
const userStore = useUserStore()
const isCollapse = ref(false)

// 判断当前用户是否为管理员（更全面的判断）
const isUserAdmin = computed(() => {
  const userInfo = userStore.userInfo || JSON.parse(localStorage.getItem('userInfo') || '{}')
  const isAdmin = userStore.isAdmin || userInfo.is_superuser || userInfo.is_staff || false
  console.log('当前用户信息:', userInfo)
  console.log('是否管理员:', isAdmin)
  return isAdmin
})

// 页面加载时重新获取用户信息
onMounted(async () => {
  // 如果用户信息不完整，尝试重新获取
  if (!userStore.userInfo?.id) {
    await userStore.getUserInfo()
    console.log('重新获取的用户信息:', userStore.userInfo)
  }
})

// 计算激活的菜单项
const activeMenu = computed(() => {
  const { meta, path } = route
  return path
})

// 切换折叠状态
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

const menuItems = [
  {
    title: '仪表盘',
    icon: 'dashboard',
    path: '/dashboard'
  },
  {
    title: '数据库管理',
    icon: 'database',
    path: '/database'
  },
  {
    title: '查询管理',
    icon: 'query',
    path: '/query'
  },
  {
    title: '审计管理',
    icon: 'audit',
    path: '/audit',
    children: [
      {
        title: '操作审计',
        icon: 'audit',
        path: '/audit/logs'
      },
      {
        title: 'SQL审计',
        icon: 'sql-audit',
        path: '/audit/sql-logs'
      },
      {
        title: '审计规则',
        icon: 'rules',
        path: '/audit/rules'
      }
    ]
  },
  {
    title: '个人设置',
    icon: 'settings',
    path: '/profile'
  }
]
</script>

<style scoped>
.sidebar {
  height: 100%;
  position: relative;
  background-color: #304156;
}

.logo-container {
  height: 60px;
  padding: 10px 0;
  text-align: center;
  overflow: hidden;
}

.logo-container a {
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.logo {
  width: 32px;
  height: 32px;
  margin-right: 10px;
}

.title {
  color: #fff;
  font-size: 18px;
  font-weight: bold;
}

.el-menu {
  border-right: none;
}

.collapse-button {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #263445;
  color: #bfcbd9;
  border-radius: 50%;
}

.collapse-button:hover {
  background-color: #1f2d3d;
  color: #fff;
}
</style> 