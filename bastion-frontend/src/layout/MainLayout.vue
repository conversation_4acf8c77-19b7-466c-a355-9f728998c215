<template>
  <div class="app-wrapper">
    <!-- 侧边栏 -->
    <div class="sidebar-container" :class="{ 'is-collapsed': isCollapsed }">
      <div class="logo-container">
        <img src="./6006570.png" alt="Logo" class="logo-image" />
        <span v-if="!isCollapsed" class="logo-text">数据库堡垒机</span>
      </div>
      
      <el-menu
        :default-active="activeMenu"
        class="sidebar-menu"
        :collapse="isCollapsed"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
      >
        <el-menu-item v-if="isAdmin" index="/dashboard" @click="navigateTo('/dashboard')">
          <el-icon><Menu /></el-icon>
          <template #title>仪表盘</template>
        </el-menu-item>
        
        <el-menu-item index="/database" @click="navigateTo('/database')">
          <el-icon><Connection /></el-icon>
          <template #title>数据库管理</template>
        </el-menu-item>
        
        <el-sub-menu index="query">
          <template #title>
            <el-icon><Search /></el-icon>
            <span>查询管理</span>
          </template>
          <el-menu-item index="/query" @click="navigateTo('/query')">SQL查询</el-menu-item>
          <el-menu-item index="/saved" @click="navigateTo('/saved')">保存的查询</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu v-if="isAdmin" index="audit">
          <template #title>
            <el-icon><Warning /></el-icon>
            <span>审计管理</span>
          </template>
          <el-menu-item index="/audit/logs" @click="navigateTo('/audit/logs')">操作日志</el-menu-item>
          <el-menu-item index="/audit/sql-logs" @click="navigateTo('/audit/sql-logs')">SQL审计</el-menu-item>
          <!-- <el-menu-item index="/audit/risk" @click="navigateTo('/audit/risk')">风险SQL</el-menu-item> -->
          <el-menu-item index="/audit/rules" @click="navigateTo('/audit/rules')">审计规则</el-menu-item>
        </el-sub-menu>
        
        <el-menu-item v-if="isAdmin" index="/permission" @click="navigateTo('/permission')">
          <el-icon><Lock /></el-icon>
          <template #title>权限管理</template>
        </el-menu-item>
        
        <el-menu-item index="/profile" @click="navigateTo('/profile')">
          <el-icon><User /></el-icon>
          <template #title>个人设置</template>
        </el-menu-item>
      </el-menu>
    </div>
    
    <!-- 主内容区 -->
    <div class="main-container">
      <!-- 顶部导航 -->
      <div class="navbar">
        <div class="navbar-left">
          <el-button
            type="text"
            @click="toggleSidebar"
            class="toggle-button"
          >
            <el-icon v-if="isCollapsed"><Expand /></el-icon>
            <el-icon v-else><Fold /></el-icon>
          </el-button>
          <breadcrumb />
        </div>
        
        <div class="navbar-right">
          <el-dropdown @command="handleCommand" trigger="click">
            <div class="avatar-container">
              <el-avatar :size="30" :src="userInfo.avatar || defaultAvatar" />
              <span class="username">{{ userInfo.username }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人设置</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 页面内容 -->
      <div class="app-main">
        <router-view v-slot="{ Component }">
          <component :is="Component" />
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { 
  Menu, Connection, Search, Warning, User,
  Expand, Fold, ArrowDown, Lock
} from '@element-plus/icons-vue'
import { useUserStore } from '../store/user'
import Breadcrumb from '../components/Breadcrumb.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 默认头像
const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'

// 侧边栏收起状态
const isCollapsed = ref(false)

// 当前用户信息
const userInfo = computed(() => userStore.userInfo || {})

// 判断当前用户是否为管理员
const isAdmin = computed(() => {
  return userInfo.value.is_superuser || userInfo.value.is_staff || false
})

// 当前激活的菜单项
const activeMenu = computed(() => {
  return route.path
})

// 切换侧边栏
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

// 导航到指定路由
const navigateTo = (path) => {
  router.push(path)
}

// 下拉菜单命令处理
const handleCommand = (command) => {
  if (command === 'profile') {
    router.push('/profile')
  } else if (command === 'logout') {
    ElMessageBox.confirm('确定要退出登录吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      await userStore.logout()
      router.push('/login')
    }).catch(() => {
      // 取消操作
    })
  }
}

// 获取用户信息
onMounted(async () => {
  if (!userStore.userInfo.id) {
    await userStore.getUserInfo()
  }
})
</script>

<style scoped>
.app-wrapper {
  display: flex;
  height: 100vh;
  width: 100%;
}

.sidebar-container {
  width: 240px;
  height: 100%;
  background-color: #304156;
  transition: width 0.3s;
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-container.is-collapsed {
  width: 64px;
}

.logo-container {
  height: 64px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  background-color: #2b3649;
}

.logo-image {
  width: 36px;
  height: 36px;
  margin-right: 12px;
  filter: brightness(1.2); /* 使图标略微更亮 */
}

.logo-text {
  font-size: 18px;
  font-weight: bold;
  white-space: nowrap;
}

.sidebar-menu {
  border-right: none;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.navbar {
  height: 64px;
  background-color: white;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.toggle-button {
  font-size: 20px;
  padding: 0 15px;
  cursor: pointer;
}

.navbar-right {
  display: flex;
  align-items: center;
}

.avatar-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin: 0 5px;
  color: #606266;
}

.app-main {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f0f2f5;
}

/* 路由切换动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style> 