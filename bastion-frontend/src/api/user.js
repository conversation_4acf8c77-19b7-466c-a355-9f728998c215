import request from '../utils/request'

/**
 * 获取用户列表
 * @returns {Promise} - 返回用户列表
 */
export function getUsers() {
  console.log('调用API: 获取用户列表');
  return request({
    url: '/auth/users/',
    method: 'get'
  }).then(response => {
    console.log('获取用户列表成功:', response);
    return response;
  }).catch(error => {
    console.error('获取用户列表失败:', error);
    throw error;
  });
}

/**
 * 获取用户详情
 * @param {Number} id - 用户ID
 * @returns {Promise} - 返回用户详情
 */
export function getUserById(id) {
  return request({
    url: `/auth/users/${id}/`,
    method: 'get'
  })
}

/**
 * 获取当前用户信息
 * @returns {Promise} - 返回当前用户信息
 */
export function getCurrentUser() {
  return request({
    url: '/auth/users/me/',
    method: 'get'
  })
}

/**
 * 创建用户
 * @param {Object} data - 用户数据
 * @returns {Promise} - 返回创建结果
 */
export function createUser(data) {
  return request({
    url: '/auth/users/',
    method: 'post',
    data
  })
}

/**
 * 更新用户
 * @param {Number} id - 用户ID
 * @param {Object} data - 用户数据
 * @returns {Promise} - 返回更新结果
 */
export function updateUser(id, data) {
  return request({
    url: `/auth/users/${id}/`,
    method: 'put',
    data
  })
}

/**
 * 删除用户
 * @param {Number} id - 用户ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteUser(id) {
  return request({
    url: `/auth/users/${id}/`,
    method: 'delete'
  })
}

/**
 * 修改用户密码
 * @param {Object} data - 包含旧密码和新密码的对象
 * @returns {Promise} - 返回修改结果
 */
export function changePassword(data) {
  return request({
    url: '/auth/users/change-password/',
    method: 'post',
    data
  })
} 