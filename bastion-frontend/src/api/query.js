import request from '../utils/request'

/**
 * 执行SQL查询
 * @param {Object} data - 查询信息 {datasource_id, sql_content}
 * @param {Object} [config={}] - 自定义请求配置，例如超时设置
 * @returns {Promise} - 返回查询结果
 */
export function executeQuery(data, config = {}) {
  console.log('调用API: executeQuery', { 
    ...data, 
    sql_content: data.sql_content ? `${data.sql_content.substring(0, 50)}...` : undefined 
  });
  
  // 处理SQL语句，确保不会因为分号导致问题
  if (data.sql_content && typeof data.sql_content === 'string') {
    // 检查SQL是否包含多条语句
    const sqlStatements = data.sql_content.split(';').filter(sql => sql.trim().length > 0);
    
    // 检查是否有多条SQL语句
    if (sqlStatements.length > 1) {
      console.log(`检测到${sqlStatements.length}条SQL语句，将一次性执行所有语句`);
      // 添加多语句标记，如果后端支持的话
      data.is_multi_statement = true;
      
      // 确保分号保留在正确的位置
      // 确保每条SQL语句都以分号结尾，最后一条可以不带分号
      const statements = data.sql_content.split(';').filter(s => s.trim());
      if (statements.length > 1) {
        const formattedStatements = statements.map((s, i) => {
          const trimmed = s.trim();
          // 最后一条SQL不需要分号
          return i < statements.length - 1 ? trimmed + ';' : trimmed;
        });
        data.sql_content = formattedStatements.join(' ');
      }
      
      // 多SQL语句执行时，保留所有分号，确保每条SQL语句都能被正确执行
      // console.log(`多SQL语句执行，处理后的SQL: ${data.sql_content}`);
    } else {
      // 单SQL语句执行时，移除末尾分号
      if (data.sql_content.trim().endsWith(';')) {
        data.sql_content = data.sql_content.trim().slice(0, -1);
        console.log(`单SQL语句执行，移除末尾分号后的SQL: ${data.sql_content}`);
      }
    }
    
    // 记录处理后的SQL长度
    console.log(`SQL语句长度: ${data.sql_content.length}`);
  }
  
  // 设置默认超时时间
  const timeout = config.timeout || 300000; // 默认300秒(5分钟)，增加超时时间
  
  // 设置重试配置
  const maxRetries = config.maxRetries || 2; // 默认重试2次
  const retryDelay = config.retryDelay || 2000; // 默认延迟2秒
  
  // 计算后端查询超时时间（略小于前端超时时间）
  const queryTimeout = Math.floor(timeout / 1000) - 10; // 比前端少10秒，确保前端能收到超时响应
  
  // 确保headers存在
  const headers = config.headers || {};
  headers['X-Query-Timeout'] = queryTimeout.toString();
  
  // 添加详细日志
  console.log('发送查询请求到: /query/execute/', {
    method: 'post',
    data,
    timeout,
    headers,
    maxRetries,
    retryDelay,
    is_multi_statement: data.is_multi_statement // 添加多SQL语句标记
  });
  
  // 创建重试函数
  const executeWithRetry = (retryCount = 0) => {
    return request({
      url: '/query/execute/',
      method: 'post',
      data,
      timeout: timeout,
      headers: headers,
      ...config
    }).then(response => {
      console.log('API响应: executeQuery', response);

      // 检查响应中的success字段，如果为false，说明SQL执行失败
      if (response && response.success === false) {
        console.log('SQL执行失败，但接口调用成功:', response);
        // 直接返回响应，让调用方处理错误信息
        return response;
      }

      return response;
    }).catch(error => {
      console.error('API错误: executeQuery', error);

      const errorDetails = {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: error.config
      };
      console.error('错误详情:', errorDetails);
      
      // 检查是否是网络错误、超时或服务器错误，并且还有重试次数
      if (retryCount < maxRetries && 
         (error.message.includes('timeout') || 
          error.message.includes('Network Error') || 
          error.message.includes('socket hang up') ||
          (error.response?.status >= 500 && error.response?.status < 600))) {
        
        const delay = retryDelay * (retryCount + 1);
        console.log(`查询失败，将在 ${delay}ms 后进行第 ${retryCount + 1}/${maxRetries} 次重试...`);
        
        return new Promise(resolve => {
          setTimeout(() => {
            console.log(`开始第 ${retryCount + 1}/${maxRetries} 次重试...`);
            resolve(executeWithRetry(retryCount + 1));
          }, delay);
        });
      }
      
      // 处理特定错误
      if (error.response?.data) {
        const responseData = error.response.data;
        
        // 添加详细的调试日志
        console.log('后端返回的原始错误数据:', {
          success: responseData.success,
          message: responseData.message,
          error: responseData.error,
          error_message: responseData.error_message,
          error_code: responseData.error_code,
          error_details: responseData.error_details,
          query_id: responseData.query_id
        });
        
        // 如果后端返回了格式化的错误信息，直接返回而不是抛出错误
        if (responseData.success === false) {
          console.warn('后端返回了格式化的错误信息:', responseData);
          
          // 提取更详细的错误信息
          let errorMessage = '';
          
          // 按优先级提取错误信息
          if (responseData.error && typeof responseData.error === 'string' && responseData.error.trim() !== '') {
            errorMessage = responseData.error;
          } else if (responseData.message && typeof responseData.message === 'string' && responseData.message.trim() !== '') {
            errorMessage = responseData.message;
          } else if (responseData.error_message && typeof responseData.error_message === 'string' && responseData.error_message.trim() !== '') {
            errorMessage = responseData.error_message;
          } else {
            errorMessage = '查询执行出错，但未返回具体错误信息';
          }
          
          // 如果有详细错误信息，添加到错误消息中
          if (responseData.error_details && Array.isArray(responseData.error_details) && responseData.error_details.length > 0) {
            errorMessage += '\n详细信息: ' + responseData.error_details.join('\n');
          }
          
          console.log('提取到的错误信息:', errorMessage);
          
          return {
            success: false,
            error_message: errorMessage,
            error_code: responseData.error_code,
            query_id: responseData.query_id
          };
        }
        
        // 处理连接断开错误
        if (responseData.error_code === 2006 || responseData.error_code === 2013) {
          return {
            success: false,
            error_message: responseData.error_message || '数据库连接已断开，请重试',
            error_code: responseData.error_code
          };
        }
      }
      
      // 处理socket hang up错误
      if (error.message && error.message.includes('socket hang up')) {
        return {
          success: false,
          error_message: '连接中断，可能是查询结果过大或处理时间过长，请尝试优化查询或分批获取数据',
          error_code: 'ECONNRESET'
        };
      }
      
      // 处理其他错误，提供更友好的错误信息
      return {
        success: false,
        error_message: error.response?.data?.message || 
                      error.response?.data?.error || 
                      error.message || 
                      '查询执行失败，可能是网络问题或服务器错误。请检查SQL语法或联系管理员。',
        error_code: error.response?.status || error.code || 'UNKNOWN_ERROR'
      };
    });
  };
  
  return executeWithRetry();
}

/**
 * 获取查询历史
 * @param {Object} params - 查询参数 {datasource_id, limit, offset}
 * @returns {Promise} - 返回查询历史列表
 */
export function getQueryHistory(params) {
  return request({
    url: '/query/history/',
    method: 'get',
    params
  })
}

/**
 * 获取查询详情
 * @param {Number} id - 查询ID
 * @returns {Promise} - 返回查询详情
 */
export function getQueryDetail(id) {
  return request({
    url: `/query/history/${id}/`,
    method: 'get'
  })
}

/**
 * 获取保存的查询列表
 * @param {Object} params - 查询参数 {datasource_id, is_public}
 * @returns {Promise} - 返回保存的查询列表
 */
export function getSavedQueries(params) {
  return request({
    url: '/query/saved/',
    method: 'get',
    params
  })
}

/**
 * 获取保存的查询详情
 * @param {Number} id - 查询ID
 * @returns {Promise} - 返回保存的查询详情
 */
export function getSavedQueryById(id) {
  return request({
    url: `/query/saved/${id}/`,
    method: 'get'
  })
}

/**
 * 保存查询
 * @param {Object} data - 查询信息 {name, datasource_id, sql_content, description, is_public}
 * @returns {Promise} - 返回保存结果
 */
export function saveQuery(data) {
  return request({
    url: '/query/saved/',
    method: 'post',
    data
  })
}

/**
 * 更新保存的查询
 * @param {Number} id - 查询ID
 * @param {Object} data - 查询信息
 * @returns {Promise} - 返回更新结果
 */
export function updateSavedQuery(id, data) {
  return request({
    url: `/query/saved/${id}/`,
    method: 'put',
    data
  })
}

/**
 * 删除保存的查询
 * @param {Number} id - 查询ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteSavedQuery(id) {
  return request({
    url: `/query/saved/${id}/`,
    method: 'delete'
  })
}

/**
 * 更新表格单元格值
 * @param {Object} data - 更新信息 {datasourceId, schema, tableName, columnName, oldValue, newValue, rowData, sql}
 * @returns {Promise} - 返回更新结果
 */
export function updateCellValue(data) {
  console.log('调用API: updateCellValue - 原始数据:', data);
  
  // 确保有columnName字段
  if (!data.columnName && data.columnKey) {
    console.log('使用columnKey作为columnName:', data.columnKey);
    data.columnName = data.columnKey;
  }
  
  if (!data.columnName) {
    console.error('缺少columnName字段，无法确定要更新的字段');
    return Promise.reject(new Error('缺少columnName字段，无法确定要更新的字段'));
  }
  
  // 根据后端API要求构建请求结构
  // 1. 使用column_name和new_value构建data对象
  const updateData = {
    [data.columnName]: data.newValue
  };
  
  console.log('更新数据:', updateData);
  
  // 2. 构建where_clause用于定位要更新的行
  // 通常使用主键或唯一标识符
  let whereClause = '';
  if (data.rowData && typeof data.rowData === 'object') {
    // 尝试从行数据中找到唯一标识符来构建WHERE子句
    // 假设表中有id或其他主键字段
    const whereConditions = [];
    for (const key in data.rowData) {
      // 忽略要更新的字段
      if (key !== data.columnName) {
        const value = data.rowData[key];
        if (value === null || value === undefined) {
          whereConditions.push(`\`${key}\` IS NULL`);
        } else if (typeof value === 'string') {
          whereConditions.push(`\`${key}\` = '${value.replace(/'/g, "''")}'`);
        } else {
          whereConditions.push(`\`${key}\` = ${value}`);
        }
      }
    }
    whereClause = whereConditions.join(' AND ');
  }
  
  console.log('WHERE条件:', whereClause);
  
  // 构建完整的请求数据
  const requestData = {
    operation: 'UPDATE',           // 指定操作类型为UPDATE
    data: updateData,              // 要更新的字段和值
    where_clause: whereClause,     // WHERE子句，用于定位要更新的行
    original_data: data.rowData    // 原始行数据，仅用于参考
  };
  
  console.log('发送到后端的完整请求数据:', requestData);
  
  return request({
    url: `/query/table/edit/${data.datasourceId}/${data.tableName}/`,
    method: 'post',
    data: requestData,
    params: {
      schema: data.schema            // 添加schema作为查询参数
    },
    timeout: 15000                   // 15秒超时
  }).then(response => {
    console.log('单元格更新成功:', response);
    return response;
  }).catch(error => {
    console.error('单元格更新失败:', error);
    // 提供详细的错误信息
    const errorMessage = error.response?.data?.message || 
                        error.response?.data?.error || 
                        error.message || 
                        '更新失败，请检查数据格式或权限';
    
    return {
      success: false,
      message: errorMessage,
      code: error.response?.status || 500
    };
  });
}

/**
 * 获取表结构信息
 * @param {Number} datasourceId - 数据源ID
 * @param {String} tableName - 表名
 * @param {Object} [params={}] - 查询参数, 例如 { schema: 'public' }
 * @returns {Promise}
 */
export function getTableStructure(datasourceId, tableName, params) {
  console.log(`调用API: getTableStructure, datasourceId=${datasourceId}, tableName=${tableName}`, { params });
  
  // 确保params对象存在
  const queryParams = params || {};
  
  // 动态构建URL
  let url = `/query/table/structure/${datasourceId}/${tableName}/`;
  if (queryParams.schema) {
    // 如果schema作为独立路径段
    // url = `/database/${datasourceId}/schema/${queryParams.schema}/tables/${tableName}/structure/`;
  }

  // 添加数据源类型参数，后端可以据此判断如何处理不同类型的数据源
  if (queryParams.datasourceType) {
    console.log(`表结构请求指定了数据源类型: ${queryParams.datasourceType}`);
  }

  return request({
    url: url,
    method: 'get',
    params: queryParams,
    timeout: 60000 // 增加超时时间到60秒
  }).then(response => {
    console.log(`获取表 '${tableName}' 结构成功:`, response);
    
    // 如果是ClickHouse数据源且返回了错误，尝试使用备用方法
    if (queryParams.datasourceType === 'clickhouse' && (!response || response.success === false)) {
      console.log('检测到ClickHouse数据源，使用备用方法获取表结构');
      
      // 构建一个查询ClickHouse系统表的SQL
      const clickhouseSql = `SELECT 
        name as Field, 
        type as Type, 
        'YES' as Null, 
        '' as Key, 
        default_expression as Default, 
        '' as Extra, 
        comment as Comment 
      FROM system.columns 
      WHERE table='${tableName}'
      ${queryParams.schema ? ` AND database='${queryParams.schema}'` : ''}
      ORDER BY position`;
      
      // 直接使用request发送请求，避免循环引用executeQuery
      return request({
        url: '/query/execute/',
        method: 'post',
        data: {
          datasource_id: datasourceId,
          sql_content: clickhouseSql,
          schema: queryParams.schema
        },
        timeout: 60000
      }).then(sqlResponse => {
        console.log('通过SQL查询获取ClickHouse表结构:', sqlResponse);
        
        if (sqlResponse && sqlResponse.rows && Array.isArray(sqlResponse.rows)) {
          return sqlResponse.rows;
        } else if (sqlResponse && sqlResponse.data && sqlResponse.data.rows) {
          return sqlResponse.data.rows;
        }
        
        // 如果SQL查询也失败，返回一个友好的错误
        return Promise.reject(new Error('无法获取ClickHouse表结构，请检查表名和权限'));
      });
    }
    
    return response;
  }).catch(error => {
    console.error(`获取表 '${tableName}' 结构失败:`, error);
    // 重新抛出错误，以便调用方可以处理
    throw error;
  });
}

/**
 * 获取表索引信息
 * @param {Number} datasourceId - 数据源ID
 * @param {String} tableName - 表名
 * @param {Object} [params={}] - 查询参数, 例如 { schema: 'public' }
 * @returns {Promise}
 */
export function getTableIndexes(datasourceId, tableName, params) {
  console.log(`调用API: getTableIndexes, datasourceId=${datasourceId}, tableName=${tableName}`, { params });
  
  // 确保params对象存在
  const queryParams = params || {};
  
  // 动态构建URL
  let url = `/query/table/indexes/${datasourceId}/${tableName}/`;
  if (queryParams.schema) {
    //
  }
  
  // 如果是ClickHouse数据源，直接返回一个空数组，因为ClickHouse的索引不同于传统数据库
  if (queryParams.datasourceType === 'clickhouse') {
    console.log('检测到ClickHouse数据源，返回自定义索引信息');
    
    return Promise.resolve([
      {
        Table: tableName,
        Non_unique: 0,
        Key_name: 'PRIMARY',
        Seq_in_index: 1,
        Column_name: '(ClickHouse使用主键排序)',
        Index_type: 'ClickHouse不使用传统索引',
        Comment: 'ClickHouse使用主键来排序数据，通过稀疏索引和跳数索引优化查询'
      }
    ]);
  }
  
  return request({
    url: url,
    method: 'get',
    params: queryParams,
    timeout: 60000 // 增加超时时间到60秒
  }).then(response => {
    console.log(`获取表 '${tableName}' 索引成功:`, response);
    return response;
  }).catch(error => {
    console.error(`获取表 '${tableName}' 索引失败:`, error);
    // 重新抛出错误，以便调用方可以处理
    throw error;
  });
}

/**
 * 获取建表语句(DDL)
 * @param {Number} datasourceId - 数据源ID
 * @param {String} tableName - 表名
 * @param {Object} [params={}] - 查询参数, 例如 { schema: 'public' }
 * @returns {Promise}
 */
export function getTableDDL(datasourceId, tableName, params) {
  console.log(`调用API: getTableDDL, datasourceId=${datasourceId}, tableName=${tableName}`, { params });

  const queryParams = params || {};

  // 如果是ClickHouse数据源，直接通过SQL查询获取DDL
  if (queryParams.datasourceType === 'clickhouse') {
    console.log('检测到ClickHouse数据源，使用SHOW CREATE TABLE获取DDL');
    
    // 构建一个查询ClickHouse DDL的SQL
    const clickhouseSql = `SHOW CREATE TABLE ${queryParams.schema ? `${queryParams.schema}.` : ''}${tableName}`;
    
    // 直接使用request发送请求
    return request({
      url: '/query/execute/',
      method: 'post',
      data: {
        datasource_id: datasourceId,
        sql_content: clickhouseSql,
        schema: queryParams.schema
      },
      timeout: 60000
    }).then(sqlResponse => {
      console.log('通过SQL查询获取ClickHouse表DDL:', sqlResponse);
      
      if (sqlResponse && sqlResponse.rows && sqlResponse.rows.length > 0) {
        // SHOW CREATE TABLE通常返回一行一列的结果，包含DDL
        return sqlResponse.rows[0]['statement'] || sqlResponse.rows[0][0] || '';
      } else if (sqlResponse && sqlResponse.data && sqlResponse.data.rows && sqlResponse.data.rows.length > 0) {
        return sqlResponse.data.rows[0]['statement'] || sqlResponse.data.rows[0][0] || '';
      }
      
      return '-- 无法获取ClickHouse表DDL';
    }).catch(error => {
      console.error(`获取表 '${tableName}' DDL失败:`, error);
      return `-- 获取ClickHouse表DDL失败: ${error.message || '未知错误'}`;
    });
  }

  return request({
    url: `/query/table/ddl/${datasourceId}/${tableName}/`,
    method: 'get',
    params: queryParams,
    timeout: 60000
  }).then(response => {
    console.log(`获取表 '${tableName}' DDL成功:`, response);
    return response;
  }).catch(error => {
    console.error(`获取表 '${tableName}' DDL失败:`, error);
    throw error;
  });
}

/**
 * 直接执行DDL语句 (例如 ALTER TABLE)
 * @param {Object} data - { datasource_id, schema, sql_content }
 * @returns {Promise}
 */
export function executeDdl(data) {
  console.log(`调用API: executeDdl`, { data });
  return request({
    url: '/query/execute-ddl/',
    method: 'post',
    data,
    timeout: 120000 // DDL operations can be slow, give it 2 minutes
  });
}

/**
 * 导出表结构
 * @param {Number} datasourceId - 数据源ID
 * @param {String} tableName - 表名
 * @param {Object} params - 查询参数，可包含schema信息
 * @returns {Promise} - 返回表结构SQL
 */
export function exportTableStructure(datasourceId, tableName, params) {
  console.log('调用API: exportTableStructure', { datasourceId, tableName, params });
  return request({
    url: `/query/table/export/structure/${datasourceId}/${tableName}/`,
    method: 'get',
    params
  }).then(response => {
    console.log('API响应: exportTableStructure', response);
    return response;
  }).catch(error => {
    console.error('API错误: exportTableStructure', error);
    throw error;
  });
}

/**
 * 导出表数据
 * @param {Number} datasourceId - 数据源ID
 * @param {String} tableName - 表名
 * @param {Object} params - 查询参数 {format, limit}
 * @returns {Promise} - 返回表数据
 */
export function exportTableData(datasourceId, tableName, params) {
  return request({
    url: `/query/table/export/data/${datasourceId}/${tableName}/`,
    method: 'get',
    params
  })
}

/**
 * 编辑表数据
 * @param {Number} datasourceId - 数据源ID
 * @param {String} tableName - 表名
 * @param {Object} data - 编辑数据 {operation, data, where_clause}
 * @param {Object} params - 查询参数，可包含schema信息
 * @returns {Promise} - 返回编辑结果
 */
export function editTableData(datasourceId, tableName, data, params) {
  return request({
    url: `/query/table/edit/${datasourceId}/${tableName}/`,
    method: 'post',
    data,
    params
  })
}

/**
 * 删除查询记录
 * @param {Number} id - 查询记录ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteQueryRecord(id) {
  return request({
    url: `/query/history/${id}/delete/`,
    method: 'delete'
  })
}