import request from '../utils/request'

/**
 * 用户登录
 * @param {Object} data - 登录信息 {username, password}
 * @returns {Promise} - 返回登录结果
 */
export function login(data) {
  console.log('调用登录API，参数:', { username: data.username, password: '******' })
  return request({
    url: '/auth/login/',
    method: 'post',
    data,
    // 添加额外的错误处理
    validateStatus: function (status) {
      // 允许处理所有状态码，不抛出错误
      return true
    }
  }).then(response => {
    console.log('登录API原始响应:', response)
    return response
  }).catch(error => {
    console.error('登录API错误:', error)
    throw error
  })
}

/**
 * 用户登出
 * @returns {Promise} - 返回登出结果
 */
export function logout() {
  return request({
    url: '/auth/logout/',
    method: 'post'
  })
}

/**
 * 用户注册
 * @param {Object} data - 注册信息 {username, password, email}
 * @returns {Promise} - 返回注册结果
 */
export function register(data) {
  return request({
    url: '/auth/register/',
    method: 'post',
    data
  })
}

/**
 * 获取用户信息
 * @returns {Promise} - 返回用户信息
 */
export function getUserProfile() {
  return request({
    url: '/auth/user/profile/',
    method: 'get'
  })
}

/**
 * 更新用户信息
 * @param {Object} data - 用户信息
 * @returns {Promise} - 返回更新结果
 */
export function updateUserProfile(data) {
  return request({
    url: '/auth/user/profile/',
    method: 'put',
    data
  })
}

/**
 * 修改密码
 * @param {Object} data - 密码信息 {old_password, new_password}
 * @returns {Promise} - 返回修改结果
 */
export function changePassword(data) {
  return request({
    url: '/auth/user/change-password/',
    method: 'post',
    data
  })
}

/**
 * 获取角色列表
 * @returns {Promise} - 返回角色列表
 */
export function getRoles() {
  return request({
    url: '/auth/roles/',
    method: 'get'
  })
}

/**
 * 获取角色详情
 * @param {Number} id - 角色ID
 * @returns {Promise} - 返回角色详情
 */
export function getRoleById(id) {
  return request({
    url: `/auth/roles/${id}/`,
    method: 'get'
  })
}

/**
 * 获取权限列表
 * @returns {Promise} - 返回权限列表
 */
export function getPermissions() {
  return request({
    url: '/auth/permissions/',
    method: 'get'
  })
} 