import request from '@/utils/request'

/**
 * 获取仪表盘统计数据
 * @param {Object} params - 请求参数
 * @param {string} params.period - 查询统计周期，可选值：'week'、'month'或'custom'
 * @param {string} [params.start_date] - 自定义开始日期，格式：YYYY-MM-DD
 * @param {string} [params.end_date] - 自定义结束日期，格式：YYYY-MM-DD
 * @returns {Promise} - 返回Promise对象
 */
export function getDashboardStatistics(params = { period: 'week' }) {
  return request({
    url: '/dashboard/statistics/',
    method: 'get',
    params
  })
} 