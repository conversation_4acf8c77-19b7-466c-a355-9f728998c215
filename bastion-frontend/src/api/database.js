import request from '@/utils/request'
import axios from 'axios'

/**
 * 获取指定数据源的模式列表
 * @param {number} datasourceId 数据源ID
 * @returns {Promise<Array>}
 */
export function getSchemasForDatasource(datasourceId) {
  console.log(`调用getSchemaList API: /datasource/schema-list/${datasourceId}/`)
  return request({
    url: `/datasource/schema-list/${datasourceId}/`,
    method: 'get'
  })
}

/**
 * 获取指定模式下的所有表
 * @param {number} datasourceId - The ID of the datasource.
 * @param {object} params - 其他参数，例如 schema
 * @returns {Promise<Array>}
 */
export function getDatabaseTables(datasourceId, params) {
  return request({
    url: '/query/database/tables/',
    method: 'get',
    params: {
      ...params,
      datasource_id: datasourceId
    }
  });
}

/**
 * 获取指定表的所有列
 * @param {number} datasourceId - The ID of the datasource.
 * @param {string} tableName - The name of the table.
 * @param {object} params - 其他参数，例如 schema
 * @returns {Promise<Array>}
 */
export function getTableColumns(datasourceId, tableName, params) {
  return request({
    url: '/query/database/columns/',
    method: 'get',
    params: {
      ...params,
      datasource_id: datasourceId,
      table_name: tableName
    }
  });
}

/**
 * 获取所有可用的数据库列表
 * @param {number} datasourceId - 数据源ID
 * @returns {Promise<Array>} 数据库名称列表
 */
export function getDatabases(datasourceId) {
  return request({
    url: '/query/database/full-metadata',
    method: 'get',
    params: {
      datasource_id: datasourceId
    }
  }).then(response => {
    if (!response || !Array.isArray(response.data)) {
      console.warn('获取数据库列表返回格式异常:', response);
      return [];
    }
    return response.data.map(db => ({
      name: db.database_name || db.name,
      comment: db.comment || ''
    }));
  });
}

/**
 * 获取指定数据源的完整元数据（用于自动补全缓存）
 * @param {number} datasourceId - The ID of the datasource.
 * @returns {Promise<object>}
 */
export function getDatabaseFullMetadata(datasourceId) {
  return request({
    url: '/query/database/full-metadata/',
    method: 'get',
    params: {
      datasource_id: datasourceId
    },
    timeout: 60000 // 增加超时时间
  }).catch(error => {
    console.warn(`元数据API访问失败，尝试替代方案: ${error.message}`);
    // 如果主API失败，尝试通过组合其他API获取元数据
    return combineMetadataFromOtherApis(datasourceId);
  });
}

/**
 * 当主元数据API失败时，通过组合schema和tables API构建元数据
 * @param {number} datasourceId 
 * @returns {Promise<object>}
 */
async function combineMetadataFromOtherApis(datasourceId) {
  try {
    // 首先获取所有schema
    const schemas = await getSchemasForDatasource(datasourceId);
    const schemaList = Array.isArray(schemas) ? schemas : 
                     (schemas.schemas || schemas.data || []);
    
    // 构建结果对象
    const metadata = {};
    
    // 对每个schema获取表信息
    for (const schema of schemaList) {
      const schemaName = typeof schema === 'string' ? schema : (schema.name || schema.value);
      if (!schemaName) continue;
      
      // 获取该schema下的所有表
      const tables = await getDatabaseTables(datasourceId, { schema: schemaName });
      const tableList = Array.isArray(tables) ? tables : (tables.tables || tables.data || []);
      
      metadata[schemaName] = {};
      
      // 简化处理表信息
      for (const table of tableList) {
        const tableName = typeof table === 'string' ? table : (table.name || table.table_name);
        if (!tableName) continue;
        
        metadata[schemaName][tableName] = { columns: [] };
        
        // 不立即加载列信息，而是在用户输入表名后再加载，以提高性能
      }
    }
    
    console.log(`已成功构建替代元数据，包含${Object.keys(metadata).length}个schema`);
    return metadata;
  } catch (error) {
    console.error('构建替代元数据失败:', error);
    // 返回空对象避免错误
    return {};
  }
}

// 新增数据行
export function addTableRow(params) {
  return request({
    url: `/query/table/edit/${params.datasourceId}/${params.tableName}/`,
    method: 'post',
    data: {
      operation: 'insert',
      schema_name: params.database,
      row_data: params.row
    }
  })
}

// 更新数据行
export function updateTableData(params) {
  console.log('updateTableData被调用，参数:', {
    datasourceId: params.datasourceId,
    tableName: params.tableName, 
    database: params.database,
    columnName: params.columnName,
    newValue: params.newValue
  });
  
  // 如果缺少必要参数，记录错误并返回失败
  if (!params.datasourceId || !params.tableName || !params.database) {
    const error = `缺少必要参数: datasourceId=${params.datasourceId}, tableName=${params.tableName}, database=${params.database}`;
    console.error(error);
    return Promise.reject(new Error(error));
  }
  
  // 不再检查新值和原值是否相同，始终执行更新操作
  console.log('执行更新操作，即使新值与原值可能相同', params);

  const url = `/query/table/edit/${params.datasourceId}/${params.tableName}/`;
  
  // 创建条件对象，移除正在更新的列
  const conditions = { ...params.row };
  if (conditions[params.columnName]) {
    console.log(`从WHERE条件中移除正在更新的列: ${params.columnName}`);
    delete conditions[params.columnName];
  }
  
  // 如果条件为空，尝试使用其他主键或唯一列作为条件
  if (Object.keys(conditions).length === 0) {
    console.warn('没有可用的WHERE条件，更新可能影响多行数据');
    return Promise.reject(new Error('没有足够的条件来唯一标识记录，操作已取消'));
  }
  
  const data = {
    operation: 'update',
    schema_name: params.database,
    row_data: {
      [params.columnName]: params.newValue
    },
    conditions: conditions  // 使用不包含正在更新列的条件
  };
  
  console.log(`准备发送POST请求到 ${url}`, data);
  console.log('完整条件数据:', conditions);
  
  return request({
    url: url,
    method: 'post',
    data: data
  }).then(response => {
    console.log('更新表数据成功:', response);
    return response;
  }).catch(error => {
    console.error('更新表数据失败:', error);
    throw error;
  });
}

// 删除数据行
export function deleteTableRow(params) {
  // 确保params包含所有必要的字段
  if (!params.database) {
    console.error('deleteTableRow: 缺少必要参数database_name');
    return Promise.reject(new Error('删除操作缺少必要的database_name参数'));
  }
  
  if (!params.row || Object.keys(params.row).length === 0) {
    console.error('deleteTableRow: 缺少必要的row条件');
    return Promise.reject(new Error('删除操作缺少必要的where条件'));
  }

  console.log('删除行参数:', params);
  
  return request({
    url: `/query/table/edit/${params.datasourceId}/${params.tableName}/`,
    method: 'post',
    data: {
      operation: 'delete',
      schema_name: params.database,
      conditions: params.row  // 使用整行数据作为条件
    }
  })
}

// 批量提交更改
export function submitBatchChanges(params) {
  if (!params.changes || !params.changes.length) {
    console.error('批量提交函数被调用，但没有要提交的更改:', params);
    return Promise.reject(new Error('没有要提交的更改'));
  }

  console.log('批量提交更改 - 函数被调用:', {
    datasourceId: params.datasourceId,
    database: params.database,
    schema: params.schema,
    tableName: params.tableName,
    changeCounts: params.changes.length
  });
  console.log('变更详情:', JSON.stringify(params.changes));
  
  // 首先按行合并更新操作，避免同一行多个字段单独更新
  const groupedChanges = [];
  const updatesByRow = new Map(); // 使用Map来分组同一行的更新操作
  
  // 清理row对象的辅助函数
  const cleanRowObject = (row) => {
    if (!row) return {};
    const cleanRow = {};
    for (const key in row) {
      // 排除以__v开头的Vue内部属性和其他非数据属性
      if (!key.startsWith('__v') && key !== '_rowKey' && typeof row[key] !== 'function') {
        cleanRow[key] = row[key];
      }
    }
    return cleanRow;
  };
  
  // 第一步：识别和分组针对同一行的多个更新
  params.changes.forEach((change, index) => {
    // 清理row数据
    if (change.row) {
      change.row = cleanRowObject(change.row);
    }
    
    // 添加和删除操作直接添加到结果数组
    if (change.type === 'add' || change.type === 'insert' || change.type === 'delete') {
      groupedChanges.push({...change, originalIndex: index});
      return;
    }
    
    // 处理更新操作，尝试合并同一行的多个字段更新
    if (change.type === 'update') {
      // 需要一个可靠的方式来标识同一行
      // 使用主键值或行的完整数据来创建行标识符
      let rowKey = '';
      
      // 尝试使用id作为主键
      if (change.row.id !== undefined) {
        rowKey = `id:${change.row.id}`;
      } else {
        // 如果没有id，使用行的所有数据（不包括要更新的字段）创建一个键
        const rowData = {...change.row};
        if (change.columnName && rowData[change.columnName] !== undefined) {
          delete rowData[change.columnName]; // 移除正在更新的字段
        }
        rowKey = JSON.stringify(rowData);
      }
      
      // 检查这一行是否已有更新
      if (updatesByRow.has(rowKey)) {
        // 获取现有更新并添加当前字段
        const existingUpdate = updatesByRow.get(rowKey);
        existingUpdate.fields[change.columnName || change.column || change.columnKey] = change.newValue;
        console.log(`合并对同一行的字段更新: ${change.columnName || change.column || change.columnKey} = ${change.newValue}`);
      } else {
        // 创建新的更新组
        updatesByRow.set(rowKey, {
          type: 'update',
          row: change.row,
          fields: {
            [change.columnName || change.column || change.columnKey]: change.newValue
          },
          originalIndex: index
        });
      }
    }
  });
  
  // 将合并后的更新操作添加到结果数组
  updatesByRow.forEach(update => {
    groupedChanges.push(update);
  });
  
  console.log('合并后的变更数量:', groupedChanges.length);
  console.log('合并后的变更详情:', JSON.stringify(groupedChanges));
  
  // 创建一个数组来保存所有的更改操作Promise
  const changePromises = groupedChanges.map((change, index) => {
    console.log(`处理第${index+1}个变更:`, change.type);
    const operation = change.type;

    // 根据操作类型调用不同API
    if (operation === 'add' || operation === 'insert') {
      return addTableRow({
        datasourceId: params.datasourceId,
        database: params.database,
        tableName: params.tableName,
        row: change.row
      }).then(result => {
        console.log(`第${index+1}个变更(${operation})处理成功:`, result);
        return { ...result, changeIndex: change.originalIndex, type: operation };
      }).catch(error => {
        console.error(`第${index+1}个变更(${operation})处理失败:`, error);
        throw error; // 重新抛出错误以便被外层捕获
      });
    } else if (operation === 'update') {
      // 处理合并后的更新操作，一次提交多个字段
      console.log(`提交合并更新，字段数: ${Object.keys(change.fields).length}`);
      
      return request({
        url: `/query/table/edit/${params.datasourceId}/${params.tableName}/`,
        method: 'post',
        data: {
          operation: 'update',
          schema_name: params.database,
          row_data: change.fields, // 使用合并后的所有字段
          conditions: change.row  // 使用完整的行数据作为条件
        }
      }).then(result => {
        console.log(`第${index+1}个合并更新处理成功:`, result);
        return { ...result, changeIndex: change.originalIndex, type: operation };
      }).catch(error => {
        console.error(`第${index+1}个合并更新处理失败:`, error);
        throw error; // 重新抛出错误以便被外层捕获
      });
    } else if (operation === 'delete') {
      return deleteTableRow({
        datasourceId: params.datasourceId,
        database: params.database,
        tableName: params.tableName,
        row: change.row
      }).then(result => {
        console.log(`第${index+1}个变更(${operation})处理成功:`, result);
        return { ...result, changeIndex: change.originalIndex, type: operation };
      }).catch(error => {
        console.error(`第${index+1}个变更(${operation})处理失败:`, error);
        throw error; // 重新抛出错误以便被外层捕获
      });
    } else {
      console.error(`不支持的操作类型: ${operation}`);
      return Promise.reject(new Error(`不支持的操作类型: ${operation}`));
    }
  });

  // 串行执行所有操作以确保按顺序执行
  return changePromises.reduce((chain, currentPromise, index) => {
    return chain.then(results => 
      currentPromise
        .then(result => {
          console.log(`成功执行变更 #${index+1}`);
          return [...results, result];
        })
        .catch(error => {
          console.error(`执行变更 #${index+1} 失败:`, error);
          // 继续执行后续变更，但记录错误
          const errorMessage = error.response?.data?.message || error.message || '未知错误';
          return [...results, { error: errorMessage, changeIndex: index }];
        })
    );
  }, Promise.resolve([])).then(results => {
    console.log('所有变更执行结果:', results);
    
    // 检查是否有错误
    const errors = results.filter(r => r.error);
    if (errors.length > 0) {
      console.warn(`${errors.length}个变更执行出错:`, errors);
    }
    
    // 汇总结果
    const summary = {
      success: errors.length === 0,
      total: results.length,
      successful: results.length - errors.length,
      failed: errors.length,
      results: results,
      errors: errors.length > 0 ? errors : null
    };
    
    return summary;
  }).catch(error => {
    console.error('批量提交过程中发生错误:', error);
    return {
      success: false,
      message: error.message || '批量提交过程中发生错误',
      error: error
    };
  });
}

// 获取CSRF令牌
function getCsrfToken() {
  // 从cookie中获取CSRF令牌
  const name = 'csrftoken='
  const decodedCookie = decodeURIComponent(document.cookie)
  const cookies = decodedCookie.split(';')
  
  for (let i = 0; i < cookies.length; i++) {
    let cookie = cookies[i].trim()
    if (cookie.indexOf(name) === 0) {
      return cookie.substring(name.length, cookie.length)
    }
  }
  return null
}