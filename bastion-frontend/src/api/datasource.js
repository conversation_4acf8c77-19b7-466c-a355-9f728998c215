import request from '../utils/request'

/**
 * 获取数据库类型列表
 * @returns {Promise} - 返回数据库类型列表
 */
export function getDatabaseTypes() {
  return request({
    url: '/datasource/types/',
    method: 'get'
  })
}

/**
 * 获取数据源列表
 * @param {Object} params - 查询参数，可选，如 { onlyAuthorized: true } 只获取有权限的数据源
 * @returns {Promise} - 返回数据源列表
 */
export function getDatasources(params) {
  return request({
    url: '/datasource/datasources/',
    method: 'get',
    params
  })
}

/**
 * 获取数据源详情
 * @param {Number} id - 数据源ID
 * @returns {Promise} - 返回数据源详情
 */
export function getDatasourceById(id) {
  return request({
    url: `/datasource/datasources/${id}/`,
    method: 'get'
  })
}

/**
 * 创建数据源
 * @param {Object} data - 数据源信息
 * @returns {Promise} - 返回创建结果
 */
export function createDatasource(data) {
  // 创建数据的副本，以便修改
  const requestData = { ...data };
  
  // 确保我们有db_type_id字段
  if (!requestData.db_type_id) {
    console.error('缺少db_type_id字段，这可能导致后端验证错误');
  }
  
  return request({
    url: '/datasource/datasources/',
    method: 'post',
    data: requestData
  })
}

/**
 * 更新数据源
 * @param {Number} id - 数据源ID
 * @param {Object} data - 数据源信息
 * @returns {Promise} - 返回更新结果
 */
export function updateDatasource(id, data) {
  return request({
    url: `/datasource/datasources/${id}/`,
    method: 'put',
    data
  })
}

/**
 * 删除数据源
 * @param {Number} id - 数据源ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteDatasource(id) {
  return request({
    url: `/datasource/datasources/${id}/`,
    method: 'delete'
  })
}

/**
 * 测试数据源连接
 * @param {Number} id - 数据源ID
 * @returns {Promise} - 返回测试结果
 */
export function testConnection(id) {
  return request({
    url: `/datasource/datasources/${id}/test-connection/`,
    method: 'post'
  })
}

/**
 * 测试临时数据源连接
 * @param {Object} data - 临时数据源信息
 * @returns {Promise} - 返回测试结果
 */
export function testTempConnection(data) {
  // 创建数据的副本，以便修改
  const requestData = { ...data };
  
  // 确保我们有db_type_id字段
  if (!requestData.db_type_id) {
    console.error('缺少db_type_id字段，这可能导致后端验证错误');
  }
  
  return request({
    url: '/datasource/test-connection/',
    method: 'post',
    data: requestData
  })
}

/**
 * 获取数据源权限列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回数据源权限列表
 */
export function getDatasourcePermissions(params) {
  return request({
    url: '/datasource/datasource-permissions/',
    method: 'get',
    params
  })
}

/**
 * 授予数据源权限
 * @param {Object} data - 权限信息
 * @returns {Promise} - 返回授权结果
 */
export function grantDatasourcePermission(data) {
  return request({
    url: '/datasource/datasource-permissions/',
    method: 'post',
    data
  })
}

/**
 * 更新数据源权限
 * @param {Number} id - 权限ID
 * @param {Object} data - 权限信息
 * @returns {Promise} - 返回更新结果
 */
export function updateDatasourcePermission(id, data) {
  return request({
    url: `/datasource/datasource-permissions/${id}/`,
    method: 'put',
    data
  })
}

/**
 * 撤销数据源权限
 * @param {Number} id - 权限ID
 * @returns {Promise} - 返回撤销结果
 */
export function revokeDatasourcePermission(id) {
  return request({
    url: `/datasource/datasource-permissions/${id}/`,
    method: 'delete'
  })
}

/**
 * 获取所有数据源的连接状态（从缓存）
 * @returns {Promise} - 返回所有数据源的状态信息
 */
export function getDatasourceStatus() {
  return request({
    url: '/datasource/status/',
    method: 'get'
  })
}

/**
 * 获取单个数据源的连接状态（从缓存）
 * @param {Number} id - 数据源ID
 * @returns {Promise} - 返回单个数据源的状态信息
 */
export function getSingleDatasourceStatus(id) {
  return request({
    url: `/datasource/status/${id}/`,
    method: 'get'
  })
}

/**
 * 获取数据库所有模式列表
 * @param {Number} id - 数据源ID
 * @returns {Promise} - 返回数据库所有模式列表
 */
export function getSchemaList(id) {
  console.log(`调用getSchemaList API: /datasource/schema-list/${id}/`)
  return request({
    url: `/datasource/schema-list/${id}/`,
    method: 'get'
  })
}

/**
 * 获取数据库 schema（表结构）
 * @param {number} datasourceId - 数据源ID
 * @param {string} schemaName - schema名称
 * @param {object} options - 可选配置项
 * @param {boolean} detailed - whether to return detailed information
 * @returns {Promise} - API响应
 */
export function getDatabaseSchema(datasourceId, schemaName, params = {}) {
  let url = `/datasource/database-schema/${datasourceId}/`;
  
  // Append schemaName to the path if it's provided and not empty.
  if (schemaName) {
    // Backend might use a placeholder for default/empty schema, let's just encode it.
    url += `${encodeURIComponent(schemaName)}/`;
  }
  
  // Append any extra parameters as a query string.
  if (Object.keys(params).length > 0) {
    url += `?${new URLSearchParams(params).toString()}`;
  }

  return request({
    url: url,
    method: 'get'
  });
}

/**
 * 获取数据源支持的数据库列表
 * @param {Number} id - 数据源ID
 * @returns {Promise} - 返回数据源支持的数据库列表
 */
export function getSupportedDatabases(id) {
  return request({
    url: `/datasource/datasources/${id}/supported-databases/`,
    method: 'get'
  })
} 