import request from '../utils/request'

/**
 * 获取审计日志列表
 * @param {Object} params - 查询参数 {keyword, database_id, user_id, action_type, risk_level, start_date, end_date, page, limit}
 * @returns {Promise} - 返回审计日志列表
 */
export function getAuditLogs(params) {
  return request({
    url: '/audit/logs/',
    method: 'get',
    params
  })
}

/**
 * 获取审计规则列表
 * @param {Object} params - 查询参数 {rule_type, risk_level, is_enabled}
 * @returns {Promise} - 返回审计规则列表
 */
export function getAuditRules(params) {
  return request({
    url: '/audit/rules/',
    method: 'get',
    params,
    transformResponse: [function (data) {
      try {
        // 尝试解析JSON
        const parsedData = JSON.parse(data);
        // 返回适当的格式，保留原始数据结构
        return { data: parsedData };
      } catch (error) {
        console.error('解析审计规则数据失败:', error);
        return { data: [] };
      }
    }]
  })
}

/**
 * 检查SQL是否符合审计规则
 * @param {Object} data - 包含SQL内容和数据库ID {sql_content, database_id}
 * @returns {Promise} - 返回审计结果，包括风险等级和匹配的规则
 */
export function checkSqlWithRules(data) {
  return request({
    url: '/audit/check-sql/',
    method: 'post',
    data
  })
}

/**
 * 获取审计规则详情
 * @param {Number} id - 规则ID
 * @returns {Promise} - 返回审计规则详情
 */
export function getAuditRuleById(id) {
  return request({
    url: `/audit/rules/${id}/`,
    method: 'get'
  })
}

/**
 * 创建审计规则
 * @param {Object} data - 规则信息 {name, rule_type, pattern, description, risk_level, is_enabled}
 * @returns {Promise} - 返回创建结果
 */
export function createAuditRule(data) {
  return request({
    url: '/audit/rules/',
    method: 'post',
    data
  })
}

/**
 * 更新审计规则
 * @param {Number} id - 规则ID
 * @param {Object} data - 规则信息
 * @returns {Promise} - 返回更新结果
 */
export function updateAuditRule(id, data) {
  return request({
    url: `/audit/rules/${id}/`,
    method: 'put',
    data
  })
}

/**
 * 删除审计规则
 * @param {Number} id - 规则ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteAuditRule(id) {
  return request({
    url: `/audit/rules/${id}/`,
    method: 'delete'
  })
}

/**
 * 获取查询的审计结果
 * @param {Number} queryId - 查询ID
 * @returns {Promise} - 返回审计结果列表
 */
export function getAuditResults(queryId) {
  return request({
    url: `/audit/results/${queryId}/`,
    method: 'get'
  })
}

/**
 * 导出审计日志
 * @param {Object} params - 查询参数，与getAuditLogs相同
 * @returns {Promise} - 返回文件下载链接
 */
export function exportAuditLogs(params) {
  return request({
    url: '/audit/export/',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取SQL审计日志
export function getSqlAuditLogs(params) {
  return request({
    url: '/audit/sql-logs/',
    method: 'get',
    params
  })
}

// 导出SQL审计日志
export function exportSqlAuditLogs(params) {
  return request({
    url: '/audit/sql-logs/export/',
    method: 'get',
    params,
    responseType: 'blob'
  })
} 