import request from '../utils/request'

/**
 * 获取表级权限列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回权限列表
 */
export function getTablePermissions(params) {
  return request({
    url: '/datasource/table-permissions/',
    method: 'get',
    params
  })
}

/**
 * 获取指定数据源的表级权限
 * @param {Number} datasourceId - 数据源ID
 * @returns {Promise} - 返回权限列表
 */
export function getPermissionsByDatasource(datasourceId) {
  return request({
    url: `/datasource/table-permissions/by_datasource/?datasource_id=${datasourceId}`,
    method: 'get'
  })
}

/**
 * 获取当前用户有权限的表
 * @param {Number} datasourceId - 可选，数据源ID
 * @returns {Promise} - 返回权限列表
 */
export function getUserTables(datasourceId) {
  const params = datasourceId ? { datasource_id: datasourceId } : {}
  console.log('获取用户表权限，参数:', params, '时间戳:', Date.now())
  return request({
    url: '/datasource/table-permissions/user_tables/',
    method: 'get',
    params
  }).then(response => {
    console.log('用户表权限API响应:', response, '时间戳:', Date.now())
    if (!response || (!response.data && !response.results && !Array.isArray(response))) {
      console.warn('用户表权限API返回无效数据:', response)
    } else {
      const data = response.data || response.results || response
      console.log('权限表数量:', Array.isArray(data) ? data.length : '无法确定')
    }
    return response
  }).catch(error => {
    console.error('用户表权限API错误:', error, '时间戳:', Date.now())
    console.error('错误详情:', error.message)
    if (error.response) {
      console.error('响应状态:', error.response.status)
      console.error('响应数据:', error.response.data)
    }
    throw error
  })
}

/**
 * 创建表级权限
 * @param {Object} data - 权限数据
 * @returns {Promise} - 返回创建结果
 */
export function createTablePermission(data) {
  return request({
    url: '/datasource/table-permissions/',
    method: 'post',
    data
  })
}

/**
 * 批量创建表级权限
 * @param {Object} data - 批量权限数据 {datasource_id, user_id, tables, permission_type}
 * @returns {Promise} - 返回创建结果
 */
export function batchCreateTablePermissions(data) {
  return request({
    url: '/datasource/table-permissions/batch_create/',
    method: 'post',
    data
  })
}

/**
 * 更新表级权限
 * @param {Number} id - 权限ID
 * @param {Object} data - 权限数据
 * @returns {Promise} - 返回更新结果
 */
export function updateTablePermission(id, data) {
  return request({
    url: `/datasource/table-permissions/${id}/`,
    method: 'put',
    data
  })
}

/**
 * 删除表级权限
 * @param {Number} id - 权限ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteTablePermission(id) {
  return request({
    url: `/datasource/table-permissions/${id}/`,
    method: 'delete'
  })
}

/**
 * 批量删除表级权限
 * @param {Array} permissionIds - 权限ID数组
 * @returns {Promise} - 返回删除结果
 */
export function batchDeleteTablePermissions(permissionIds) {
  return request({
    url: '/datasource/table-permissions/batch_delete/',
    method: 'post',
    data: { permission_ids: permissionIds }
  })
}

/**
 * 获取数据源权限列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回权限列表
 */
export function getDatasourcePermissions(params) {
  return request({
    url: '/datasource/datasource-permissions/',
    method: 'get',
    params
  })
}

/**
 * 创建数据源权限
 * @param {Object} data - 权限数据
 * @returns {Promise} - 返回创建结果
 */
export function createDatasourcePermission(data) {
  return request({
    url: '/datasource/datasource-permissions/',
    method: 'post',
    data
  })
}

/**
 * 更新数据源权限
 * @param {Number} id - 权限ID
 * @param {Object} data - 权限数据
 * @returns {Promise} - 返回更新结果
 */
export function updateDatasourcePermission(id, data) {
  return request({
    url: `/datasource/datasource-permissions/${id}/`,
    method: 'put',
    data
  })
}

/**
 * 删除数据源权限
 * @param {Number} id - 权限ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteDatasourcePermission(id) {
  return request({
    url: `/datasource/datasource-permissions/${id}/`,
    method: 'delete'
  })
}

/**
 * 获取用户列表
 * @returns {Promise} - 返回用户列表
 */
export function getUsers() {
  return request({
    url: '/auth/users/',
    method: 'get'
  })
} 