import { defineStore } from 'pinia'
import { getDatasources, getDatasourceById, createDatasource, updateDatasource, deleteDatasource, testConnection } from '../api/datasource'

export const useDatasourceStore = defineStore('datasource', {
  state: () => ({
    datasources: [],
    currentDatasource: null,
    loading: false,
    error: null
  }),
  
  getters: {
    // 获取数据源列表
    getDatasourceList: (state) => state.datasources,
    
    // 获取当前选中的数据源
    getCurrentDatasource: (state) => state.currentDatasource,
    
    // 获取数据源总数
    getDatasourceCount: (state) => state.datasources.length
  },
  
  actions: {
    // 加载数据源列表
    async loadDatasources() {
      this.loading = true
      this.error = null
      
      try {
        const response = await getDatasources()
        this.datasources = response
        return response
      } catch (error) {
        this.error = error.message || '获取数据源列表失败'
        console.error('获取数据源列表时发生错误:', error)
        return []
      } finally {
        this.loading = false
      }
    },
    
    // 根据ID获取数据源详情
    async getDatasourceDetail(id) {
      this.loading = true
      this.error = null
      
      try {
        const response = await getDatasourceById(id)
        this.currentDatasource = response
        return response
      } catch (error) {
        this.error = error.message || '获取数据源详情失败'
        console.error('获取数据源详情时发生错误:', error)
        return null
      } finally {
        this.loading = false
      }
    },
    
    // 创建数据源
    async createDatasource(datasourceData) {
      this.loading = true
      this.error = null
      
      try {
        const response = await createDatasource(datasourceData)
        if (response.success) {
          // 刷新数据源列表
          await this.loadDatasources()
        }
        return response
      } catch (error) {
        this.error = error.message || '创建数据源失败'
        console.error('创建数据源时发生错误:', error)
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },
    
    // 更新数据源
    async updateDatasource(id, datasourceData) {
      this.loading = true
      this.error = null
      
      try {
        const response = await updateDatasource(id, datasourceData)
        if (response.success) {
          // 刷新数据源列表和当前数据源
          await this.loadDatasources()
          if (this.currentDatasource && this.currentDatasource.id === id) {
            await this.getDatasourceDetail(id)
          }
        }
        return response
      } catch (error) {
        this.error = error.message || '更新数据源失败'
        console.error('更新数据源时发生错误:', error)
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },
    
    // 删除数据源
    async deleteDatasource(id) {
      this.loading = true
      this.error = null
      
      try {
        const response = await deleteDatasource(id)
        if (response.success) {
          // 刷新数据源列表
          await this.loadDatasources()
          // 如果当前选中的是被删除的数据源，清空当前数据源
          if (this.currentDatasource && this.currentDatasource.id === id) {
            this.currentDatasource = null
          }
        }
        return response
      } catch (error) {
        this.error = error.message || '删除数据源失败'
        console.error('删除数据源时发生错误:', error)
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },
    
    // 测试数据源连接
    async testDatasourceConnection(id) {
      this.loading = true
      this.error = null
      
      try {
        const response = await testConnection(id)
        return response
      } catch (error) {
        this.error = error.message || '测试连接失败'
        console.error('测试数据源连接时发生错误:', error)
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },
    
    // 设置当前数据源
    setCurrentDatasource(datasource) {
      this.currentDatasource = datasource
    },
    
    // 清空当前数据源
    clearCurrentDatasource() {
      this.currentDatasource = null
    }
  }
}) 