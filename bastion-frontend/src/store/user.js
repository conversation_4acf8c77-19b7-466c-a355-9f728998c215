import { defineStore } from 'pinia'
import { login, logout, getUserProfile } from '../api/auth'

// 定义用户存储
export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: JSON.parse(localStorage.getItem('userInfo') || '{}'),
    isAuthenticated: !!localStorage.getItem('token')
  }),
  
  getters: {
    // 是否是管理员
    isAdmin: (state) => state.userInfo?.is_superuser || state.userInfo?.is_staff || false,
    
    // 用户角色
    userRole: (state) => state.userInfo?.role || '普通用户'
  },
  
  actions: {
    // 登录
    async login(credentials) {
      try {
        console.log('开始登录，用户名:', credentials.username)
        const response = await login(credentials)
        console.log('登录API响应:', response)
        
        // 用户登录成功
        if (response && response.success) {
          // 在会话认证中，token可能是从cookie中获取而不是响应中
          // 设置一个标识符，表示已登录
          const token = response.token || 'session_authenticated'
          this.token = token
          this.userInfo = response.user || {}
          this.isAuthenticated = true
          
          // 存储到本地
          localStorage.setItem('token', token)
          localStorage.setItem('userInfo', JSON.stringify(this.userInfo))
          
          console.log('登录成功，用户信息:', this.userInfo)
          return { success: true }
        }
        
        // 登录失败
        console.error('登录失败，响应:', response)
        return { 
          success: false, 
          message: response?.message || '登录失败，用户名或密码错误' 
        }
      } catch (error) {
        console.error('登录过程中发生错误:', error)
        
        // 详细记录错误信息
        if (error.response) {
          console.error('错误状态码:', error.response.status)
          console.error('错误数据:', error.response.data)
          
          // 针对403错误提供更具体的提示
          if (error.response.status === 403) {
            return { 
              success: false, 
              message: '账户无权限或未激活，请联系管理员' 
            }
          }
        }
        
        return { 
          success: false, 
          message: error.response?.data?.message || error.message || '登录过程中发生错误' 
        }
      }
    },
    
    // 登出
    async logout() {
      try {
        await logout()
      } catch (error) {
        console.error('注销时发生错误:', error)
      } finally {
        // 清除状态和本地存储
        this.resetState()
      }
    },
    
    // 获取用户信息
    async getUserInfo() {
      try {
        const response = await getUserProfile()
        this.userInfo = response
        localStorage.setItem('userInfo', JSON.stringify(response))
        return response
      } catch (error) {
        console.error('获取用户信息时发生错误:', error)
        return null
      }
    },
    
    // 重置状态
    resetState() {
      this.token = ''
      this.userInfo = {}
      this.isAuthenticated = false
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
    }
  }
}) 