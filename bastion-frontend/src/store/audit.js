import { defineStore } from 'pinia'
import { getAuditLogs, getAuditRules, getAuditRuleById, createAuditRule, updateAuditRule, deleteAuditRule, getAuditResults } from '../api/audit'

export const useAuditStore = defineStore('audit', {
  state: () => ({
    auditLogs: [],
    auditRules: [],
    currentRule: null,
    auditResults: [],
    loading: false,
    error: null,
    totalLogs: 0
  }),
  
  getters: {
    // 获取审计日志列表
    getAuditLogList: (state) => state.auditLogs,
    
    // 获取审计规则列表
    getAuditRuleList: (state) => state.auditRules,
    
    // 获取当前审计规则
    getCurrentRule: (state) => state.currentRule,
    
    // 获取审计结果列表
    getAuditResultList: (state) => state.auditResults
  },
  
  actions: {
    // 加载审计日志
    async loadAuditLogs(params = {}) {
      this.loading = true
      this.error = null
      
      try {
        const response = await getAuditLogs(params)
        this.auditLogs = response.items || []
        this.totalLogs = response.total || 0
        return response
      } catch (error) {
        this.error = error.message || '获取审计日志失败'
        console.error('获取审计日志时发生错误:', error)
        return { total: 0, items: [] }
      } finally {
        this.loading = false
      }
    },
    
    // 加载审计规则
    async loadAuditRules(params = {}) {
      this.loading = true
      this.error = null
      
      try {
        const response = await getAuditRules(params)
        this.auditRules = response || []
        return response
      } catch (error) {
        this.error = error.message || '获取审计规则失败'
        console.error('获取审计规则时发生错误:', error)
        return []
      } finally {
        this.loading = false
      }
    },
    
    // 获取审计规则详情
    async getAuditRuleDetail(id) {
      this.loading = true
      this.error = null
      
      try {
        const response = await getAuditRuleById(id)
        this.currentRule = response
        return response
      } catch (error) {
        this.error = error.message || '获取审计规则详情失败'
        console.error('获取审计规则详情时发生错误:', error)
        return null
      } finally {
        this.loading = false
      }
    },
    
    // 创建审计规则
    async createAuditRule(ruleData) {
      this.loading = true
      this.error = null
      
      try {
        const response = await createAuditRule(ruleData)
        if (response.success) {
          // 刷新审计规则列表
          await this.loadAuditRules()
        }
        return response
      } catch (error) {
        this.error = error.message || '创建审计规则失败'
        console.error('创建审计规则时发生错误:', error)
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },
    
    // 更新审计规则
    async updateAuditRule(id, ruleData) {
      this.loading = true
      this.error = null
      
      try {
        const response = await updateAuditRule(id, ruleData)
        if (response.success) {
          // 刷新审计规则列表和当前规则
          await this.loadAuditRules()
          if (this.currentRule && this.currentRule.id === id) {
            await this.getAuditRuleDetail(id)
          }
        }
        return response
      } catch (error) {
        this.error = error.message || '更新审计规则失败'
        console.error('更新审计规则时发生错误:', error)
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },
    
    // 删除审计规则
    async deleteAuditRule(id) {
      this.loading = true
      this.error = null
      
      try {
        const response = await deleteAuditRule(id)
        if (response.success) {
          // 刷新审计规则列表
          await this.loadAuditRules()
          // 如果当前选中的是被删除的规则，清空当前规则
          if (this.currentRule && this.currentRule.id === id) {
            this.currentRule = null
          }
        }
        return response
      } catch (error) {
        this.error = error.message || '删除审计规则失败'
        console.error('删除审计规则时发生错误:', error)
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },
    
    // 获取查询的审计结果
    async loadAuditResults(queryId) {
      this.loading = true
      this.error = null
      
      try {
        const response = await getAuditResults(queryId)
        this.auditResults = response || []
        return response
      } catch (error) {
        this.error = error.message || '获取审计结果失败'
        console.error('获取审计结果时发生错误:', error)
        return []
      } finally {
        this.loading = false
      }
    },
    
    // 设置当前审计规则
    setCurrentRule(rule) {
      this.currentRule = rule
    },
    
    // 清空当前审计规则
    clearCurrentRule() {
      this.currentRule = null
    }
  }
}) 