import { defineStore } from 'pinia'
import { executeQuery, getQueryHistory, getQueryDetail, getSavedQueries, getSavedQueryById, saveQuery, updateSavedQuery, deleteSavedQuery } from '../api/query'

export const useQueryStore = defineStore('query', {
  state: () => ({
    queryHistory: [],
    savedQueries: [],
    currentQuery: null,
    currentResult: null,
    loading: false,
    error: null
  }),
  
  getters: {
    // 获取查询历史
    getQueryHistoryList: (state) => state.queryHistory,
    
    // 获取保存的查询列表
    getSavedQueryList: (state) => state.savedQueries,
    
    // 获取当前查询结果
    getCurrentResult: (state) => state.currentResult
  },
  
  actions: {
    // 执行SQL查询
    async executeQuery(queryData) {
      this.loading = true
      this.error = null
      
      try {
        const response = await executeQuery(queryData)
        this.currentResult = response
        
        // 执行成功后刷新查询历史
        if (response.success) {
          await this.loadQueryHistory()
        }
        
        return response
      } catch (error) {
        this.error = error.message || '执行查询失败'
        console.error('执行查询时发生错误:', error)
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },
    
    // 加载查询历史
    async loadQueryHistory(params = {}) {
      this.loading = true
      this.error = null
      
      try {
        const response = await getQueryHistory(params)
        this.queryHistory = response.items || []
        return response
      } catch (error) {
        this.error = error.message || '获取查询历史失败'
        console.error('获取查询历史时发生错误:', error)
        return { total: 0, items: [] }
      } finally {
        this.loading = false
      }
    },
    
    // 获取查询详情
    async getQueryDetail(id) {
      this.loading = true
      this.error = null
      
      try {
        const response = await getQueryDetail(id)
        this.currentQuery = response
        return response
      } catch (error) {
        this.error = error.message || '获取查询详情失败'
        console.error('获取查询详情时发生错误:', error)
        return null
      } finally {
        this.loading = false
      }
    },
    
    // 加载保存的查询列表
    async loadSavedQueries(params = {}) {
      this.loading = true
      this.error = null
      
      try {
        const response = await getSavedQueries(params)
        this.savedQueries = response || []
        return response
      } catch (error) {
        this.error = error.message || '获取保存的查询列表失败'
        console.error('获取保存的查询列表时发生错误:', error)
        return []
      } finally {
        this.loading = false
      }
    },
    
    // 获取保存的查询详情
    async getSavedQueryDetail(id) {
      this.loading = true
      this.error = null
      
      try {
        const response = await getSavedQueryById(id)
        return response
      } catch (error) {
        this.error = error.message || '获取保存的查询详情失败'
        console.error('获取保存的查询详情时发生错误:', error)
        return null
      } finally {
        this.loading = false
      }
    },
    
    // 保存查询
    async saveQuery(queryData) {
      this.loading = true
      this.error = null
      
      try {
        const response = await saveQuery(queryData)
        if (response.success) {
          // 刷新保存的查询列表
          await this.loadSavedQueries()
        }
        return response
      } catch (error) {
        this.error = error.message || '保存查询失败'
        console.error('保存查询时发生错误:', error)
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },
    
    // 更新保存的查询
    async updateSavedQuery(id, queryData) {
      this.loading = true
      this.error = null
      
      try {
        const response = await updateSavedQuery(id, queryData)
        if (response.success) {
          // 刷新保存的查询列表
          await this.loadSavedQueries()
        }
        return response
      } catch (error) {
        this.error = error.message || '更新查询失败'
        console.error('更新查询时发生错误:', error)
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },
    
    // 删除保存的查询
    async deleteSavedQuery(id) {
      this.loading = true
      this.error = null
      
      try {
        const response = await deleteSavedQuery(id)
        if (response.success) {
          // 刷新保存的查询列表
          await this.loadSavedQueries()
        }
        return response
      } catch (error) {
        this.error = error.message || '删除查询失败'
        console.error('删除查询时发生错误:', error)
        return { success: false, message: this.error }
      } finally {
        this.loading = false
      }
    },
    
    // 清空当前查询结果
    clearCurrentResult() {
      this.currentResult = null
    }
  }
}) 