<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item
        v-for="(item, index) in breadcrumbs"
        :key="item.path"
      >
        <span 
          v-if="index === breadcrumbs.length - 1" 
          class="no-redirect"
        >{{ item.meta.title }}</span>
        <a v-else @click.prevent="handleLink(item)">{{ item.meta.title }}</a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 面包屑项目
const breadcrumbs = ref([])

// 过滤不需要显示的路由
const getBreadcrumb = () => {
  // 如果当前路由不需要显示面包屑，则返回
  if (route.meta?.hideBreadcrumb) {
    breadcrumbs.value = []
    return
  }
  
  // 生成层级面包屑
  const matched = route.matched.filter(
    item => item.meta && item.meta.title && !item.meta.hideBreadcrumb
  )
  
  // 处理首页
  const first = matched[0]
  if (first && first.path !== '/dashboard') {
    matched.unshift({
      path: '/dashboard',
      meta: { title: '首页' }
    })
  }
  
  breadcrumbs.value = matched
}

// 处理面包屑导航点击
const handleLink = (item) => {
  const { path } = item
  router.push(path)
}

// 监听路由变化，更新面包屑
watch(
  () => route.path,
  () => {
    getBreadcrumb()
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.app-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 64px;
  margin-left: 8px;
  
  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
  
  a {
    color: #666;
    font-weight: normal;
    
    &:hover {
      color: #409EFF;
    }
  }
}

.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all 0.5s;
}

.breadcrumb-enter-from,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-leave-active {
  position: absolute;
}
</style> 