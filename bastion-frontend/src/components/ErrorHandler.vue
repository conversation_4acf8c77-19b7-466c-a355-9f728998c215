<template>
    <div class="error-handler-container" v-if="visible">
      <el-alert
        :title="title"
        :type="type"
        :description="description"
        :closable="closable"
        show-icon
      >
        <template #default v-if="showHelp">
          <div class="error-details">
            <p class="error-help-title">可能的解决方法:</p>
            <ul class="error-help-list">
              <li v-for="(item, index) in helpItems" :key="index">{{ item }}</li>
            </ul>
            
            <div class="error-technical-details" v-if="showTechnicalDetails">
              <p class="error-tech-title">技术细节 <el-button size="small" @click="toggleTechDetails">{{ techDetailsVisible ? '隐藏' : '显示' }}</el-button></p>
              <div class="tech-details-content" v-show="techDetailsVisible">
                <pre>{{ technicalDetails }}</pre>
              </div>
            </div>
            
            <div class="error-actions" v-if="actions.length > 0">
              <el-button 
                v-for="(action, index) in actions" 
                :key="index"
                :type="action.type || 'default'"
                :size="action.size || 'default'"
                @click="handleAction(action)"
              >
                {{ action.label }}
              </el-button>
            </div>
          </div>
        </template>
      </el-alert>
    </div>
  </template>
  
  <script setup>
  import { ref, computed } from 'vue';
  
  const props = defineProps({
    error: { type: [Object, String], default: null },
    title: { type: String, default: '发生错误' },
    type: { type: String, default: 'error' },
    description: { type: String, default: '' },
    visible: { type: Boolean, default: true },
    closable: { type: Boolean, default: true },
    showHelp: { type: Boolean, default: true },
    helpItems: { 
      type: Array, 
      default: () => [
        '刷新页面后重试',
        '检查网络连接',
        '联系系统管理员获取帮助'
      ]
    },
    showTechnicalDetails: { type: Boolean, default: false },
    technicalDetails: { type: String, default: '' },
    actions: { 
      type: Array, 
      default: () => [
        { label: '刷新页面', type: 'primary', action: 'reload' }
      ]
    }
  });
  
  const emit = defineEmits(['action', 'close']);
  
  const techDetailsVisible = ref(false);
  
  const toggleTechDetails = () => {
    techDetailsVisible.value = !techDetailsVisible.value;
  };
  
  const handleAction = (action) => {
    if (action.action === 'reload') {
      window.location.reload();
    } else {
      emit('action', action);
    }
  };
  
  // 根据错误类型自动生成技术细节
  const errorDetails = computed(() => {
    if (!props.error) return '';
    
    if (typeof props.error === 'string') {
      return props.error;
    }
    
    if (props.error.message) {
      let details = `错误: ${props.error.message}\n`;
      
      if (props.error.name) {
        details += `类型: ${props.error.name}\n`;
      }
      
      if (props.error.code) {
        details += `代码: ${props.error.code}\n`;
      }
      
      if (props.error.response) {
        details += `状态码: ${props.error.response.status}\n`;
        
        if (props.error.response.data) {
          if (typeof props.error.response.data === 'string') {
            details += `响应数据: ${props.error.response.data}\n`;
          } else {
            details += `响应数据: ${JSON.stringify(props.error.response.data, null, 2)}\n`;
          }
        }
      }
      
      if (props.error.stack) {
        details += `\n堆栈:\n${props.error.stack}`;
      }
      
      return details;
    }
    
    return JSON.stringify(props.error, null, 2);
  });
  </script>
  
  <style scoped>
  .error-handler-container {
    margin: 10px 0;
  }
  
  .error-details {
    margin-top: 10px;
    border-top: 1px solid rgba(255, 220, 200, 0.3);
    padding-top: 10px;
  }
  
  .error-help-title {
    font-weight: bold;
    margin: 0 0 8px 0;
  }
  
  .error-help-list {
    margin: 0;
    padding-left: 20px;
  }
  
  .error-help-list li {
    margin-bottom: 5px;
  }
  
  .error-technical-details {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px dashed rgba(0, 0, 0, 0.1);
  }
  
  .error-tech-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0 8px 0;
    font-weight: bold;
  }
  
  .tech-details-content {
    background-color: rgba(0, 0, 0, 0.03);
    padding: 10px;
    border-radius: 4px;
    overflow: auto;
    max-height: 300px;
  }
  
  .tech-details-content pre {
    margin: 0;
    white-space: pre-wrap;
    font-family: monospace;
    font-size: 12px;
  }
  
  .error-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
  }
  </style>