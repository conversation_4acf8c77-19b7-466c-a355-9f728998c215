import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from '../layout/MainLayout.vue'

// 路由配置
const routes = [
  {
    path: '/',
    redirect: '/query'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: { requiresAuth: false, hideBreadcrumb: true }
  },
  {
    path: '/',
    component: MainLayout,
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('../views/dashboard/index.vue'),
        meta: { requiresAuth: true, title: '仪表盘', isAdmin: true }
      },
      {
        path: 'database',
        name: 'Database',
        component: () => import('../views/database/index.vue'),
        meta: { requiresAuth: true, title: '数据库管理' }
      },
      {
        path: 'datasource/list',
        redirect: '/database',
        meta: { requiresAuth: true }
      },
      {
        path: 'datasource/add',
        redirect: '/database',
        meta: { requiresAuth: true, isAdmin: true }
      },
      {
        path: 'datasource',
        redirect: '/database',
        meta: { requiresAuth: true }
      },
      {
        path: 'query',
        name: 'Query',
        component: () => import('../views/query/index.vue'),
        meta: { requiresAuth: true, title: 'SQL查询' }
      },
      // {
      //   path: 'query/debug',
      //   name: 'QueryDebug',
      //   component: () => import('../views/query/debug.vue'),
      //   meta: { requiresAuth: true, title: 'API调试', isAdmin: true }
      // },
      {
        path: 'saved',
        name: 'SavedQueries',
        component: () => import('../views/saved/index.vue'),
        meta: { requiresAuth: true, title: '保存的查询' }
      },
      {
        path: 'history',
        redirect: '/audit/sql-logs',
        meta: { requiresAuth: true, isAdmin: true }
      },
      {
        path: 'audit/logs',
        component: () => import('@/views/audit/logs.vue'),
        name: 'AuditLogs',
        meta: { requiresAuth: true, title: '操作审计', icon: 'audit', isAdmin: true }
      },
      {
        path: 'audit/sql-logs',
        component: () => import('@/views/audit/sqlLogs.vue'),
        name: 'SqlAuditLogs',
        meta: { requiresAuth: true, title: 'SQL审计', icon: 'sql-audit', isAdmin: true }
      },
      {
        path: 'audit/rules',
        name: 'AuditRules',
        component: () => import('../views/audit/rules.vue'),
        meta: { requiresAuth: true, title: '审计规则', isAdmin: true }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('../views/profile/index.vue'),
        meta: { requiresAuth: true, title: '个人设置' }
      },
      {
        path: 'permission',
        name: 'Permission',
        component: () => import('@/views/permission/index.vue'),
        meta: { 
          title: '权限管理',
          requiresAuth: true,
          isAdmin: true  // 只有管理员可以访问
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue'),
    meta: { hideBreadcrumb: true }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 检查是否是登录页面
  if (to.path === '/login') {
    next()
    return
  }

  const token = localStorage.getItem('token')
  const userInfoStr = localStorage.getItem('userInfo')
  const lastActiveTime = localStorage.getItem('lastActiveTime')
  let userInfo = {}
  
  // 检查 token 是否过期（30分钟）
  const tokenExpired = lastActiveTime && (Date.now() - parseInt(lastActiveTime)) > 30 * 60 * 1000
  
  // 如果 token 过期，清除所有状态并重定向到登录页
  if (tokenExpired) {
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    localStorage.removeItem('lastActiveTime')
    sessionStorage.clear()
    
    // 重置 store
    try {
      const { useUserStore } = require('@/store/user')
      const userStore = useUserStore()
      if (userStore) {
        userStore.resetState()
      }
    } catch (e) {
      console.warn('重置用户 store 失败:', e)
    }
    
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
    return
  }
  
  // 更新最后活动时间
  if (token) {
    localStorage.setItem('lastActiveTime', Date.now().toString())
  }
  
  // 尝试解析用户信息
  try {
    userInfo = userInfoStr ? JSON.parse(userInfoStr) : {}
  } catch (e) {
    console.error('解析用户信息失败:', e)
  }
  
  // 判断是否为管理员
  const isAdmin = userInfo.is_superuser || userInfo.is_staff || false
  
  console.log('路由守卫 - 当前路由:', to.path)
  console.log('路由守卫 - 来源路由:', from.path)
  console.log('路由守卫 - 用户信息:', userInfo)
  console.log('路由守卫 - 是否管理员:', isAdmin)
  console.log('路由守卫 - 路由元信息:', to.meta)
  
  if (to.meta.requiresAuth && !token) {
    // 需要登录但未登录，重定向到登录页
    console.log('需要登录但未登录，重定向到登录页')
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
  } else if (to.meta.isAdmin && !isAdmin) {
    // 需要管理员权限但不是管理员，重定向到查询页面
    console.log('需要管理员权限但不是管理员，重定向到查询页面')
    
    next({ path: '/query' })
  } else {
    // 专门处理SQL查询页面离开的路由守卫
    if (from.path === '/query') {
      console.log('[router.beforeEach] 即将离开SQL查询页面，执行清理...');
      
      // 1. 清理全局组件状态
      if (window.clearTimeout) {
        for (let i = 1; i < 10000; i++) {
          window.clearTimeout(i);
        }
      }
      
      // 2. 清理事件监听器引用
      try {
        document.removeEventListener('click', null);
        document.removeEventListener('mousemove', null);
        document.removeEventListener('mouseup', null);
      } catch (e) {
        console.warn('[router.beforeEach] 清理事件监听器时出错:', e);
      }
      
      // 3. 尝试调用全局清理函数（如果存在）
      if (window.cleanupBeforeNavigationFromQuery && typeof window.cleanupBeforeNavigationFromQuery === 'function') {
        try {
          window.cleanupBeforeNavigationFromQuery();
          console.log('[router.beforeEach] SQL查询页面清理函数执行完毕');
          // 删除引用，防止内存泄漏
          window.cleanupBeforeNavigationFromQuery = null;
        } catch (error) {
          console.error('[router.beforeEach] SQL查询页面清理函数执行出错:', error);
          window.cleanupBeforeNavigationFromQuery = null;
        }
      }
      
      // 4. 清理localStorage中可能存在的临时状态
      try {
        localStorage.removeItem('temp_sql_query_state');
      } catch (e) {}
      
      console.log('[router.beforeEach] SQL查询页面清理完毕，继续导航到:', to.path);
      
      // 直接继续路由，不再延迟
      next();
    } else {
      // 正常访问
      console.log('[router.beforeEach] 正常导航:', from.path, '->', to.path);
      next()
    }
  }
})

export default router
