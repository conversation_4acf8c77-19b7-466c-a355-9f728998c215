<script setup>
// 主应用组件
import { onMounted } from 'vue'
import { useUserStore } from './store/user'

const userStore = useUserStore()

// 在应用启动时加载用户信息
onMounted(async () => {
  console.log('App.vue 初始化，当前用户信息:', userStore.userInfo)
  if (!userStore.userInfo?.id) {
    console.log('App.vue 尝试获取用户信息')
    await userStore.getUserInfo()
    console.log('App.vue 获取用户信息完成:', userStore.userInfo)
  }
})
</script>

<template>
  <router-view />
</template>

<style>
/* 全局样式已移至assets/css/main.css */
</style>
