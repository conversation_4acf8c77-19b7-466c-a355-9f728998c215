# 编辑流程测试指南

## 测试步骤

### 1. 测试初始状态
- [ ] 执行查询：`select * from kangjian where craftsman_name = '丁丽娜';`
- [ ] 确认查询结果显示，但**不显示**编辑按钮组
- [ ] 确认表格处于只读模式

### 2. 测试开启编辑模式
- [ ] 点击"开启编辑"按钮
- [ ] 确认显示编辑按钮组：提交修改、新增、删除、取消编辑
- [ ] 确认表格进入编辑模式

### 3. 测试新增功能
- [ ] 点击"新增"按钮
- [ ] 确认在表格第一行插入一行空数据
- [ ] 确认空字段显示"(点击编辑)"占位符
- [ ] 确认显示"(1项待提交)"
- [ ] 双击单元格，确认可以编辑

### 4. 测试删除功能
- [ ] 勾选一行或多行数据
- [ ] 确认删除按钮变为可点击状态（不再是灰色）
- [ ] 点击删除按钮
- [ ] 确认选中行被删除
- [ ] 确认待提交项数量更新

### 5. 测试提交更改
- [ ] 点击"提交修改"按钮
- [ ] 确认显示SQL确认对话框
- [ ] 确认显示正确的INSERT/DELETE语句
- [ ] 确认显示正确的回滚语句
- [ ] 点击确认提交

### 6. 测试取消编辑
- [ ] 进行一些修改（新增/删除）
- [ ] 点击"取消编辑"按钮
- [ ] 确认回到只读模式
- [ ] 确认所有未提交的修改被丢弃

## 预期结果

### 新增功能
- INSERT语句：`INSERT INTO kangjian (column1, column2, ...) VALUES (value1, value2, ...);`
- 回滚语句：`DELETE FROM kangjian WHERE condition;`

### 删除功能
- DELETE语句：`DELETE FROM kangjian WHERE condition;`
- 回滚语句：`INSERT INTO kangjian (column1, column2, ...) VALUES (original_value1, original_value2, ...);`

## 注意事项

1. 默认状态应该始终是只读模式
2. 编辑按钮组只在编辑模式下显示
3. 新增行应该在表格顶部显示
4. 删除按钮只在有选中行时才可点击
5. 所有操作都应该正确更新待提交项计数
